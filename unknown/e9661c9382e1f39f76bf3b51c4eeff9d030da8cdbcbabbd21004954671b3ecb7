/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ObjectId } from "bson";
import Initiative, { InitiativePlain, InitiativeTypes } from "../../models/initiative";
import InitiativeGroup from "../../models/initiativeGroup";
import { getIndustryText, getSectorText } from "../reporting/FrameworkMapping";
import { RequesterType } from "../../models/dataShare";
import { DataShareMin, DataShareRepository, getDataShareRepository } from "../../repository/DataShareRepository";
import { DateRangeType, SurveyRepository } from "../../repository/SurveyRepository";
import { ExchangeSurvey, PortfolioCompany, portfolioCompanyProjection, PortfolioService } from "./PortfolioService";
import { UserInitiativeRepository } from "../../repository/UserInitiativeRepository";
import { DataPeriods } from "../utr/constants";
import { naturalSort } from "../../util/string";

interface CompaniesRecursive {
  maxRecursion: number;
  dateRange?: DateRangeType;
  period?: DataPeriods;
  metrics?: string[];
  portfolio: InitiativePlain;
}
interface CompaniesExchange {
  dateRange?: DateRangeType;
  period?: DataPeriods;
  metrics?: string[];
  portfolio: InitiativePlain;
}
export class PortfolioCompaniesService {
  constructor(private dataShareRepo: DataShareRepository) {}


  public async getCompaniesPtExchange({ dateRange, period, metrics = [], portfolio }: CompaniesExchange) {
    const companies: Map<string, PortfolioCompany & { requestedDataShares?: DataShareMin[] }> = new Map();

    const customMetricGroups = metrics.length > 0 ?
      await PortfolioService.getPortfolioMetricGroups(portfolio, metrics) :
      [];

    const groups = await InitiativeGroup.findById(portfolio.initiativeGroupId).lean();
    if (!groups) {
      return
    }
    const ids = groups.group.map(g => g.initiativeId)
    const initiatives: PortfolioCompany[] = await Initiative.find({ _id: { $in: ids } }, portfolioCompanyProjection).lean();

    const initiativeLatestSurveyMap = await SurveyRepository.getLatestSurveyByInitiativeIds({
      initiativeIds: ids,
      startDate: dateRange?.startDate,
      endDate: dateRange?.endDate,
      period,
    });

    const exchangeSurveys = await SurveyRepository.getPortfolioExchangeSurveyData({
      initiativeIds: ids,
      startDate: dateRange?.startDate,
      endDate: dateRange?.endDate,
      period,
      metrics,
      customMetricGroups
    }) as Record<string, ExchangeSurvey[]>;

    initiatives.forEach(initiative => {
      const initiativeId = initiative._id.toString()
      initiative.sectorText = getSectorText(initiative.industry);
      initiative.industryText = getIndustryText(initiative.industry);
      initiative.exchangeSurveys = exchangeSurveys[initiativeId];
      initiative.latestSurvey = initiativeLatestSurveyMap.get(initiativeId);
      companies.set(initiativeId, initiative);
    })
    await this.populateRequestedDataShares(companies, portfolio);

    return Array.from(companies.values()).sort((a, b) => naturalSort(a.name, b.name))
  }

  /**
   * Abstract duplicate logic.
   * Does not return anything as it modifies the original map object
   */
  private async populateRequestedDataShares(
    companies: Map<string, PortfolioCompany & { requestedDataShares?: DataShareMin[] }>,
    portfolio: Pick<InitiativePlain, '_id'>
  ): Promise<void> {
    const lookupIds = Array.from(companies.keys()).map(id => new ObjectId(id));
    const dataShare = await this.dataShareRepo.findRequesterDataShare({
      requesterType: RequesterType.Portfolio,
      requesterId: portfolio._id,
      initiativeId: { $in: lookupIds },
    });

    dataShare.forEach(share => {
      const company = companies.get(String(share.initiativeId));
      if (company) {
        company.requestedDataShares = company.requestedDataShares ? [...company.requestedDataShares, share] : [share];
      }
    });
  }

  public async getCompaniesRecursive({ maxRecursion, portfolio }: CompaniesRecursive) {
    const initiativeId = String(portfolio._id);
    const companies: Map<string, PortfolioCompany & { requestedDataShares?: DataShareMin[] }> = new Map();


    const recursiveInitiativeGroup = async (id: string, depth: number = 1) => {
      const initiative: PortfolioCompany | null = await Initiative.findById(id, portfolioCompanyProjection).lean();
      if (!initiative) {
        return;
      }

      if (initiative.type === InitiativeTypes.Initiative) {
        const iid = String(initiative._id);
        if (companies.has(iid)) {
          return;
        }
        initiative.sectorText = getSectorText(initiative.industry);
        initiative.industryText = getIndustryText(initiative.industry);

        const companyStatusFields = await UserInitiativeRepository.getCompanyStatusFields([initiative._id]);

        return companies.set(iid,
          {
            ...initiative,
            ...companyStatusFields[iid]
          }
        );
      }
      if (depth > maxRecursion) {
        return;
      }
      const group = await InitiativeGroup.findById(initiative.initiativeGroupId).lean();
      if (group) {
        for (const g of group.group) {
          await recursiveInitiativeGroup(String(g.initiativeId), depth + 1);
        }
      }
    }

    await recursiveInitiativeGroup(initiativeId);

    await this.populateRequestedDataShares(companies, portfolio);

    return Array.from(companies.values()).sort((a, b) => naturalSort(a.name, b.name));
  }
}

let instance: PortfolioCompaniesService;
export const getPortfolioCompaniesService = () => {
  if (!instance) {
    instance = new PortfolioCompaniesService(
      getDataShareRepository()
    );
  }
  return instance;
}
