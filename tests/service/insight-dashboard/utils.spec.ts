import { expect } from 'chai';
import {
  DashboardSurveyType,
  Filters,
  InsightDashboardType,
  PrivacyFilter,
  SurveyFilter,
  TimeFrameType,
  UtrvFilter,
} from '../../../server/models/insightDashboard';
import { SurveyType } from '../../../server/models/survey';
import { DataPeriods } from '../../../server/service/utr/constants';
import {
  AdditionalQueryOptions,
  StaticDashboardType,
  dashboards,
  getDashboardByAppConfigCode,
  getDashboardTypeFromParams,
  getOrdinate,
  getUtrvFiltersFromDashboardFilters,
  isESGDashboard,
  toPreloadOptions,
} from '../../../server/service/insight-dashboard/utils';
import { AppCode } from '../../../server/service/app/AppConfig';
import { ObjectId } from 'bson';
import { ctStarterDashboards } from '../../../server/service/insight-dashboard/summary/ctStarterDashboards';
import { randomInt } from 'mathjs';
import { COLUMNS } from '../../../server/service/insight-dashboard/constants';

describe('getUtrvFiltersFromDashboardFilters func', () => {
  const dateRange = { startDate: new Date().toISOString(), endDate: new Date().toISOString() };
  const fixedStartDate = new Date('2023-01-01T00:00:00.000Z');
  const fixedEndDate = new Date('2023-03-31T23:59:59.999Z');
  const testCases = [
    {
      describe: 'should return correct filters when all filters are provided',
      input: {
        filters: {
          utrv: UtrvFilter.Assured,
          survey: SurveyFilter.Completed,
          privacy: PrivacyFilter.Public,
          surveyType: SurveyType.Default,
          period: DataPeriods.Monthly,
          baselinesTargets: { enabled: true },
        } as Filters,
        additionalFilters: {
          period: DataPeriods.Quarterly,
          timeFrameType: TimeFrameType.SixMonths,
          dateRange: dateRange,
          surveyType: SurveyType.Aggregation,
        },
      },
      output: {
        isCompletedData: true,
        assuredOnly: true,
        isPublicOnly: true,
        showBaselines: true,
        showTargets: true,
        period: DataPeriods.Quarterly,
        dateRange: dateRange,
        surveyType: SurveyType.Aggregation,
        status: UtrvFilter.Assured,
      },
    },
    {
      describe: 'should handle missing filters gracefully',
      input: {
        filters: {
          utrv: UtrvFilter.Assured,
          survey: SurveyFilter.All,
          privacy: PrivacyFilter.All,
        } as Filters,
      },
      output: {
        isCompletedData: false,
        assuredOnly: true,
        isPublicOnly: false,
        showBaselines: false,
        showTargets: false,
        period: undefined,
        dateRange: {
          startDate: undefined,
          endDate: undefined,
        },
        surveyType: undefined,
        status: UtrvFilter.Assured,
      },
    },
    {
      describe: 'should return correct dateRange when timeFrameType is all-time',
      input: {
        filters: {
          utrv: UtrvFilter.Verified,
          survey: SurveyFilter.Completed,
          privacy: PrivacyFilter.Public,
          timeFrame: { type: TimeFrameType.SixMonths },
          surveyType: SurveyType.Default,
          period: DataPeriods.Monthly,
          baselinesTargets: { enabled: true },
        } as Filters,
        additionalFilters: {
          timeFrameType: TimeFrameType.AllTime,
        },
      },
      output: {
        isCompletedData: true,
        assuredOnly: false,
        isPublicOnly: true,
        showBaselines: true,
        showTargets: true,
        period: DataPeriods.Monthly,
        dateRange: undefined,
        surveyType: SurveyType.Default,
        status: UtrvFilter.Verified,
      },
    },
    {
      describe: 'should return correct result when filter all survey type and period',
      input: {
        filters: {
          utrv: UtrvFilter.Assured,
          survey: SurveyFilter.Completed,
          privacy: PrivacyFilter.Public,
          surveyType: SurveyType.Default,
          period: DataPeriods.Monthly,
          baselinesTargets: { enabled: true },
        } as Filters,
        additionalFilters: {
          period: 'all',
          timeFrameType: TimeFrameType.SixMonths,
          dateRange: dateRange,
          surveyType: 'all',
        } as AdditionalQueryOptions,
      },
      output: {
        isCompletedData: true,
        assuredOnly: true,
        isPublicOnly: true,
        showBaselines: true,
        showTargets: true,
        period: undefined,
        dateRange: dateRange,
        surveyType: undefined,
        status: UtrvFilter.Assured
      },
    },
    {
      describe:
        'should use timeFrame from filters when timeFrameType and dateRange is absent from additionalFilters',
      input: {
        filters: {
          utrv: UtrvFilter.Verified,
          survey: SurveyFilter.Completed,
          privacy: PrivacyFilter.Public,
          timeFrame: { type: TimeFrameType.Custom, startDate: fixedStartDate, endDate: fixedEndDate },
          surveyType: SurveyType.Default,
          period: DataPeriods.Monthly,
        } as Filters,
        additionalFilters: {
          timeFrameType: TimeFrameType.ThreeMonths,
        },
      },
      output: {
        isCompletedData: true,
        assuredOnly: false,
        isPublicOnly: true,
        showBaselines: false,
        showTargets: false,
        period: DataPeriods.Monthly,
        dateRange: { startDate: fixedStartDate.toString(), endDate: fixedEndDate.toString() },
        surveyType: SurveyType.Default,
        status: UtrvFilter.Verified,
      },
    },
    {
      describe:
        'should return undefined dateRange when no additional date/timeframe filters and timeFrameType from filters is all time',
      input: {
        filters: {
          utrv: UtrvFilter.Verified,
          survey: SurveyFilter.Completed,
          privacy: PrivacyFilter.Public,
          timeFrame: { type: TimeFrameType.AllTime },
          surveyType: SurveyType.Default,
          period: DataPeriods.Monthly,
          baselinesTargets: { enabled: true },
        } as Filters,
        additionalFilters: {},
      },
      output: {
        isCompletedData: true,
        assuredOnly: false,
        isPublicOnly: true,
        showBaselines: true,
        showTargets: true,
        period: DataPeriods.Monthly,
        dateRange: undefined,
        surveyType: SurveyType.Default,
        status: UtrvFilter.Verified,
      },
    },
    {
      describe:
        'should use timeFrame from filters when no additional date/timeframe filters and timeFrameType from filters is not all time',
      input: {
        filters: {
          utrv: UtrvFilter.Verified,
          survey: SurveyFilter.Completed,
          privacy: PrivacyFilter.Public,
          timeFrame: { startDate: fixedStartDate, endDate: fixedEndDate },
          surveyType: SurveyType.Default,
          period: DataPeriods.Monthly,
          baselinesTargets: { enabled: false },
        } as Filters,
        additionalFilters: {},
      },
      output: {
        isCompletedData: true,
        assuredOnly: false,
        isPublicOnly: true,
        showBaselines: false,
        showTargets: false,
        period: DataPeriods.Monthly,
        dateRange: { startDate: fixedStartDate.toString(), endDate: fixedEndDate.toString() },
        surveyType: SurveyType.Default,
        status: UtrvFilter.Verified,
      },
    },
  ];

  testCases.forEach(({ describe, input, output }) => {
    it(describe, () => {
      const result = getUtrvFiltersFromDashboardFilters(input);
      expect(result).to.eql(output);
    });
  });
});

describe('toPreloadOptions', () => {
  const dateRange = { startDate: new Date().toISOString(), endDate: new Date().toISOString() };
  const testCases = [
    {
      describe: 'should return correct options when all filters are provided',
      input: {
        period: DataPeriods.Monthly,
        timeFrameType: TimeFrameType.OneMonth,
        dateRange,
        surveyType: SurveyType.Default as DashboardSurveyType,
      },
      output: {
        period: DataPeriods.Monthly,
        timeFrameType: TimeFrameType.OneMonth,
        dateRange,
        surveyType: SurveyType.Default as DashboardSurveyType,
      },
    },
    {
      describe: 'should handle missing filters gracefully',
      input: {},
      output: {
        period: undefined,
        surveyType: undefined
      },
    },
  ];

  testCases.forEach(({ describe, input, output }) => {
    it(describe, () => {
      const result = toPreloadOptions(input);
      expect(result).to.eql(output);
    });
  });
});

describe('getDashboardTypeFromParams', () => {
  const testCases = [
    {
      describe: 'should return correct type if mainDownloadCode is not sgx_metrics',
      input: {
        dashboard: 'test',
        mainDownloadCode: 'test',
      },
      output: 'test',
    },
    {
      describe: 'should return correct type if mainDownloadCode is not sgx_metrics and dashboard is overview',
      input: {
        dashboard: InsightDashboardType.Overview,
        mainDownloadCode: 'test',
      },
      output: InsightDashboardType.Overview,
    },
    {
      describe: 'should return correct type if mainDownloadCode is sgx_metrics',
      input: {
        dashboard: 'test',
        mainDownloadCode: 'sgx_metrics',
      },
      output: 'sgx_test',
    },
  ];

  testCases.forEach(({ describe, input, output }) => {
    it(describe, () => {
      const result = getDashboardTypeFromParams(input);
      expect(result).to.eql(output);
    });
  });
});

describe('getDashboardByAppConfigCode', () => {
  const userIdOne = new ObjectId();
  const initiativeIdOne = new ObjectId();

  const userIdTwo = new ObjectId();
  const initiativeIdTwo = new ObjectId();

  const testCases = [
    ...[
      InsightDashboardType.Social,
      InsightDashboardType.Governance,
      InsightDashboardType.Environmental,
      InsightDashboardType.Overview,
    ].map((type) => ({
      describe: `should return ${type} starter dashboard if appConfigCode is CompanyTrackerStarter`,
      input: {
        type: type as StaticDashboardType,
        appConfigCode: AppCode.CompanyTrackerStarter,
        userId: userIdOne,
        initiativeId: initiativeIdOne,
      },
      output: {
        ...ctStarterDashboards[type],
        initiativeId: initiativeIdOne,
        creatorId: userIdOne,
      },
    })),
    ...[
      InsightDashboardType.Social,
      InsightDashboardType.Governance,
      InsightDashboardType.Environmental,
      InsightDashboardType.SgxEnvironmental,
      InsightDashboardType.SgxSocial,
      InsightDashboardType.SgxGovernance,
      InsightDashboardType.Overview,
    ].map((type) => ({
      describe: `should return ${type} dashboard if appConfigCode is not CompanyTrackerStarter`,
      input: {
        type: type as StaticDashboardType,
        appConfigCode: AppCode.CompanyTrackerEnterprise,
        userId: userIdTwo,
        initiativeId: initiativeIdTwo,
      },
      output: {
        ...dashboards[type as keyof typeof dashboards],
        initiativeId: initiativeIdTwo,
        creatorId: userIdTwo,
      },
    })),
  ];

  testCases.forEach(({ describe, input, output }) => {
    it(describe, () => {
      const { _id, ...rest } = getDashboardByAppConfigCode(input);
      expect(rest).to.eql(output);
    });
  });
});

describe('getOrdinate', () => {
  const notMaxWidth = randomInt(1, 11);
  const newItemWidth = COLUMNS - notMaxWidth;
  const itemHeight = 2;

  const testCases = [
    {
      describe: 'should handle empty items',
      items: [],
      newItemWidth: 0,
      output: { x: 0, y: 0 },
    },
    {
      describe: 'should fit item in the row if there is enough space',
      items: [
        { _id: new ObjectId(), gridSize: { x: 0, y: 0, w: randomInt(1, 11), h: itemHeight } },
        { _id: new ObjectId(), gridSize: { x: 0, y: itemHeight, w: notMaxWidth, h: itemHeight } },
      ],
      newItemWidth: newItemWidth,
      output: { x: notMaxWidth, y: 2 },
    },
    {
      describe: 'should put item in a new row if there is not enough space',
      items: [
        { _id: new ObjectId(), gridSize: { x: 0, y: 0, w: randomInt(1, 11), h: itemHeight } },
        { _id: new ObjectId(), gridSize: { x: 0, y: itemHeight, w: notMaxWidth, h: itemHeight } },
      ],
      newItemWidth: newItemWidth + 1,
      output: { x: 0, y: itemHeight + itemHeight },
    },
  ];

  testCases.forEach(({ describe, items, newItemWidth, output }) => {
    it(describe, () => {
      const result = getOrdinate(newItemWidth, items);
      expect(result).to.eql(output);
    });
  });
});

describe('isESGDashboard', () => {
  const testCases = [
    {
      describe: 'should return true if type is ESG',
      input: InsightDashboardType.Social,
      output: true,
    },
    {
      describe: 'should return false if type is not ESG',
      input: InsightDashboardType.Overview,
      output: false,
    },
  ];

  testCases.forEach(({ describe, input, output }) => {
    it(describe, () => {
      const result = isESGDashboard(input);
      expect(result).to.eql(output);
    });
  });
});
