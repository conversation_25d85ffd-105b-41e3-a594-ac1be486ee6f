import { ObjectId } from 'bson';
import { expect } from 'chai';
import { createSandbox } from 'sinon';
import ContextError from '../../../server/error/ContextError';
import MetricGroup, {
  MetricGroupPlain,
  MetricGroupSource,
  MetricGroupSourceType,
  MetricGroupType,
} from '../../../server/models/metricGroup';
import Survey, { SurveyType } from '../../../server/models/survey';
import { SurveyRepository } from '../../../server/repository/SurveyRepository';
import { SupportedJobModel } from '../../../server/service/materiality-assessment/background-job/types';
import { getMaterialityAssessmentBackgroundJobService } from '../../../server/service/materiality-assessment/MaterialityAssessmentBackgroundJobService';
import { getMaterialityAssessmentManager } from '../../../server/service/materiality-assessment/MaterialityAssessmentManager';
import { MaterialityAssessmentService } from '../../../server/service/materiality-assessment/MaterialityAssessmentService';
import { MaterialityMetricGroupService } from '../../../server/service/materiality-assessment/MaterialityMetricGroupService';
import { AssessmentType } from '../../../server/types/materiality-assessment';
import { metricGroupOne } from '../../fixtures/metricGroupFixtures';
import { surveyOne } from '../../fixtures/survey';
import { userOne } from '../../fixtures/userFixtures';
import { createMongooseModel } from '../../setup';
import { MaterialityAssessmentScope } from '../../../server/service/materiality-assessment/types';

describe('MaterialityMetricGroupService', () => {
  const materialityAssessmentManager = getMaterialityAssessmentManager();
  const backgroundJobMTService = getMaterialityAssessmentBackgroundJobService();
  const sandbox = createSandbox();
  const service = new MaterialityMetricGroupService(
    SurveyRepository,
    materialityAssessmentManager,
    backgroundJobMTService
  );

  const assessmentOne = {
    ...surveyOne,
    type: SurveyType.Materiality,
    assessmentType: AssessmentType.FinancialMateriality,
    completedDate: new Date(),
  };

  const utrsMapping = [{ _id: new ObjectId() }];

  const createScoreJob = (_id = new ObjectId()) =>
    ({ _id, tasks: [{ data: { surveyId: assessmentOne._id } }] } as SupportedJobModel);

  const createMetricGroupSource = (overrides: Partial<MetricGroupSource> = {}): MetricGroupSource => ({
    type: MetricGroupSourceType.Survey,
    surveyId: assessmentOne._id,
    jobId: new ObjectId(),
    topTopicsCount: 20,
    topicUtrs: utrsMapping,
    ...overrides,
  });

  const createMetricGroupDocument = (overrides: Partial<MetricGroupPlain> = {}) =>
    new MetricGroup({
      _id: new ObjectId(),
      type: MetricGroupType.Custom,
      source: createMetricGroupSource(),
      ...overrides,
    });

  afterEach(() => {
    sandbox.restore();
  });

  describe('generateMetricGroupUtrs', () => {
    it('should throw error if survey is not materiality', async () => {
      sandbox.stub(SurveyRepository, 'mustFindById').resolves(new Survey(surveyOne));
      await expect(
        service.generateMetricGroupUtrs({ userId: userOne._id, job: createScoreJob() })
      ).to.eventually.be.rejectedWith(ContextError, 'Survey is not a materiality survey');
    });

    it('should create metric group if not found & generate utrs', async () => {
      sandbox.stub(SurveyRepository, 'mustFindById').resolves(new Survey(assessmentOne));
      sandbox.stub(MetricGroup, 'findOne').returns(createMongooseModel(null));
      sandbox
        .stub(materialityAssessmentManager, 'getSizeScopeByAssessmentId')
        .resolves(MaterialityAssessmentScope.MidCap);
      sandbox.stub(MaterialityAssessmentService.prototype, 'getUtrMappingFromOrderedTopics').resolves(utrsMapping);
      const scoreJob = createScoreJob();
      const metricGroupDocument = await service.generateMetricGroupUtrs({ userId: userOne._id, job: scoreJob });

      const expectedSource = {
        surveyId: assessmentOne._id,
        type: 'survey',
        topTopicsCount: 20,
        topicUtrs: utrsMapping,
        jobId: scoreJob._id,
      };
      const metricGroup = metricGroupDocument.toObject();
      expect(metricGroup.groupName).to.equal('February 2019');
      expect(metricGroup.source).to.deep.equal(expectedSource);
      expect(metricGroup.universalTrackers).to.deep.equal(utrsMapping.map(({ _id }) => _id));
    });

    it('should skip regenerating utrs if already generated', async () => {
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource(),
      });

      sandbox.stub(SurveyRepository, 'mustFindById').resolves(new Survey(assessmentOne));
      sandbox.stub(MetricGroup, 'findOne').returns(createMongooseModel(metricGroupDocument));
      await service.generateMetricGroupUtrs({ userId: userOne._id, job: createScoreJob() });
      const topicLengthSpy = sandbox.spy(materialityAssessmentManager, 'getTopTopicsCount');
      expect(topicLengthSpy.called).to.be.false;
    });
  });

  describe('regenerateMetricGroupUtrs', () => {
    it('should throw error if no source survey', async () => {
      const metricGroupDocument = createMetricGroupDocument();
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      await expect(
        service.regenerateMetricGroupUtrs({ groupId: metricGroupOne._id, topTopicsCount: 5 })
      ).to.eventually.be.rejectedWith(ContextError, 'No survey found for metric group');
    });

    it('should throw error if no score job found', async () => {
      const metricGroupDocument = createMetricGroupDocument({ survey: assessmentOne });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      await expect(
        service.regenerateMetricGroupUtrs({ groupId: metricGroupOne._id, topTopicsCount: 5 })
      ).to.eventually.be.rejectedWith(ContextError, 'No score job found for metric group');
    });

    it('should skip regenerating if nothing changed', async () => {
      const jobId = new ObjectId();
      const topTopicsCount = 5;
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource({ jobId, topTopicsCount }),
        survey: assessmentOne,
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // same score job
      sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(createScoreJob(jobId));
      await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // same topic length
        topTopicsCount,
      });
      const topicLengthSpy = sandbox.spy(materialityAssessmentManager, 'getTopTopicsCount');
      expect(topicLengthSpy.called).to.be.false;
    });

    it('should regenerating if scores changed', async () => {
      const newJobId = new ObjectId();
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource(),
        survey: assessmentOne,
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // new score job
      sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(createScoreJob(newJobId));
      sandbox.stub(MaterialityAssessmentService.prototype, 'getUtrMappingFromOrderedTopics').resolves(utrsMapping);
      const updatedMetricGroupDocument = await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // same topic length
        topTopicsCount: 20,
      });
      expect(updatedMetricGroupDocument.toObject().source?.jobId).to.deep.equal(newJobId);
    });

    it('should regenerating if topic length changed', async () => {
      const jobId = new ObjectId();
      const newTopicLength = 10;
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource({ jobId }),
        survey: assessmentOne,
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // same score job
      sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(createScoreJob(jobId));
      sandbox.stub(MaterialityAssessmentService.prototype, 'getUtrMappingFromOrderedTopics').resolves(utrsMapping);
      const updatedMetricGroupDocument = await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // new topic length
        topTopicsCount: newTopicLength,
      });
      const updatedMetricGroup = updatedMetricGroupDocument.toObject();
      expect(updatedMetricGroup.source?.jobId).to.deep.equal(jobId);
      expect(updatedMetricGroup.source?.topTopicsCount).to.deep.equal(newTopicLength);
    });
  });
});
