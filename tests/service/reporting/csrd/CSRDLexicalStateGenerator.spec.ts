/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { testLogger } from "../../../factories/logger";
import { surveyOne } from "../../../fixtures/survey";
import { userOne } from "../../../fixtures/userFixtures";
import { createInitiative } from "../../../fixtures/initiativeFixtures";
import fullStateSnapshot from './full-state.snapshot.json'
import UniversalTrackerValue from "../../../../server/models/universalTrackerValue";
import { createAggregate } from "../../../setup";
import { esrs2_E3_4 } from "../../../fixtures/utr/utrTableFixtures";
import {
  CSRDLexicalStateGenerator,
  getCSRDLexicalStateGenerator
} from "../../../../server/service/reporting/csrd/CSRDLexicalStateGenerator";

describe('CSRDLexicalStateGenerator', () => {
  const sandbox = createSandbox();
  const csrdGenerator = new CSRDLexicalStateGenerator(testLogger);


  const initiative = createInitiative({
    name: 'Alpha Bravo Ltd.',
  })

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    const instance = getCSRDLexicalStateGenerator();
    expect(instance).to.be.instanceOf(CSRDLexicalStateGenerator);
  });

  it('process report and return status', async () => {
    sandbox.stub(UniversalTrackerValue, 'aggregate').returns(createAggregate([
      esrs2_E3_4
    ]));

    const state = await csrdGenerator.generateCSRDLexicalState({
      survey: surveyOne,
      initiative: initiative,
      mapping: {},
      user: userOne,
      preview: true,
    });

    expect(state).to.deep.equal(fullStateSnapshot)
  });
});
