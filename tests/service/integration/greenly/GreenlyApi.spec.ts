/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import '../../../setup';
import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { getGreenlyApi, GreenlyApi } from '../../../../server/service/integration/greenly/GreenlyApi';
import { getEmailService } from '../../../../server/service/email/EmailService';
import axios from 'axios';
import { createGreenlyFixtures } from './GreenlyApiFixtures';
import ContextError from '../../../../server/error/ContextError';
import { GreenlyCreateData } from '../../../../server/service/integration/greenly/greenlyTypes';
import { MailerResponseInterface } from '../../../../server/service/email/EmailService';

const { greenlyCompanyListItem, createEmissionData } = createGreenlyFixtures();

describe('GreenlyApi', () => {
  const sandbox = createSandbox();
  const emailService = getEmailService();
  const httpClient = axios.create();
  const api = new GreenlyApi(emailService, httpClient);

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(getGreenlyApi()).to.be.instanceOf(GreenlyApi);
  });

  describe('listCompanies', () => {
    it('should return companies list', async () => {
      const mockResponse = {
        data: {
          companies: [greenlyCompanyListItem]
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.listCompanies();
      expect(result).to.deep.equal([greenlyCompanyListItem]);
    });

    it('should handle API error', async () => {
      const error = new ContextError('API Error');
      sandbox.stub(httpClient, 'get').rejects(error);

      await expect(api.listCompanies()).to.be.rejectedWith(error);
    });
  });

  describe('getCompany', () => {
    it('should return company by id', async () => {
      const mockResponse = {
        data: {
          companies: [greenlyCompanyListItem]
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.getCompany(greenlyCompanyListItem.id);
      expect(result).to.deep.equal(greenlyCompanyListItem);
    });

    it('should throw error when company not found', async () => {
      const mockResponse = {
        data: {
          companies: []
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);
      await expect(api.getCompany('non-existent-id')).to.be.rejectedWith(ContextError);
    });
  });

  describe('createConnection', () => {
    it('should send email with connection details', async () => {
      const mockEmailResponse: MailerResponseInterface = {
        getId: () => 'email-123',
        isSuccess: () => true,
        getRawResponse: () => ({})
      };
      sandbox.stub(emailService, 'send').resolves(mockEmailResponse);

      const createData: GreenlyCreateData = {
        company: {
          _id: '123',
          name: 'Test Company',
          logo: 'logo.png',
          description: 'Test Description',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'Test State',
            country: 'Test Country',
            postalCode: '12345'
          }
        },
        user: {
          jobTitle: 'Developer',
          firstName: 'John',
          surname: 'Doe',
          email: '<EMAIL>'
        },
        additionalContext: [
          {
            name: 'Metric 1',
            value: '100',
            unit: 'kg',
            code: 'metric-1'
          }
        ]
      };

      const result = await api.createConnection(createData);

      expect(result).to.deep.equal({
        emailTransactionId: 'email-123',
        companyId: '123'
      });
    });

    it('should handle email service error', async () => {
      const error = new ContextError('Email Error');
      sandbox.stub(emailService, 'send').rejects(error);

      const createData: GreenlyCreateData = {
        company: {
          _id: '123',
          name: 'Test Company',
          logo: 'logo.png',
          description: 'Test Description',
          address: {
            street: '',
            city: '',
            state: '',
            country: '',
            postalCode: ''
          }
        },
        user: {
          jobTitle: 'Developer',
          firstName: 'John',
          surname: 'Doe',
          email: '<EMAIL>'
        },
        additionalContext: []
      };

      await expect(api.createConnection(createData)).to.be.rejectedWith(error);
    });
  });

  describe('getEmissionsDataTotal', () => {
    it('should return emissions data', async () => {
      const mockResponse = {
        data: createEmissionData()
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.getEmissionsDataTotal('company-123', 2024);
      expect(result).to.deep.equal(mockResponse.data);
    });

    it('should handle API error with context', async () => {
      const error = {
        message: 'API Error',
        response: {
          data: { error: 'Invalid request' }
        }
      };
      sandbox.stub(httpClient, 'get').rejects(error);
      await expect(api.getEmissionsDataTotal('company-123', 2024)).to.be.rejectedWith(ContextError);
    });
  });
});
