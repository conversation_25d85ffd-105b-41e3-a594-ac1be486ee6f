/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { initiativeOneSimple, initiativeStarter, SGXPortfolio } from '../../fixtures/initiativeFixtures';
import {
  PortfolioCompaniesDashboardService,
  PortfolioCompanyDashboardLookupData,
} from '../../../server/service/portfolio/PortfolioCompaniesDashboardService';
import { createSandbox, SinonStub } from 'sinon';
import { getDefaultFilters, InsightDashboard, InsightDashboardType, InsightDashboardPlain, UtrvFilter, SurveyFilter, PrivacyFilter } from '../../../server/models/insightDashboard';
import { userOne } from '../../fixtures/userFixtures';
import { DataSharePermissions } from '../../../server/service/share/DataSharePermissions';
import { DataScopeAccess, DataShareScopeView } from '../../../server/models/dataShare';
import { UniversalTrackerRepository } from '../../../server/repository/UniversalTrackerRepository';
import { ObjectId } from 'bson';
import * as utils from '../../../server/service/insight-dashboard/utils';
import { ctStarterDashboards } from '../../../server/service/insight-dashboard/summary/ctStarterDashboards';
import { getUniversalTrackerHistoricalDataManager } from '../../../server/service/utr/historical-data/UniversalTrackerHistoricalDataManager';
import { universalTrackerOne } from '../../fixtures/universalTrackerFixtures';
import { getDashboardItemManager } from '../../../server/service/insight-dashboard/DashboardItemManager';
import { getPortfolioDashboardService } from '../../../server/service/portfolio/PortfolioDashboardService';
import { createMongooseModel } from '../../setup';

describe('PortfolioCompaniesDashboardService', () => {
  const sandbox = createSandbox();

  afterEach(() => {
    sandbox.restore();
  });

  const historicalDataManager = getUniversalTrackerHistoricalDataManager();
  const portfolioDashboardService = getPortfolioDashboardService();
  const dashboardItemManager = getDashboardItemManager();
  const service = new PortfolioCompaniesDashboardService(
    historicalDataManager,
    portfolioDashboardService,
    dashboardItemManager
  );

  describe('getSummaryDashboard', () => {
    const lookupData = {
      dashboardType: InsightDashboardType.SgxEnvironmental,
      initiative: initiativeStarter,
      portfolioId: SGXPortfolio._id,
      user: userOne,
      filters: {}
    } satisfies PortfolioCompanyDashboardLookupData;

    it('should return empty dashboard with no items', async () => {
      sandbox.stub(utils, 'getDashboardByAppConfigCode').returns({
        _id: new ObjectId(),
        creatorId: userOne._id,
        initiativeId: initiativeStarter._id,
        ...ctStarterDashboards[InsightDashboardType.Environmental],
        title: '',
        filters: getDefaultFilters(),
        items: [],
        share: []
      })
      const dashboard = await service.getSummaryDashboard(lookupData);
      expect(dashboard.items).lengthOf(0);
      expect(dashboard.utrsData).to.lengthOf(0);
    });

    it('should return empty dashboard with right userId and initiativeId set', async () => {
      sandbox.stub(historicalDataManager, 'getUtrsHistoricalData').resolves([]);
      sandbox.stub(DataSharePermissions, 'getDataScopeAccess').resolves({
        access: DataScopeAccess.Full,
        scope: undefined,
        views: [],
      });
      const dashboard = await service.getSummaryDashboard(lookupData);
      expect(dashboard.creatorId.toString()).eq(userOne._id.toString())
      expect(dashboard.initiativeId.toString()).eq(initiativeStarter._id.toString())
    });

    it('should pass returned utr codes', async () => {
      sandbox.stub(DataSharePermissions, 'getDataScopeAccess').resolves({
        access: DataScopeAccess.Partial,
        scope: { standards: ['gri', 'tcfd'] },
        views: [DataShareScopeView.Insights],
      });
      const code = 'gri/2020/305-1/a';
      sandbox.stub(UniversalTrackerRepository, 'getBlueprintUtrs').resolves([
        { code: code, _id: new ObjectId() },
      ]);
      const populateStub = sandbox.stub(UniversalTrackerRepository, 'populateValueValidationByCodes').resolves([universalTrackerOne]);
      const utrStub = sandbox.stub(historicalDataManager, 'getUtrsHistoricalData').resolves([])
      const dashboard = await service.getSummaryDashboard(lookupData);
      const first = utrStub.getCall(0).firstArg;

      expect(populateStub.calledWith([code])).to.be.true;
      expect(first.utrIds).eqls([universalTrackerOne._id.toString()]);
      expect(dashboard.type).eq(lookupData.dashboardType);
    });
  });

  describe('getDefaultDashboards', () => {
    const portfolioId = new ObjectId().toString();
    let findStub: SinonStub;
    let execStub: SinonStub;

    beforeEach(() => {
      execStub = sandbox.stub();
      findStub = sandbox.stub(InsightDashboard, 'find').returns({ lean: () => ({ exec: execStub }) } as any);
    });

    it('should return dashboards if found', async () => {
      const mockDashboards = [
        { _id: new ObjectId(), title: 'Dashboard 1' },
        { _id: new ObjectId(), title: 'Dashboard 2' },
      ];
      execStub.resolves(mockDashboards);

      const dashboards = await service.getDefaultDashboards(portfolioId);

      expect(dashboards).to.deep.equal(mockDashboards);
      expect(findStub.calledOnceWith({
        initiativeId: portfolioId,
        'filters.displayAsDefault.enabled': { $eq: true },
      }, {
        _id: 1,
        title: 1,
      })).to.be.true;
    });
  });

  describe('getPortfolioCompanyInsightsDashboard', () => {
    const portfolio = initiativeOneSimple;
    const initiativeId = new ObjectId().toString();
    const dashboardId = new ObjectId().toString();
    const mockPortfolioDashboard = {
      _id: new ObjectId(dashboardId),
      initiativeId: portfolio._id,
      title: 'Test Dashboard',
      filters: {
        displayAsDefault: { enabled: true },
        utrv: UtrvFilter.Verified,
        survey: SurveyFilter.Completed,
        privacy: PrivacyFilter.Public,
      },
      items: [],
      share: [],
      creatorId: userOne._id,
      type: InsightDashboardType.Custom,
    } as InsightDashboardPlain;

    let findOneStub: SinonStub;
    let populateUtrDataStub: SinonStub;
    let populateFilesStub: SinonStub;
    let populateScorecardStub: SinonStub;

    beforeEach(() => {
      findOneStub = sandbox.stub(InsightDashboard, 'findOne').returns(createMongooseModel(mockPortfolioDashboard));
      populateUtrDataStub = sandbox
        .stub(portfolioDashboardService, 'populateUtrDataByPortfolioId')
        .resolves(mockPortfolioDashboard as any);
      populateFilesStub = sandbox.stub(dashboardItemManager, 'populateFiles').resolves(mockPortfolioDashboard as any);
      populateScorecardStub = sandbox
        .stub(portfolioDashboardService, 'populateScorecard')
        .resolves(mockPortfolioDashboard as any);
    });

    it('should return dashboard when found and processed successfully', async () => {
      const result = await service.getPortfolioCompanyInsightsDashboard({
        portfolio,
        initiativeId,
        dashboardId,
      });

      expect(findOneStub.calledOnce).to.be.true;
      expect(
        populateUtrDataStub.calledOnceWith({
          dashboard: mockPortfolioDashboard,
          portfolio,
          filters: { dataShareInitiativeId: initiativeId },
        })
      ).to.be.true;
      expect(populateFilesStub.calledOnceWith(mockPortfolioDashboard)).to.be.true;
      expect(populateScorecardStub.calledOnceWith(mockPortfolioDashboard, portfolio)).to.be.true;
      expect(result).to.deep.equal(mockPortfolioDashboard);
    });
  });
});
