import { FeatureDetails, FeatureTag, getFeatureDetails } from '@g17eco/core';
import InitiativeSettings, {
  FeatureSetting,
  FeatureSettingType,
} from '../../models/initiativeSettings';
import { ObjectId } from 'bson';

interface UpdateFeature extends Pick<FeatureDetails, 'active' | 'disabled' | 'disabledReason'> {
  tag: FeatureTag;
}

export class InitiativeSettingsService {
  public async updateFeatures(initiativeId: string, updateData: UpdateFeature[]) {
    const featuresSetting = updateData.map(({ tag, active, disabled, disabledReason }) => {
      const details = getFeatureDetails(tag);
      return {
        ...details,
        active,
        disabled,
        disabledReason,
        name: disabled ? `${details.name} (Disabled)` : details.name,
      };
    });

    return InitiativeSettings.findOneAndUpdate(
      { initiativeId: new ObjectId(initiativeId) },
      { features: featuresSetting },
      { upsert: true, new: true }
    ).exec();
  }

  public async getFeaturesSetting(initiativeId: ObjectId): Promise<FeatureSetting[]> {
    const setting = await InitiativeSettings.findOne({ initiativeId }, { features: 1 }).lean().exec();
    if (!setting || !setting.features) {
      return [];
    }
    return setting.features.map((feature) => ({ ...feature, type: FeatureSettingType.InitiativeSetting }));
  }
}

let instance: InitiativeSettingsService;
export const getInitiativeSettingsService = () => {
  if (!instance) {
    instance = new InitiativeSettingsService();
  }

  return instance;
};
