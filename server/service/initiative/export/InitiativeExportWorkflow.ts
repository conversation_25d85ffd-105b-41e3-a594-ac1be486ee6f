/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import BackgroundJob, { JobStatus, JobType, LogMessage, TaskStatus, TaskType } from '../../../models/backgroundJob';
import ContextError from '../../../error/ContextError';
import { BackgroundBaseWorkflow, TaskResult } from '../../background-process/BackgroundBaseWorkflow';
import { getInitiativeExportService, InitiativeExportService } from './InitiativeExportService';
import {
  InitiativeExportTask,
  SupportedJobModel,
  TaskInitiativeProcess,
  TaskExportSetup,
  TaskExportZip,
  WorkflowCreate,
  SurveyBundleInfo,
  CreatedJob,
  ZipContext,
  NotificationParams,
} from './types';
import { createLogEntry } from '../../../service/jobs/logMessage';
import { createTempDir, removeTmpFolder } from '../../../service/file/filesystem';
import Survey, { SurveyWithInitiative } from '../../../models/survey';
import { getReportName } from '../../../util/survey';
import Initiative from '../../../models/initiative';
import { ObjectId } from 'bson';
import path from 'path';
import { normalizeNamePath } from './utils';
import { getRootInitiativeService, RootInitiativeService } from '../../../service/organization/RootInitiativeService';
import { getNotificationService, NotificationService } from '../../../service/notification/NotificationService';
import {
  CustomAttributes,
  NotificationCategory,
  NotificationPage,
} from '../../../service/notification/NotificationTypes';
import { UrlMapper } from '../../../service/url/UrlMapper';
import { LEVEL } from '../../../service/event/Events';
import User from '../../../models/user';
import { tryGetContext } from '../../../middleware/audit/contextMiddleware';

export class InitiativeExportWorkflow extends BackgroundBaseWorkflow<SupportedJobModel> {
  constructor(
    protected logger: LoggerInterface,
    protected jobType: JobType.ExportInitiativeDataFull,
    private exportService: InitiativeExportService,
    private rootInitiativeService: RootInitiativeService,
    private notificationService: NotificationService
  ) {
    super();
  }

  public async findLatestJob(initiativeId: ObjectId) {
    const job = await BackgroundJob.findOne({ initiativeId, type: this.jobType }).sort({ created: -1 }).lean().exec();
    return { job: job as SupportedJobModel };
  }

  public async getBundleSignedUrl(jobId: ObjectId) {
    return this.exportService.getSignedUrl(jobId);
  }

  public async create(workflow: WorkflowCreate): Promise<CreatedJob> {
    const { initiativeId, userId, idempotencyKey } = workflow;

    // Ensure we are not spamming the same job again and again.
    const exists = await BackgroundJob.exists({
      type: this.jobType,
      initiativeId,
      idempotencyKey,
      status: { $in: [JobStatus.Pending, JobStatus.Processing] },
    });

    if (exists) {
      throw new ContextError(`Import job already exists for this configuration`, {
        initiativeId,
        idempotencyKey,
        jobType: this.jobType,
        existingJobId: exists._id,
      });
    }

    const key = this.getIdempotencyKey({
      id: initiativeId.toHexString(),
      idempotencyKey,
    });

    return this.exportService.createJob({ initiativeId, userId, idempotencyKey: key });
  }

  private async setupTask(job: SupportedJobModel, task: TaskExportSetup) {
    await this.startTask(job, task);

    const setupTask = job.tasks.find((t) => t.type === TaskType.ExportInitiativeSetup);
    const folderStructure = setupTask?.data.folderStructure ?? {};

    const processInitiativeTasks: TaskInitiativeProcess[] = Object.entries(folderStructure).map(
      ([_, { _id, name, path }]) =>
        this.exportService.getInitiativeProcessTask({ initiativeId: _id, name, uploadFolder: path })
    );

    const zipTask: TaskExportZip = this.exportService.getZipTask();
    const nextTasks = [...processInitiativeTasks, zipTask];

    job.tasks.push(...nextTasks);
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.logs.push(
      createLogEntry(`Completed ${task.name} processing`, {
        metadata: {
          taskId: task.id,
          nextTaskIds: nextTasks.map((t) => t.id),
        },
      })
    );

    job.markModified('tasks');
    return job.save();
  }

  private getRemotePath({ jobId, uploadFolder }: { jobId: string; uploadFolder: string }) {
    return path.join('jobs', jobId, uploadFolder);
  }

  private async exportInitiativeProcess(job: SupportedJobModel, task: TaskInitiativeProcess) {
    await this.startTask(job, task);

    const { initiativeId, uploadFolder } = task.data;

    const surveys = await Survey.find({
      initiativeId: initiativeId,
      deletedDate: { $exists: false },
    })
      .populate('initiative')
      .lean<SurveyWithInitiative[]>()
      .exec();

    const jobId = job._id.toString();
    const remotePath = this.getRemotePath({ jobId, uploadFolder });
    const localPath = await createTempDir(initiativeId.toString());

    const bundles: SurveyBundleInfo[] = [];
    for (const survey of surveys) {
      const bundle = await this.exportService.createSurveyBundle({ remotePath, localPath, survey });

      bundles.push(bundle);
      const surveyId = survey._id.toString();
      this.logger.info(`Completed exporting survey: ${getReportName(survey)}`, {
        jobId,
        surveyId,
        bundle,
      });
    }

    // clean up disk
    await removeTmpFolder(localPath);

    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.logs.push(
      createLogEntry(`Completed exporting initiative data: ${initiativeId}`, {
        metadata: {
          jobId,
          taskId: task.id,
          initiativeId,
          surveyIds: surveys.map((s) => s._id.toString()),
          bundles,
        },
      })
    );

    job.markModified('tasks');
    return job.save();
  }

  private async zipTask(job: SupportedJobModel, task: TaskExportZip) {
    await this.startTask(job, task);

    const rootInitiativeId = job.initiativeId;
    const initiative = await Initiative.findOne({ _id: rootInitiativeId }, { name: 1 }).orFail().lean().exec();
    const initiativeName = normalizeNamePath(initiative.name);

    const jobId = job._id.toString();
    const remotePath = path.join('jobs', jobId);
    const localPath = await createTempDir(jobId);

    // replace prefix path in storage to get relative path of file
    const zipResult: ZipContext = await this.exportService.uploadBundleZip({ remotePath, localPath, initiativeName });

    // clean up disk
    await removeTmpFolder(localPath);

    task.data = zipResult;
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.logs.push(
      createLogEntry(`Completed zipping initiative data: ${rootInitiativeId}`, {
        metadata: {
          jobId,
          taskId: task.id,
          initiativeId: rootInitiativeId,
        },
      })
    );

    const user = await User.findById(job.userId).orFail().lean().exec();
    await this.sendNotification({
      title: `Completed exporting full data of the initiative: ${initiative.name}`,
      content: `[${initiativeName}] Bundle file is ready to be downloaded.`,
      job,
      user,
    });

    job.markModified('tasks');
    return job.save();
  }

  private async sendNotification({ job, user, title, content }: NotificationParams): Promise<LogMessage> {
    try {
      const initiative = await Initiative.findById(job.initiativeId).orFail().exec();
      const org = await this.rootInitiativeService.getOrganization(initiative);

      const customAttributes: CustomAttributes = {
        orgId: org?._id.toString(),
        appConfigCode: org?.appConfigCode,
        page: NotificationPage.CompanySettingsAccountManagement,
        domain: job.attributes.find((attr) => attr.name === 'domain')?.value || tryGetContext()?.origin,
        initiativeId: String(job.initiativeId),
        jobId: String(job._id),
      };

      const notificationId = new ObjectId();
      await this.notificationService.createNotification({
        _id: notificationId,
        title,
        content,
        category: NotificationCategory.Announcements,
        topic: `export_initiative_data_full`,
        customAttributes: customAttributes,
        actionUrl: UrlMapper.notificationUrl(customAttributes),
        recipients: [{ id: String(user._id) }],
      });

      return createLogEntry(`Finish export initiative data full for initiativeId: ${String(job.initiativeId)}`, {
        metadata: {
          notificationId,
          userId: user._id.toHexString(),
          jobId: String(job.initiativeId),
        },
      });
    } catch (e) {
      return createLogEntry(`Failed to create export initiative data full notification`, {
        severity: LEVEL.ERROR,
        metadata: {
          userId: user._id.toHexString(),
          jobId: String(job.initiativeId),
          errorMessage: e.message,
        },
      });
    }
  }

  public async processTask(job: SupportedJobModel, task: InitiativeExportTask): Promise<TaskResult<SupportedJobModel>> {
    switch (task.type) {
      case TaskType.ExportInitiativeSetup:
        return {
          job: await this.setupTask(job, task),
          // Allow to go to next job straight away in the same process
          executeNextTask: true,
        };
      case TaskType.ExportInitiativeProcess:
        return {
          job: await this.exportInitiativeProcess(job, task),
          executeNextTask: true,
        };
      case TaskType.ExportInitiativeZip:
        return {
          job: await this.zipTask(job, task),
          executeNextTask: true,
        };
      default:
        throw new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
          jobId: job._id,
        });
    }
  }
}

let instance: InitiativeExportWorkflow;
export const getInitiativeExportWorkflow = () => {
  if (!instance) {
    instance = new InitiativeExportWorkflow(
      wwgLogger,
      JobType.ExportInitiativeDataFull,
      getInitiativeExportService(),
      getRootInitiativeService(),
      getNotificationService()
    );
  }
  return instance;
};
