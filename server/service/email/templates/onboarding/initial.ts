/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import baseTemplate, { getBrandingTemplate, renderContentBottom } from '../core/baseTemplate';
import { BrandingTemplate, DomainConfig } from '../../../organization/domainConfig';
import { Email<PERSON>enderer } from '../htmlGenerator';
import { AppConfig } from '../../../app/AppConfig';

export enum EmailTemplate {
  SurveyContributorVerifierAssurer = 'survey-contributor-verifier-assurer',
  User = 'user',
  Manager = 'manager',
  Guest = 'guest'
}

export interface OnboardingInitialData {
  firstName?: string;
  onboardingUrl: string;
  unsubscribeUrl: string;
  initiativeName: string;
  emailTemplate?: EmailTemplate;
  domain: string | undefined;
  domainConfig: DomainConfig | undefined;
  appConfig: AppConfig | undefined;
}

export type TemplatesInterface = {
  [key in EmailTemplate]: EmailRenderer<OnboardingInitialData>
};

const getTemplates = (branding: BrandingTemplate): TemplatesInterface => {
  if (branding === BrandingTemplate.SGX) {
    const commonBody = `<p>Welcome to the Singapore ESG Disclosure Platform.</p>
    <p>Please click on the button below to register, and access the platform.</p>`;
    const getSubject = (userType: string) => `Singapore ESG Disclosure Platform - ${userType} Registration`;
    return {
      [EmailTemplate.Manager]: {
        subject: () => getSubject('Administrator'),
        topContent: () => commonBody
      },
      [EmailTemplate.SurveyContributorVerifierAssurer]: {
        subject: () => getSubject('Member'),
        topContent: () => commonBody
      },
      [EmailTemplate.User]: {
        subject: () => getSubject('User'),
        topContent: () => commonBody
      },
      [EmailTemplate.Guest]: {
        subject: () => getSubject('Guest'),
        topContent: () => commonBody
      }
    }
  }

  const nonManagerSubText = `<p>For you to access the platform, we need to on-board you as a member. Please click on the button below to register.</p>`
  const getSubject = (userType: string) => `G17Eco ${userType} Registration`;

  return {
    [EmailTemplate.Manager]: {
      subject: () => getSubject('Administrator'),
      topContent: ({ initiativeName }) => `<p>You are the nominated administrator for <strong>${initiativeName}</strong></p>
<p>In order to onboard you onto G17Eco, please click on the button below to complete your member registration.</p>`,
    },
    [EmailTemplate.SurveyContributorVerifierAssurer]: {
      subject: () => getSubject('Member'),
      topContent: ({ initiativeName }) => `<p><strong>${initiativeName}</strong> has invited you onto the G17Eco Platform to participate in their sustainability reporting as a contributor, verifier or assurer.</p> ${nonManagerSubText}`,
    },
    [EmailTemplate.User]: {
      subject: () => getSubject('User'),
      topContent: ({ initiativeName }) => `<p><strong>${initiativeName}</strong> has invited you to join the sustainability profile on the G17Eco Platform.</p> ${nonManagerSubText}`,
    },
    [EmailTemplate.Guest]: {
      subject: () => getSubject('Guest'),
      topContent: ({ initiativeName }) => `<p>You have been invited to view the sustainability profile for <strong>${initiativeName}</strong>.</p> ${nonManagerSubText}`,
    }
  }
}

export default (data: OnboardingInitialData) => {

  const template = data.emailTemplate ?? EmailTemplate.SurveyContributorVerifierAssurer
  const branding = getBrandingTemplate(data);
  const renderer = getTemplates(branding)[template];

  const topContent = renderer.topContent(data);

  return {
    subject: renderer.subject(data),
    body: baseTemplate({
      domain: data.domain,
      domainConfig: data.domainConfig,
      appConfig: data.appConfig,
      user: {
        firstName: data.firstName,
      },
      link: {
        url: data.onboardingUrl,
        type: 'button',
        text: 'Join now',
      },
      topContent: `${topContent}${renderContentBottom()}`,
    })
  }
};
