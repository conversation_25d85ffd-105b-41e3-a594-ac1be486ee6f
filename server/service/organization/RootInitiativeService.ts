/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { DomainConfig, mergeConfigScope } from './domainConfig';
import { DomainConfigRepository, getDomainConfigRepository } from './DomainConfigRepository';
import { getSubdomain } from '../../util/url';
import { InitiativeRepository, RootInitiativeData } from '../../repository/InitiativeRepository';
import { getSubscriptionManager, SubscriptionManager } from './SubscriptionManager';
import { AppConfigProvider, getAppConfigProvider } from '../app/AppConfigProvider';
import { getFeatureDetails, FeatureDetails, FeatureTag, FeatureCode } from '@g17eco/core';
import { ObjectId } from 'bson';
import { getInitiativeSettingsService, InitiativeSettingsService } from '../initiative/InitiativeSettingsService';
import { FeatureSetting, FeatureSettingType } from '../../models/initiativeSettings';
import { wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';


interface Options {
  domain?: string;
}

export interface RootConfig {
  features: FeatureDetails[];
  defaultFeatures: FeatureDetails[];
  survey: Pick<DomainConfig, 'scope'>;
}

export type Feature = FeatureDetails | FeatureSetting;

export class RootInitiativeService {
  constructor(
    private repo: DomainConfigRepository,
    private subscriptionManager: SubscriptionManager,
    private appConfigProvider: AppConfigProvider,
    private initiativeSettingsService: InitiativeSettingsService
  ) {}

  public async getDomainConfig(options: Options) {
    const subdomain = getSubdomain(options.domain);
    return subdomain ? this.repo.getBySubDomain(subdomain) : undefined;
  }

  private async getBaseFeatures(initiative: RootInitiativeData): Promise<FeatureDetails[]> {
    const appConfig = await this.getAppConfig(initiative);
    if (appConfig?.settings.baseFeatures) {
      return appConfig.settings.baseFeatures;
    }

    return [];
  }

  public async getDefaultFeatures(initiative: RootInitiativeData) {
    const baseFeatures = await this.getBaseFeatures(initiative);
    return new Set([...baseFeatures, ...this.appConfigProvider.getPermissionGroupFeatures(initiative)]);
  }

  public async getConfig(initiative: RootInitiativeData, options: Options): Promise<RootConfig> {
    const rootOrg = await this.getOrganization(initiative);

    const config: RootConfig = {
      features: [],
      defaultFeatures: [],
      survey: { scope: [] },
    };

    const [
      defaultFeatures,
      featuresSetting,
      subscriptionConfig
    ] = await Promise.all([
      this.getDefaultFeatures(initiative),
      this.initiativeSettingsService.getFeaturesSetting(initiative._id),
      this.subscriptionManager.getSubscriptionConfig(rootOrg)
    ]);

    // Add feature settings to default features
    featuresSetting.forEach((setting: FeatureSetting) => {
      defaultFeatures.add(setting);
    });

    // Create a new set instead of reference of defaultFeatures
    const features = new Set(defaultFeatures);

    // Add valid features based on initiative tags
    const validFeatureTags = Object.values(FeatureTag);
    initiative.tags
      ?.filter((tag) => validFeatureTags.includes(tag as FeatureTag))
      .forEach((tag) => {
        features.add(getFeatureDetails(tag as FeatureTag));
      });

    // Add features from subscription config
    subscriptionConfig.features?.forEach((subscriptionFeature) => {
      features.add(subscriptionFeature);
    });

    // Hydrate features
    config.features = this.consolidateFeatures(features);
    config.defaultFeatures = this.consolidateFeatures(defaultFeatures);

    // Hydrate
    // Scopes order referral -> subscription -> domain
    if (subscriptionConfig.scope?.length) {
      config.survey.scope = mergeConfigScope({
        currentScope: config.survey.scope,
        updateScope: subscriptionConfig.scope,
      });
    }

    // @TODO: [GU-4773] This should be removed - CTL is no longer required and controlled by product bundle
    const domainConfig = await this.getDomainConfig(options);
    if (domainConfig) {
      config.survey.scope = mergeConfigScope({
        currentScope: config.survey.scope,
        updateScope: domainConfig.scope,
      });
    }

    return config;
  }

  public hasFeature(config: RootConfig, featureCode: FeatureCode): boolean {
    return config.features.some((feature) => feature.code === featureCode);
  }

  /** Shortcut for getting features **/
  public async hasConfigFeature(initiative: RootInitiativeData, featureCode: FeatureCode): Promise<boolean> {
    // No options, as we only interested in features and does not require domain for it
    const config = await this.getConfig(initiative, {});

    return this.hasFeature(config, featureCode);
  }

  private isFeatureSetting(feature: Feature): feature is FeatureSetting {
    return 'type' in feature && feature.type === FeatureSettingType.InitiativeSetting;
  }

  public consolidateFeatures(features: Set<Feature>): FeatureDetails[] {
    const uniqueFeatures = new Map<FeatureCode, Feature>();
    features.forEach((feature) => {
      const code = feature.code;
      const currentFeature = uniqueFeatures.get(code);

      if (!currentFeature) {
        uniqueFeatures.set(code, feature);
        return;
      }

      if (this.isFeatureSetting(feature)) {
        // if we have multiple FeatureSetting, we take the latest, but log an error.
        if (this.isFeatureSetting(currentFeature)) {
          wwgLogger.error(
            new ContextError('Should have only one setting for a feature', { featureCode: feature.code })
          );
        }
        uniqueFeatures.set(code, feature);
        return;
      }

      // if we have FeatureSetting, we take that over base plan
      if (this.isFeatureSetting(currentFeature)) {
        return;
      }


      const currentLimit = currentFeature.config?.limit ?? 0;
      const newLimit = feature.config?.limit ?? 0;

      if (currentLimit >= newLimit) {
        return;
      }
      uniqueFeatures.set(code, feature);
    });

    return Array.from(uniqueFeatures.values());
  }

  public async getOrganization<T extends Pick<RootInitiativeData, '_id' | 'tags' | 'appConfigCode'>>(
    initiative: T
  ): Promise<RootInitiativeData | T> {
    return InitiativeRepository.getOrganization(initiative);
  }

  public async getOrganizationById(initiativeId: ObjectId | string) {
    const initiative = await InitiativeRepository.mustFindById(initiativeId, { tags: 1, appConfigCode: 1, _id: 1 });
    return InitiativeRepository.getOrganization(initiative);
  }

  async getAppConfig(root: RootInitiativeData) {
    return this.appConfigProvider.getByOrganization(root);
  }

  async getConfigs(configCodes: string[]) {
    return this.appConfigProvider.getConfigs(configCodes);
  }
}

let instance: RootInitiativeService;
export const getRootInitiativeService = () => {
  if (!instance) {
    instance = new RootInitiativeService(
      getDomainConfigRepository(),
      getSubscriptionManager(),
      getAppConfigProvider(),
      getInitiativeSettingsService()
    );
  }
  return instance;
};
