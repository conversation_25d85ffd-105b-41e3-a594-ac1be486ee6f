import { WorkBook } from '@sheet/core';
import { ObjectId } from 'bson';
import UserError from '../../error/UserError';
import { JobStatus } from '../../models/backgroundJob';
import {
  AssessmentMinData,
  MaterialityAssessmentRepository,
  getMaterialityAssessmentRepository,
} from '../../repository/MaterialityAssessmentRepository';
import { DateFormat, customDateFormat } from '../../util/date';
import { BackgroundJobService, getBackgroundJobService } from '../background-process/BackgroundJobService';
import { Excel, getExcel } from '../file/Excel';
import { MaterialityAssessmentBackgroundJobService } from './MaterialityAssessmentBackgroundJobService';
import { MaterialityAssessmentService } from './MaterialityAssessmentService';
import { ValueData } from '../../models/public/universalTrackerValueType';
import { Option } from '../../models/public/valueList';
import type { MaterialityAssessmentContext } from "./background-job/types";
import { sanitizeCellString } from '../file/writer/CsvFileWriter';
import { AssessmentResult, AssessmentResultType } from './types';

const assessmentInsightColumns = {
  name: { header: 'Material topic', width: 350 },
  score: { header: 'Materiality', width: 100 },
  utrMapping: { header: 'Mapped UTR codes', width: 200 },
  esgs: { header: 'ESGs', width: 200 },
  sdgs: { header: 'SDGs', width: 200 },
  materialPillars: { header: 'Material pillars', width: 200 },
  materialBoundaries: { header: 'Material boundaries', width: 200 },
};

const assessmentAnswerColumns = {
  name: { header: 'Metric Title', width: 400 },
  code: { header: 'Metric Code', width: 300 },
  answer: { header: 'Answer', width: 400 },
};

const defaultCategories = { boundary: [], esg: [], materialPillar: [], sdg: [] };

type ResultData = MaterialityAssessmentContext['result'];

export class MaterialityAssessmentReportService {
  constructor(
    private excel: Excel,
    private backgroundJobService: BackgroundJobService,
    private materialityAssessmentRepository: MaterialityAssessmentRepository
  ) {}

  public async downloadAssessmentResults({
    initiativeId,
    surveyId,
    type,
  }: {
    initiativeId: ObjectId | string;
    surveyId: ObjectId;
    type: AssessmentResultType;
  }) {
    const bjService = new MaterialityAssessmentBackgroundJobService(this.backgroundJobService);
    const job = await bjService.findExistingJob({
      initiativeId: new ObjectId(initiativeId),
      surveyId: new ObjectId(surveyId),
    });

    if (!job || job.status !== JobStatus.Completed) {
      throw new UserError('The report is not ready to generate yet.', { initiativeId });
    }

    const [survey] = await this.materialityAssessmentRepository.getAssessmentData(surveyId);

    const thirdSheetData = await this.recalculateMaterialityScores(surveyId);

    const workbook = await this.createWorkbook({
      firstSheetData: job.tasks[0].data.result,
      secondSheetData: survey.visibleUtrvs,
      thirdSheetData,
      type,
    });

    return {
      workbook,
      filename: `${customDateFormat(survey.effectiveDate, DateFormat.MonthYear)} Assessment Results`,
    };
  }

  private async recalculateMaterialityScores(surveyId: ObjectId) {
    // This is expensive, but no other way to get original matrix of question scores from the results in the BackgroundJob
    const service = new MaterialityAssessmentService(surveyId);
    return service.getResult({ debugMode: true });
  }

  public async downloadAssessmentAnswers({ surveyId }: { surveyId: ObjectId | string }) {
    const [survey] = await this.materialityAssessmentRepository.getAssessmentData(surveyId);
    const workbook = await this.excel.createBook();
    await this.addAnswersSheet({ workbook, data: survey.visibleUtrvs });
    return {
      workbook,
      filename: `${customDateFormat(survey.effectiveDate, DateFormat.MonthYear)} Assessment Answers`,
    };
  }

  private async createWorkbook({
    firstSheetData,
    secondSheetData,
    thirdSheetData,
    type,
  }: {
    firstSheetData: ResultData | undefined;
    secondSheetData: AssessmentMinData['visibleUtrvs'];
    thirdSheetData: AssessmentResult;
    type: AssessmentResultType;
  }) {
    const workbook = await this.excel.createBook();
    await this.addScoresSheet({ workbook, data: firstSheetData, type });
    await this.addAnswersSheet({ workbook, data: secondSheetData });
    await this.addScoreMatrixSheet({ workbook, data: thirdSheetData, type });
    return workbook;
  }

  private async addScoreMatrixSheet({
    workbook,
    data,
    type,
  }: {
    workbook: WorkBook;
    data: ResultData | undefined;
    type: AssessmentResultType;
  }) {
    if (!data?.debug) {
      return;
    }

    const debugData = data.debug[type];
    const transformedData = Object.entries(debugData)
      .map(([utrCode, map]) => ({
        utrCode,
        ...map
      }));

    const sheet = this.excel.jsonToSheet(transformedData);
    await this.excel.addToBook(
      workbook,
      sheet,
      type === AssessmentResultType.Financial ? 'Financial Scores' : 'Impact Scores'
    );
  }

  private async addScoresSheet({
    workbook,
    data,
    type,
  }: {
    workbook: WorkBook;
    data: ResultData | undefined;
    type: AssessmentResultType;
  }) {
    const records = this.getResultByScoreDesc({ data, type });
    const sheet = this.excel.jsonToSheet(records);
    await this.excel.addToBook(
      workbook,
      sheet,
      type === AssessmentResultType.Financial ? 'Financial' : 'Impact'
    );
    this.excel.changeHeaders(
      sheet,
      Object.values(assessmentInsightColumns).map(({ header }) => header)
    );
    this.excel.setColumnsWidth(
      sheet,
      Object.values(assessmentInsightColumns).map(({ width }) => width)
    );
  }

  private async addAnswersSheet({ workbook, data }: { workbook: WorkBook; data: AssessmentMinData['visibleUtrvs'] }) {
    const sheet = this.excel.jsonToSheet(this.getAssessmentAnswers(data));
    await this.excel.addToBook(workbook, sheet, 'Metrics');
    this.excel.changeHeaders(
      sheet,
      Object.values(assessmentAnswerColumns).map(({ header }) => header)
    );
    this.excel.setColumnsWidth(
      sheet,
      Object.values(assessmentAnswerColumns).map(({ width }) => width)
    );
  }

  private getResultByScoreDesc({ data, type }: { data: ResultData; type: AssessmentResultType }) {
    if (!data?.[type]) {
      return [];
    }

    return data[type]
      .map((row) => {
        const { categories = defaultCategories, utrMapping = [], name, score } = row;
        return {
          name: name,
          score,
          utrMapping: utrMapping.map((utr) => utr.code).join(', '),
          esgs: (categories.esg ?? []).join(', '),
          sdgs: (categories.sdg ?? []).join(', '),
          materialPillars: (categories.materialPillar ?? []).join(', '),
          materialBoundaries: (categories.boundary ?? []).join(', '),
        };
      })
      .sort((a, b) => b.score - a.score);
  }

  private getAssessmentAnswers(utrvs: AssessmentMinData['visibleUtrvs']) {
    return utrvs.reduce((acc, utrv) => {
      const { universalTracker, valueData, valueList } = utrv;
      acc.push({
        name: sanitizeCellString(universalTracker.name),
        code: universalTracker.code,
        answer: sanitizeCellString(this.getAnswerPerQuestion(valueData, valueList)),
      });
      return acc;
    }, [] as { [key in keyof typeof assessmentAnswerColumns]: string }[]);
  }

  private getAnswerPerQuestion(valueData: ValueData<string | string[]>, valueList: Option[]) {
    const valueListCodes = Array.isArray(valueData.data) ? valueData.data : [valueData.data];
    const valueListNames = valueListCodes.reduce((acc, code) => {
      const option = valueList.find((op) => op.code === code);
      if (option) {
        acc.push(option.name);
      }
      return acc;
    }, [] as string[]);
    return valueListNames.join(', ');
  }
}

let instance: MaterialityAssessmentReportService;
export const getMaterialityAssessmentReportService = () => {
  if (!instance) {
    instance = new MaterialityAssessmentReportService(
      getExcel(),
      getBackgroundJobService(),
      getMaterialityAssessmentRepository()
    );
  }

  return instance;
};
