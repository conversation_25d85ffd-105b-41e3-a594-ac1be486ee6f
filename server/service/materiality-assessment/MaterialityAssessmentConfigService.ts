import UserError from '../../error/UserError';
import { MaterialityAssessmentConfig } from '../../models/materiality';
import {
  getMaterialityAssessmentBackgroundJobService,
  MaterialityAssessmentBackgroundJobService,
} from './MaterialityAssessmentBackgroundJobService';
import { ObjectId } from 'bson';

export class MaterialityAssessmentConfigService {
  constructor(private backgroundJobService: MaterialityAssessmentBackgroundJobService) {}

  public async update({
    initiativeId,
    surveyId,
    config,
  }: {
    initiativeId: ObjectId;
    surveyId: ObjectId;
    config?: MaterialityAssessmentConfig;
  }) {
    const assessmentJob = await this.backgroundJobService.findExistingJob({
      initiativeId,
      surveyId,
    });

    if (!assessmentJob || !assessmentJob.tasks?.[0]?.data) {
      throw new UserError('Materiality Assessment results not found', { initiativeId, surveyId });
    }

    assessmentJob.tasks[0].data.config = config;
    assessmentJob.markModified('tasks');
    await assessmentJob.save();

    return assessmentJob.tasks[0].data.config;
  }
}

let instance: MaterialityAssessmentConfigService;
export const getMaterialityAssessmentConfigService = () => {
  if (!instance) {
    instance = new MaterialityAssessmentConfigService(getMaterialityAssessmentBackgroundJobService());
  }
  return instance;
};
