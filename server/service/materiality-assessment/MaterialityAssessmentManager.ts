import Initiative, { InitiativePlain } from '../../models/initiative';
import { SurveyModel, SurveyModelMinData, SurveyModelPlain, SurveyType } from '../../models/survey';
import { UserModel, UserPlain } from '../../models/user';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { createStakeholderGroup } from '../stakeholder/StakeholderGroupManager';
import { SurveyImporter } from '../survey/SurveyImporter';
import { blueprintDefaultUnitConfig } from '../units/unitTypes';
import { DataPeriods, UtrvType } from '../utr/constants';
import { ObjectId } from 'bson';
import { Blueprints, materialityAssessmentBlueprints } from '../../survey/blueprints';
import {
  CreateSurveyDto,
  MaterialityAssessmentUtrCodes,
  materialityContextUtrCodes,
} from '../../routes/validation-schemas/materiality-assessment';
import { SurveyScope } from '../survey/SurveyScope';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import { CustomerManager, ProducPaymentCreate, getCustomerManager } from '../payment/CustomerManager';
import { createSurveyCode } from '../../util/string';
import { ProductCodes
 } from '../../models/customer';
import { MaterialityMetricRepository } from './MaterialityMetricRepository';
import { SurveyComposer, createSurveyComposer } from '../survey/SurveyComposer';
import UniversalTrackerValueManager, { getUniversalTrackerValueManager } from '../utr/UniversalTrackerValueManager';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { ProductManager, getProductManager } from '../payment/ProductManager';
import Stripe from 'stripe';
import UserError from '../../error/UserError';
import ContextError from '../../error/ContextError';
import { DateFormat, customDateFormat, getYear } from '../../util/date';
import { MaterialityAssessmentScopeCalculator } from './MaterialityAssessmentSizeCalculator';
import { getSurveyManager, SurveyManager } from '../survey/SurveyManager';
import { getMaterialityAssessmentRepository } from '../../repository/MaterialityAssessmentRepository';
import { AssessmentType } from '../../types/materiality-assessment';
import { MaterialityAssessmentType } from '../../models/materialityMetric';
import { MaterialityAssessmentScope } from './types';
import { topicLengthMap } from './constants';

const YEARS_BACK = 6;
const YEARS_NEXT = 2;

export interface CreateSurveyParams {
  initiative: Pick<InitiativePlain, '_id' | 'code'>;
  productCode: ProductCodes;
  effectiveDate: Date;
  context: CreateSurveyDto;
  user: UserModel;
  assessmentType: AssessmentType;
}

export class MaterialityAssessmentManager {
  constructor(
    private logger: LoggerInterface,
    private customerManager: CustomerManager,
    private productManager: ProductManager,
    private surveyManager: SurveyManager,
    private surveyComposer: SurveyComposer,
    private utrvManager: UniversalTrackerValueManager,
    private materialityAssessmentRepository: ReturnType<typeof getMaterialityAssessmentRepository>,
  ){}

  public async create({ initiativeId, user, returnUrl, promoCode, context, metadata }: {
    initiativeId: ObjectId,
    user: Pick<UserPlain, '_id' | 'email'>,
    returnUrl: string,
    promoCode?: string,
    context: CreateSurveyDto,
    metadata: {
      [key: string]: string;
    }
  }): Promise<{
    sessionUrl: string | null;
  }> {

    const { productCode } = await this.getContext({
      context,
    });

    const initiative = await Initiative.findById(initiativeId).exec();
    if (!initiative) {
      throw new UserError(
        'We currently cannot provide an assessment for your organization (E1). Please contact support.',
        {
          initiativeId: initiativeId.toString(),
          promoCode,
        }
      );
    }

    const contextErrorMeta = {
      initiativeId: initiative._id.toString(),
      promoCode,
      productCode,
    };

    const product = await this.productManager.getProduct(productCode);
    if (!product) {
      throw new UserError(
        'We currently cannot provide an assessment for your organization (E2). Please contact support.',
        contextErrorMeta
      );
    }

    if (promoCode) {
      const validatedCoupon = await this.getValidatedCoupon(promoCode, product);
      if (!validatedCoupon) {
        // Even though 'createProductSubscription' gets the promotion from the promoCode,
        // I want to validate it here as that function will fall back to non-promo pricing
        // When we open MaterialityTracker for purchases, this probably can be removed.
        throw new UserError('The referral code supplied is not valid. Please contact support.', contextErrorMeta);
      }
    }

    const productInvoiceData: ProducPaymentCreate = {
      initiative,
      user,
      productCode,
      promoCode,
      successUrl: `${returnUrl}?checkout=success&session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: returnUrl,
      metadata: {
        productCode,
        ...metadata,
        ...context,
      }
    };

    return this.customerManager.createProductPayment(productInvoiceData);
  }

  public async getSurveyIdBySessionId({ initiativeId, sessionId }: { initiativeId: ObjectId; sessionId: string }) {
    const session = await this.customerManager.retrieveSession(sessionId, [ 'payment_intent' ]);

    const initiativeIdStr = initiativeId.toHexString();

    const debugInfo = { initiativeId: initiativeIdStr, sessionId };

    const metadata = typeof session.payment_intent === 'object' && session.payment_intent?.metadata ? session.payment_intent.metadata : session.metadata ?? {};

    if (!metadata.initiativeId) {
      throw new ContextError('No metadata found in session or paymentIntent', { ...debugInfo, metadata });
    }

    if (metadata.initiativeId !== initiativeIdStr) {
      throw new ContextError('Unexpected lookup from initiative to unmatched session.', { ...debugInfo, metadata });
    }

    const surveyId = metadata.surveyId;
    return {
      status: surveyId ? 'complete' : 'pending',
      surveyId,
    };
  }

  public async getContext(params: Pick<CreateSurveyParams, 'context'>) {
    const sizeScope = MaterialityAssessmentScopeCalculator.getSizeScopeTag(params.context);
    const productCode = await this.getProductCodeFromScope(sizeScope);

    const startupScope =  MaterialityAssessmentScopeCalculator.getStartupScopeTag(params.context);
    const surveyScope = [sizeScope];
    if (startupScope) {
      surveyScope.push(startupScope);
    }

    return {
      productCode,
      surveyScope,
    }
  }

  public async createSurvey(params: CreateSurveyParams) {
    const { initiative, context, effectiveDate, user, assessmentType } = params;

    await this.validateContext({
      initiative,
      effectiveDate,
    });

    const { productCode } = await this.getContext({
      context,
    });

    if (productCode !== params.productCode) {
      throw new UserError(`The purchased product does not match the expected one, expected ${productCode}`, {
        debugMessage: 'Invalid product code',
        purchased: params.productCode,
        expected: productCode
      });
    }

    const sizeScope = MaterialityAssessmentScopeCalculator.getSizeScopeTag(context);

    const createData: SurveyModelMinData & {
      blueprint: SurveyModelPlain['blueprint'];
      assessmentType: AssessmentType;
    } = {
      scope: SurveyScope.createEmpty(),
      visibleUtrvs: [],
      code: createSurveyCode(initiative.code),
      name: String(getYear(effectiveDate)),
      sourceName: Blueprints.Materiality2024,
      period: DataPeriods.Yearly,
      type: SurveyType.Materiality,
      effectiveDate,
      utrvType: UtrvType.Actual,
      visibleStakeholders: [user._id],
      unitConfig: blueprintDefaultUnitConfig,
      initiativeId: initiative._id,
      stakeholders: createStakeholderGroup(),
      roles: { admin: [user._id], viewer: [] },
      evidenceRequired: false,
      noteRequired: false,
      verificationRequired: false,
      isPrivate: true,
      blueprint: undefined,
      assessmentType,
    };

    const startupScope =  MaterialityAssessmentScopeCalculator.getStartupScopeTag(params.context);
    const surveyScope = [sizeScope];
    if (startupScope) {
      surveyScope.push(startupScope);
    }
    this.logger.info('Creating a new survey with scope', { productCode, surveyScope, context: params.context });

    const materialityMetricCodes = await this.getBlueprintUtrFilterCodes({ surveyScope, assessmentType });

    createData.blueprint = await this.surveyComposer.composeBlueprint(createData, materialityMetricCodes);
    const survey: SurveyModel<ObjectId> = await SurveyImporter.create(createData, user);
    this.logger.info('Created new survey materiality tracker survey', {
      surveyId: survey._id.toHexString(),
      context: params.context,
      surveyScope,
    });

    try {
      await this.saveScopeToUtrvs(survey, user, params.context);
    } catch (e) {
      console.log(e);
    }

    return survey;
  }

  static isMaterialityAssessmentBlueprint(sourceName: Blueprints) {
    return materialityAssessmentBlueprints.includes(sourceName);
  }

  public async processAction(survey: SurveyModel, user: UserPlain, action: 'regenerate' | 'recalculate') {
    switch (action) {
      case 'regenerate': {
        if (survey.type !== SurveyType.Materiality) {
          throw new ContextError('Invalid survey', { surveyId: survey._id.toHexString(), surveyType: survey.type });
        }
        const assessmentType = survey.assessmentType || AssessmentType.FinancialMateriality;
        const filterUtrCodes = await this.getBlueprintUtrFilterCodesBySurvey({ surveyId: survey._id, assessmentType });
        return this.surveyManager.regenerate(survey, user, filterUtrCodes);
      }
      default:
        throw new ContextError('Invalid action', { surveyId: survey._id.toHexString(), userId: user._id.toHexString(), action });
    }
  }

  private async getBlueprintUtrFilterCodes({
    surveyScope,
    assessmentType,
  }: {
    surveyScope: MaterialityAssessmentScope[];
    assessmentType: AssessmentType;
  }) {
    const types =
      assessmentType === AssessmentType.DoubleMateriality
        ? [MaterialityAssessmentType.Financial, MaterialityAssessmentType.Impact]
        : [MaterialityAssessmentType.Financial];
    const materialityMetrics = await MaterialityMetricRepository.findMetricsByScope({ scope: surveyScope, types });
    return Array.from(new Set(materialityMetrics.map((m) => m.utrCode)));
  }

  private async getBlueprintUtrFilterCodesBySurvey({
    surveyId,
    assessmentType,
  }: {
    surveyId: ObjectId;
    assessmentType: AssessmentType;
  }): Promise<string[]> {
    const utrs = await UniversalTrackerRepository.findByCodes(materialityContextUtrCodes, { _id: 1, code: 1 });
    const utrvs = await UniversalTrackerValue.find(
      {
        universalTrackerId: { $in: utrs.map((u) => u._id) },
       'compositeData.surveyId': surveyId
      },
      {
        _id: 1,
        universalTrackerId: 1,
        valueData: 1
      }
    ).lean();

    const context: CreateSurveyDto = {
      [MaterialityAssessmentUtrCodes.NumStaff]: '',
      [MaterialityAssessmentUtrCodes.OperationTime]: '',
      [MaterialityAssessmentUtrCodes.AnnualSales]: '',
      [MaterialityAssessmentUtrCodes.CapitalEmployed]: '',
      [MaterialityAssessmentUtrCodes.Sector]: '',
    };

    for (const utrCode of Object.values(MaterialityAssessmentUtrCodes)) {
      const utr = utrs.find((u) => u.code === utrCode);
      const utrId = utr?._id ?? 'missing';
      const utrv = utrvs.find((u) => u.universalTrackerId.equals(utrId));
      context[utrCode as MaterialityAssessmentUtrCodes] = String(utrv?.valueData?.data ?? '');
    }

    const sizeScope = MaterialityAssessmentScopeCalculator.getSizeScopeTag(context);
    const startupScope =  MaterialityAssessmentScopeCalculator.getStartupScopeTag(context);
    const surveyScope = [sizeScope];
    if (startupScope) {
      surveyScope.push(startupScope);
    }
    this.logger.info('Context for blueprint filter', { surveyId: surveyId.toHexString(), context, surveyScope });

    return this.getBlueprintUtrFilterCodes({ surveyScope, assessmentType });
  }

  private async getValidatedCoupon(referralCode: string, product: Stripe.Product) {
    const [promotionCode] = referralCode ? await this.customerManager.getActivePromotions(referralCode) : [];
    if (!promotionCode) {
      return;
    }

    if (promotionCode.times_redeemed > 0) {
      wwgLogger.warn(new ContextError('Promotion code has already been redeemed', { referralCode }));
      return;
    }

    const productId = product.id;

    const coupon = await this.customerManager.getCoupon(promotionCode.coupon.id, { expand: ['applies_to'] });
    if (!coupon) {
      wwgLogger.warn(new ContextError('Promotion code has no coupon', { referralCode }));
      return;
    }

    if (coupon.applies_to?.products.includes(productId)) {
      return {
        promotionCode,
        coupon,
      };
    }

    wwgLogger.warn(
      new ContextError('Promotion code cannot be used with product', {
        referralCode,
        couponId: coupon.id,
        productCode: product.metadata.productCode,
      })
    );
    return;
  }

  private async getProductCodeFromScope(scope: MaterialityAssessmentScope) {
    switch (scope) {
      case MaterialityAssessmentScope.Solopreneur:
        return ProductCodes.MaterialityTrackerSolopreneur;
      case MaterialityAssessmentScope.Startup:
        return ProductCodes.MaterialityTrackerStartup;
      case MaterialityAssessmentScope.Micro:
        return ProductCodes.MaterialityTrackerMicro;
      case MaterialityAssessmentScope.SME:
        return ProductCodes.MaterialityTrackerSME;
      case MaterialityAssessmentScope.MidCap:
        return ProductCodes.MaterialityTrackerMidCap;
      case MaterialityAssessmentScope.MNC:
        return ProductCodes.MaterialityTrackerMNC;
      case MaterialityAssessmentScope.Large:
        return ProductCodes.MaterialityTrackerLarge;
    }
  }

  private async saveScopeToUtrvs(survey: SurveyModel, user: UserModel, context: CreateSurveyDto) {
    const utrCodes = Object.keys(context);
    const utrs = await UniversalTrackerRepository.findByCodes(utrCodes, { _id: 1, code: 1 });
    const utrvs = await UniversalTrackerRepository.getUtrvs<
      Pick<UniversalTrackerValuePlain, '_id' | 'universalTrackerId'>
    >(
      survey._id,
      utrs.map((u) => u._id),
      { _id: 1, universalTrackerId: 1 }
    );

    const materialityUtrCodes = Object.values(MaterialityAssessmentUtrCodes);

    for (const { _id, code } of utrs) {
      if (!materialityUtrCodes.map(String).includes(code)) {
        wwgLogger.warn('Invalid utr code passed into materiality assessment creation. Skipping.', {
          utrCode: code,
          surveyId: survey._id.toString(),
          context,
        });
        continue;
      }

      const value = context[code as MaterialityAssessmentUtrCodes];
      if (!value) {
        wwgLogger.warn('Invalid value passed for materiality assessment utr. Skipping.', {
          utrCode: code,
          surveyId: survey._id.toString(),
          context,
        });
        continue;
      }
      const utrv = utrvs.find((u) => u.universalTrackerId.equals(_id));
      if (!utrv) {
        continue;
      }

      const updatedUtrv = await this.utrvManager.update({
        id: utrv._id,
        user,
        data: {
          valueData: {
            data: value,
            input: {
              data: value,
            },
          },
        },
        autoVerify: true,
      });
      await updatedUtrv.save();
    }
  }

  private async validateContext({
    initiative,
    effectiveDate
  }: Pick<CreateSurveyParams, 'initiative' | 'effectiveDate'>) {
    const currentYear = getYear();
    const year = getYear(effectiveDate);

    if (year <= currentYear - YEARS_BACK || year > currentYear + YEARS_NEXT) {
      // Arbitrary limits, for no good reason other than to have one. Feel free to change if needed.
      throw new UserError(
        `Year is invalid, it must be between ${currentYear - YEARS_BACK} and ${currentYear + YEARS_NEXT}`,
        { initiativeId: initiative._id, effectiveDate }
      );
    }
    const existingEffectiveDates = await this.getSurveyEffectiveDates(initiative._id);
    if (existingEffectiveDates.includes(effectiveDate.toISOString())) {
      throw new UserError(`Assessment already exists for ${customDateFormat(effectiveDate, DateFormat.MonthYear)}`);
    }

    return true;
  }

  public async getContextUtrs() {
    const utrCodes: string[] = [...materialityContextUtrCodes];

    const projection = {
      _id: 1,
      code: 1,
      type: 1,
      name: 1,
      valueType: 1,
      unitType: 1,
      valueValidation: 1,
      instructions: 1,
    };

    const utrs = await UniversalTrackerRepository.findByCodesWithValueList(utrCodes, projection);

    return (
      utrs
        // Sort utrs based on the order of utrCodes
        .sort((a, b) => utrCodes.indexOf(a.code) - utrCodes.indexOf(b.code))
        .map((utr) => ({
          ...utr,
          valueValidation: {
            valueList: {
              ...utr.valueValidation?.valueList,
              list: utr.valueListOptions?.options ?? [],
            },
          },
        }))
    );
  }

  public async getSurveyEffectiveDates(initiativeId: ObjectId) {
    const surveys = await SurveyRepository.findSurveys(
      {
        initiativeId: new ObjectId(initiativeId),
        type: SurveyType.Materiality,
        deletedDate: { $exists: false },
      },
      { effectiveDate: 1 }
    );
    return surveys.map((s) => s.effectiveDate.toISOString());
  }

  public async listSurveys(initiativeId: ObjectId | string): Promise<SurveyModelMinData[]> {
    return SurveyRepository.findSurveysWithUtrvs(
      {
        initiativeId: new ObjectId(initiativeId),
        type: SurveyType.Materiality,
        deletedDate: { $exists: false },
      },
      { $sort: { effectiveDate: -1 } }
    );
  }

  public async getSizeScopeByAssessmentId(assessmentId: string | ObjectId) {
    const setupAnswer = await this.materialityAssessmentRepository.getAssessmentSetupAnswers(assessmentId);
    return MaterialityAssessmentScopeCalculator.getSizeScopeTag(setupAnswer);
  }

  public async getTopTopicsCount(assessmentId: string | ObjectId, customTopicLength?: number) {
    if (customTopicLength) {
      return customTopicLength;
    }
    const scope = await this.getSizeScopeByAssessmentId(assessmentId);
    return topicLengthMap[scope];
  }
}

let instance: MaterialityAssessmentManager;
export const getMaterialityAssessmentManager = () => {
  if (!instance) {
    instance = new MaterialityAssessmentManager(
      wwgLogger,
      getCustomerManager(),
      getProductManager(),
      getSurveyManager(),
      createSurveyComposer(),
      getUniversalTrackerValueManager(),
      getMaterialityAssessmentRepository(),
    );
  }

  return instance;
};
