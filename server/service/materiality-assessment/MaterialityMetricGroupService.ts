import { ObjectId } from 'bson';
import config from '../../config';
import ContextError from '../../error/ContextError';
import MetricGroup, {
  CustomMetricOrderType,
  MetricGroupModel,
  MetricGroupSourceType,
  MetricGroupType,
} from '../../models/metricGroup';
import { SurveyModelPlain } from '../../models/survey';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { SupportedJobModel } from './background-job/types';
import { getMaterialityAssessmentBackgroundJobService } from './MaterialityAssessmentBackgroundJobService';
import { getMaterialityAssessmentManager } from './MaterialityAssessmentManager';
import { customDateFormat, DateFormat } from '../../util/date';
import { isMaterialitySurvey } from '../../util/survey';
import { MaterialityAssessmentService } from './MaterialityAssessmentService';

interface GenerateUtrsParams {
  metricGroup: MetricGroupModel;
  job: SupportedJobModel;
  topTopicsCountOverride?: number;
  survey: Pick<SurveyModelPlain, '_id' | 'type' | 'assessmentType' | 'completedDate'>;
}

export class MaterialityMetricGroupService {
  constructor(
    private surveyRepo: typeof SurveyRepository,
    private materialityAssessmentManager: ReturnType<typeof getMaterialityAssessmentManager>,
    private backgroundJobMTService: ReturnType<typeof getMaterialityAssessmentBackgroundJobService>
  ) {}

  public async findOrCreateMetricGroup({
    userId,
    survey,
  }: {
    userId: ObjectId;
    survey: Pick<SurveyModelPlain, '_id' | 'initiativeId' | 'effectiveDate'>;
  }) {
    const { _id: surveyId, initiativeId, effectiveDate } = survey;

    const match = {
      initiativeId,
      'source.type': MetricGroupSourceType.Survey,
      'source.surveyId': surveyId,
    };

    const metricGroup = await MetricGroup.findOne(match).exec();

    if (metricGroup) {
      return metricGroup;
    }

    return new MetricGroup({
      initiativeId: initiativeId,
      type: MetricGroupType.Custom,
      groupName: customDateFormat(effectiveDate, DateFormat.MonthYear),
      groupData: { icon: `${config.assets.logoSrcRoot}/materiality_tracker_logo.svg` },
      metricsOrder: { orderType: CustomMetricOrderType.TypeCode },
      createdBy: userId,
      source: { type: MetricGroupSourceType.Survey, surveyId },
    });
  }

  public async generateMetricGroupUtrs({ userId, job }: { userId: ObjectId } & Pick<GenerateUtrsParams, 'job'>) {
    const { surveyId } = job.tasks[0].data;
    const survey = await this.surveyRepo.mustFindById(surveyId);
    if (!isMaterialitySurvey(survey)) {
      throw new ContextError('Survey is not a materiality survey', { surveyId, jobId: job._id });
    }

    const metricGroup = await this.findOrCreateMetricGroup({ userId, survey });

    if (metricGroup.source?.jobId) {
      return metricGroup;
    }

    return this.processGenerateUtrs({ metricGroup, job, survey });
  }

  public async regenerateMetricGroupUtrs({ groupId, topTopicsCount }: { groupId: ObjectId; topTopicsCount: number }) {
    const metricGroup = await MetricGroup.findById(groupId).populate('survey').orFail().exec();

    const { surveyId } = metricGroup.source ?? {};

    if (!surveyId || !metricGroup.survey) {
      throw new ContextError('No survey found for metric group', { metricGroupId: groupId });
    }

    // find the latest score job
    const job = await this.backgroundJobMTService.findExistingJob({
      initiativeId: metricGroup.initiativeId,
      surveyId: surveyId,
    });

    if (!job) {
      throw new ContextError('No score job found for metric group', { metricGroupId: groupId });
    }

    const currentJobId = metricGroup.source?.jobId;
    const currentTopTopicsCount = metricGroup.source?.topTopicsCount;
    const shouldUpdate = !job._id.equals(currentJobId) || currentTopTopicsCount !== topTopicsCount;

    if (!shouldUpdate) {
      return metricGroup;
    }

    return this.processGenerateUtrs({
      metricGroup,
      job,
      survey: metricGroup.survey,
      topTopicsCountOverride: topTopicsCount,
    });
  }

  private async processGenerateUtrs({ metricGroup, job, survey, topTopicsCountOverride }: GenerateUtrsParams) {
    if (!survey.completedDate) {
      throw new ContextError('Survey is not completed', { surveyId: survey._id });
    }
    if (!metricGroup.source) {
      throw new ContextError('No source found for metric group', { metricGroupId: metricGroup._id });
    }
    const topTopicsCount = await this.materialityAssessmentManager.getTopTopicsCount(
      survey._id,
      topTopicsCountOverride ?? metricGroup.source.topTopicsCount
    );
    const materialityTopicService = new MaterialityAssessmentService(survey._id);
    const utrs = await materialityTopicService.getUtrMappingFromOrderedTopics({
      data: job.tasks[0].data,
      topTopicsCount,
      survey,
    });

    const topicUtrs = utrs.map(({ _id }) => ({ _id }));

    metricGroup.source.topTopicsCount = topTopicsCount;
    metricGroup.source.topicUtrs = topicUtrs;
    metricGroup.source.jobId = job._id;
    // all modifications to the existing groups will be discarded
    metricGroup.universalTrackers = topicUtrs.map(({ _id }) => _id);

    return metricGroup.save();
  }
}

let instance: MaterialityMetricGroupService;

export const getMaterialityMetricGroupService = () => {
  if (!instance) {
    instance = new MaterialityMetricGroupService(
      SurveyRepository,
      getMaterialityAssessmentManager(),
      getMaterialityAssessmentBackgroundJobService()
    );
  }
  return instance;
};
