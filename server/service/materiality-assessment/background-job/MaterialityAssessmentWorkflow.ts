/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import ContextError from '../../../error/ContextError';
import { JobType, TaskStatus, TaskType } from '../../../models/backgroundJob';
import { JobStatus } from '../../../models/surveyTemplateHistory';
import { BackgroundBaseWorkflow, TaskResult } from '../../background-process/BackgroundBaseWorkflow';
import { createLogEntry } from '../../jobs';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { MaterialityAssessmentService } from '../MaterialityAssessmentService';
import { SupportedJobModel, SupportedTask } from './types';
import { getMaterialityMetricGroupService } from '../../materiality-assessment/MaterialityMetricGroupService';

export class MaterialityAssessmentWorkflow extends BackgroundBaseWorkflow<SupportedJobModel> {
  constructor(
    protected logger: LoggerInterface,
    protected jobType: JobType.MaterialityAssessmentScores,
    private metricGroupService: ReturnType<typeof getMaterialityMetricGroupService>
  ) {
    super();
  }

  protected async processTask(job: SupportedJobModel, task: SupportedTask): Promise<TaskResult<SupportedJobModel>> {
    const result: TaskResult<SupportedJobModel> = {
      job,
      executeNextTask: false,
    };

    const taskId = task.id;
    task.status = TaskStatus.Processing;
    job.markModified('tasks');
    await job.save();

    try {
      switch (task.type) {
        case TaskType.GenerateMaterialityAssessmentScores: {
          const surveyId = task.data.surveyId;
          const service = new MaterialityAssessmentService(surveyId);
          this.logger.info('Scoring Materiality', { jobId: job._id.toString() });
          job.logs.push(createLogEntry('Scoring Materiality'));
          const scoreResult = await service.getResult({ modelType: task.data.modelType });
          task.data.result = scoreResult;
          task.status = TaskStatus.Completed;
          result.executeNextTask = true;
          break;
        }
        case TaskType.GenerateMaterialityAssessmentMetricGroup: {
          this.logger.info('Generating Materiality Metric Group', { jobId: job._id.toString() });
          job.logs.push(createLogEntry('Generating Materiality Metric Group'));
          if (!job.userId) {
            const message = 'User information is missing for GenerateMaterialityAssessmentMetricGroup task';
            job.logs.push(createLogEntry(message));
            throw new ContextError(message, { jobId: job._id.toString(), taskId: taskId.toString() });
          }
          await this.metricGroupService.generateMetricGroupUtrs({ userId: job.userId, job });
          task.status = TaskStatus.Completed;
          job.status = JobStatus.Completed;
          break;
        }
        default: {
          job.logs.push(createLogEntry('Found task that cannot be handled in job'));
          throw new ContextError('Found task that cannot be handled in job', {
            jobId: job._id.toString(),
            taskId: taskId.toString(),
          });
        }
      }
    } catch (error) {
      task.status = TaskStatus.Error;
      job.status = JobStatus.Error;
      job.logs.push(
        createLogEntry(`Failed task ${task.name}. Error: ${error.message}`, { metadata: { cause: error } })
      );
      this.logger.error(error);
    }

    job.markModified('tasks');
    await job.save();

    return result;
  }
}

let instance: MaterialityAssessmentWorkflow;
export const getMaterialityAssessmentWorkflow = () => {
  if (!instance) {
    instance = new MaterialityAssessmentWorkflow(wwgLogger, JobType.MaterialityAssessmentScores, getMaterialityMetricGroupService());
  }
  return instance;
};
