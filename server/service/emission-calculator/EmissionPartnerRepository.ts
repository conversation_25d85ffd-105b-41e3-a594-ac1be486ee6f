/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { User<PERSON>lain } from "../../models/user";

type Integration = {
  type: 'iframe';
  iframe: {
    src: string;
  }
};

export interface EmissionPartner {
  /**
   * Partner code used as slug in urls
   */
  code: string;
  name: string;
  logo: string;
  tags: string[];
  description: string;
  highlights: string[];
  restrictions: string[];
  link: string;
  images?: string[];
  utrCodes?: string[];
  integration?: Integration;
}

export const emissionsUtrCodes = [
  // Just a hand-full so we can test the functionality with limited scope
  'gri/2020/305-1/a',
  'gri/2020/305-2/a',
  'gri/2020/305-3/a',
];


type UserCheckProps = Pick<UserPlain, 'isStaff'>;

export class EmissionPartnerRepository {

  private readonly partnersData: EmissionPartner[] = [
    {
      code: 'unravel-carbon',
      name: 'Unravel Carbon',
      logo: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel-carbon-logo.jpeg',
      link: 'https://www.unravelcarbon.com/',
      tags: [
        'Emissions report',
        'Dashboards',
        'Scope 1, 2 & 3',
        'GHG Protocol aligned'
      ],
      images: [
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel/6413c8a825deb159c4f3b172_01_Hero%20Image%20v2.webp',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel/6413cad5be9e52a5eba5cf21_02_Automated.webp',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel/6413cad59d689f7a7409dcc5_03_Granular%20and%20Actionable.webp',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel/6492643bb818c4b9d7240336_04_Transparent%20and%20Auditable%20v4.png',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel/6413fde60a541a05517a6067_05_Reduce%20v3.webp',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel/6413fde6cf050a5e9681c861_07_Report%20v3.webp',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/unravel/6413cad58a61435dc2f0e923_09_Scope%203%20specialization.webp',
      ],
      description: 'Unravel Carbon provides auditable GH emissions measurement aligned with the Greenhouse Gas Protocol and ISO 14064, certified by TÜV Rheinland, with full traceability of data and information flows for your external assurance. The platform allows you to generate carbon reports using real-time data, automated dynamic commentary, graphs, and insights. Export auditable reports containing relevant information that can be used as part of external disclosures.',
      highlights: [
        'Scope 1,2,3 - spend based method',
        'Dedicated customer support included in cost',
        'Dashboards & detailed GHG emissions report',
        'Data categories match that of accounting software'
      ],
      restrictions: [
        'Excludes extractive industries'
      ],
      utrCodes: emissionsUtrCodes
    },
    {
      code: 'greenly',
      name: 'Greenly',
      logo: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/greenly-logo.jpeg',
      link: 'https://greenly.earth/',
      tags: [
        'All industries',
        'Emissions report',
        'Dashboards',
        'Scope 1, 2 & 3',
        'GHG Protocol aligned'
      ],
      description: 'Greenly is a carbon accounting company that has supported 1,500+ companies measure, manage, and report on their carbon emissions. Through their starter plan (available for organisations with under 20 employees), you can easily get started. Greenly\'s full feature plans include dedicated support from a climate expert and account manager. The platform meets GHG Protocol standards, helping your organisation achieve relevant certifications and align with regulations.',
      highlights: [
        'All industries',
        'Scope 1,2,3 - spend based + activity based',
        'Dashboards & detailed GHG emissions report',
        'Final verification by a climate expert',
        'Account management support',
        'Reduction Plans included, SBTi support (higher plans only)'
      ],
      restrictions: [
        'Some features on higher plans only'
      ],
    },
    {
      code: 'microsoft-sustainability-manager',
      name: 'Microsoft Sustainability Manager',
      logo: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft-logo-transparent-free-png.webp',
      link: 'https://www.microsoft.com/en-us/sustainability/microsoft-sustainability-manager/',
      tags: [
        'Data pipelines',
        'Customizable dashboards',
        'Scope 1, 2 & 3',
      ],
      images: [
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Report_e_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Reduce_b_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Reduce_a_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Report_d_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Report_c_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Report_b_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Report_a_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Record_c_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Record_b_1059x595_2x.avif',
        'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/microsoft/Blade004_Multi_Feature_Record_a_1059x595_2x.avif'
      ],
      description: 'Microsoft Sustainability Manager automates data connections and calculations to help you record, report, and reduce your organization\'s emissions more efficiently. It is designed to help you better manage the constant flow of data from your operations and value chain. Microsoft Sustainability Manager helps you reach your sustainability goals while driving efficiencies, improving cost savings, and reducing energy consumption.',
      highlights: [
        'Scope 1,2,3 - activity based',
        'Support for streamlining data from vast number of sources',
        'Seamless integration options for businesses using MS Azure',
        'High reporting flexibility'
      ],
      restrictions: [
        'Starting cost of US$4,000 per month',
        'Requires deep knowledge of sustainability',
      ],
    },
    {
      code: 'green-project-tech',
      name: 'Green Project Technologies',
      logo: 'https://wwg-cdn.s3.eu-west-2.amazonaws.com/carbon-calculators/green-project-technologies-logo.png',
      link: 'https://www.greenprojecttech.com/',
      tags: [
        'All industries',
        'Emissions report',
        'Dashboards',
        'Scope 1, 2 & 3',
        'GHG Protocol aligned'
      ],
      description: 'Green Project Technologies is the first carbon accounting platform that was purpose built for the private markets and supply chains. They have a client base of over 400 companies in the US, UK, EU, Australia, Africa, and South America have been coined the "QuickBooks of ESG Accounting.',
      highlights: [
        '"Quickbooks" of carbon accounting',
        'Carbon accounting: Scope 1,2,3 emissions',
        'Dashboards and downloadable reports',
        'Carbon savings, targets & tracking',
        'Direct Data Integrations to 12k+ utility providers',
        'Portfolio and Supply Chain Management',
      ],
      restrictions: [
        // 'Some features on higher plans only'
      ],
    },
  ]

  private integrations: Record<string, Integration | undefined> = {
    'green-project-tech': {
      type: 'iframe',
      iframe: {
        src: 'https://app.ab.qa.greenprojecttech.com/dashboard'
      }
    }
  }

  public async getCalculators(user?: UserCheckProps): Promise<EmissionPartner[]> {
    if (!user?.isStaff) {
      return this.partnersData;
    }

    // Staff have other integrations enabled
    return this.partnersData.map(p => ({ ...p, integration: this.getIntegration(p) }));
  }

  private getIntegration(p: EmissionPartner) {
    return this.integrations[p.code];
  }

  public async getPartnerInfo(code: string, user: UserCheckProps): Promise<EmissionPartner | undefined> {
    const partner = this.partnersData.find(p => p.code === code);
    if (partner && user.isStaff) {
      return { ...partner, integration: this.getIntegration(partner) }
    }
    return undefined;
  }
}

let instance: EmissionPartnerRepository;
export const getEmissionPartnerRepository = () => {
  if (!instance) {
    instance = new EmissionPartnerRepository();
  }
  return instance;
}
