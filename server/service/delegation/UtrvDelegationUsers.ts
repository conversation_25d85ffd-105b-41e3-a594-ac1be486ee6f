/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { UniversalTrackerValueModel } from '../../models/universalTrackerValue';
import { ObjectId } from 'bson';
import { SurveyModel } from '../../models/survey';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { getOnboardingManager } from '../onboarding/OnboardingManager';
import { UserRepository } from '../../repository/UserRepository';
import { SafeUser, safeUserFields } from '../../models/user';
import { InitiativePermissions } from '../initiative/InitiativePermissions';
import { UserRoles } from '../user/userPermissions';
import { SurveyUserRoles } from '../survey/SurveyUsers';
import { SurveyPermissions } from '../survey/SurveyPermissions';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { OnboardingStatus } from '../../models/onboarding';

const onboardingManager = getOnboardingManager();

export interface User {
  _id: ObjectId;
  initiativeRoles?: string[];
  surveyRoles?: string[];
  roles?: string[];
}

type Utrv = Pick<UniversalTrackerValueModel, '_id' | 'stakeholders'>;

type Role = SurveyUserRoles.Admin | SurveyUserRoles.Stakeholder | SurveyUserRoles.Verifier;

const initiativeDelegationRoles = [
  ...InitiativePermissions.canContributeRoles,
  ...InitiativePermissions.canVerifyRoles,
  ...InitiativePermissions.canManageRoles,
];

export class UtrvDelegationUsers {
  public async get(survey: Pick<SurveyModel, 'initiativeId' | 'stakeholders'>, utrvIds: string[]) {
    const instance = new UtrvDelegationUsers();
    const initiativeId = survey.initiativeId;

    const initiativeAndParentsIds = await InitiativeRepository.getCurrentAndParentInitiativeIds(survey.initiativeId);
    const initiativeAndParentsStringIds = initiativeAndParentsIds.map((id) => id.toString());
    const initiativeAndParentsUsers = await UserRepository.getUsersInitiativeIds<SafeUser>({
      initiativeIds: initiativeAndParentsIds,
      roles: Object.values(UserRoles),
      projection: safeUserFields,
    });

    // Must not early exit here as we need to check for onboarding users

    const initiativeAndParentsUserIds = initiativeAndParentsUsers.map((user) => user._id.toString());

    const initiativeUsers = await instance.getInitiativeLevel(initiativeAndParentsUsers, initiativeAndParentsStringIds);
    const surveyUsers = await instance.getSurveyLevel(survey, initiativeAndParentsUserIds);
    const utrvsUsers = await instance.getUtrvLevel(
      utrvIds,
      initiativeId,
      initiativeAndParentsUserIds,
      initiativeAndParentsStringIds
    );
    // utrvsUsers must be first to keep the `count` field. TODO: should do that in mergeUsers?
    const users = instance.mergeUsers([...utrvsUsers, ...initiativeUsers, ...surveyUsers]);

    return {
      contributors: instance.filterUsersByRole(users, SurveyUserRoles.Stakeholder),
      verifiers: instance.filterUsersByRole(users, SurveyUserRoles.Verifier),
      admins: instance.filterUsersByRole(users, SurveyUserRoles.Admin),
    };
  }

  private async getInitiativeLevel(initiativeAndParentsUsers: SafeUser[], initiativeAndParentsIds: string[]) {
    return initiativeAndParentsUsers.reduce((delegationUsers, user) => {
      const delegationRoles = user.permissions.reduce((delegationRoles, permission) => {
        if (!initiativeAndParentsIds.includes(permission.initiativeId.toString())) {
          return delegationRoles;
        }

        (permission.permissions ?? []).forEach((role) => {
          if (initiativeDelegationRoles.includes(role)) {
            delegationRoles.add(role);
          }
        });

        return delegationRoles;
      }, new Set<UserRoles>());

      if (delegationRoles.size > 0) {
        delegationUsers.push({ ...user, initiativeRoles: Array.from(delegationRoles) });
      }

      return delegationUsers;
    }, [] as User[]);
  }

  private async getSurveyLevel(
    survey: Pick<SurveyModel, 'stakeholders' | 'roles'>,
    initiativeAndParentsUserIds: string[]
  ) {
    // Need to filter out users at this point to reduce user load.
    const isInitiativeAndParentsUser = (id: ObjectId) => initiativeAndParentsUserIds.includes(id.toString());
    const stakeholderIds = [...(survey.stakeholders.stakeholder || [])].filter(isInitiativeAndParentsUser);
    const verifierIds = [...(survey.stakeholders.verifier || [])].filter(isInitiativeAndParentsUser);
    const adminIds = [...(survey.roles?.admin || [])].filter(isInitiativeAndParentsUser);
    const users = await UniversalTrackerValueRepository.getUtrvStakeholderUsers(stakeholderIds, verifierIds, adminIds);

    return users.map(({ roles, ...user }) => ({
      ...user,
      surveyRoles: roles,
    }));
  }

  private async getUtrvLevel(
    utrvIds: string[],
    initiativeId: ObjectId,
    initiativeAndParentsUserIds: string[],
    initiativeAndParentsIds: string[]
  ) {
    if (utrvIds.length === 0) {
      return [];
    }

    const utrvs: Utrv[] = await UniversalTrackerValueRepository.find(
      { _id: { $in: utrvIds } },
      { _id: 1, stakeholders: 1 }
    );

    if (utrvs.length === 0) {
      return [];
    }

    const stakeholderIds: { [key: string]: number } = {};
    const verifierIds: { [key: string]: number } = {};

    utrvs.forEach((utrv) => {
      utrv.stakeholders?.stakeholder.forEach((userId) => {
        const strUserId = String(userId);
        stakeholderIds[strUserId] = (stakeholderIds[strUserId] ?? 0) + 1;
      });
      utrv.stakeholders?.verifier.forEach((userId) => {
        const strUserId = String(userId);
        verifierIds[strUserId] = (verifierIds[strUserId] ?? 0) + 1;
      });
    });

    const toObjectIds = (ids: { [key: string]: number }) =>
      Object.keys(ids)
        .filter((id) => initiativeAndParentsUserIds.includes(id))
        .map((id) => new ObjectId(id));

    const users = (
      await UniversalTrackerValueRepository.getUtrvStakeholderUsers(
        toObjectIds(stakeholderIds),
        toObjectIds(verifierIds)
      )
    ).map((user) => ({
      ...user,
      count: {
        contributed: stakeholderIds[user._id.toString()],
        verified: verifierIds[user._id.toString()],
      },
    }));

    return [...users, ...(await this.getOnboardingUsers(utrvs, initiativeId, initiativeAndParentsIds))];
  }

  private async getOnboardingUsers(utrvs: Utrv[], initiativeId: ObjectId, initiativeAndParentsIds: string[]) {
    const onboardingUsers = await onboardingManager.findOnboardingUtrvUsers(utrvs, initiativeId);

    return onboardingUsers.filter(
      (onboardingUser) =>
        onboardingUser.status === OnboardingStatus.Pending &&
        onboardingUser.user.permissions.some((p) => initiativeAndParentsIds.includes(p.initiativeId.toString()))
    );
  }

  // Merge the roles and other properties if the user already exists, filtering out duplicates.
  private mergeUsers(users: User[]) {
    return users.reduce((acc, current) => {
      const existingUser = acc.find((u) => u._id.equals(current._id));

      if (!existingUser) {
        acc.push(current);
        return acc;
      }
      existingUser.roles = this.mergeRoles(existingUser.roles, current.roles);
      existingUser.surveyRoles = this.mergeRoles(existingUser.surveyRoles, current.surveyRoles);
      existingUser.initiativeRoles = this.mergeRoles(existingUser.initiativeRoles, current.initiativeRoles);
      return acc;
    }, [] as User[]);
  }

  private mergeRoles(existingRoles: string[] | undefined = [], currentRoles: string[] | undefined = []) {
    return Array.from(new Set([...existingRoles, ...currentRoles]));
  }

  private filterUsersByRole(users: User[], role: Role) {
    return users.filter(this.hasRole(role));
  }

  private hasRole(role: Role) {
    return (user: User) => {
      const keyMap: Record<Role, 'canContributeRoles' | 'canVerifyRoles' | 'canManageRoles'> = {
        [SurveyUserRoles.Stakeholder]: 'canContributeRoles',
        [SurveyUserRoles.Verifier]: 'canVerifyRoles',
        [SurveyUserRoles.Admin]: 'canManageRoles',
      };
      const key = keyMap[role];
      return (
        user.initiativeRoles?.some((r) => InitiativePermissions[key].includes(r as UserRoles)) ||
        user.surveyRoles?.some((r) => SurveyPermissions[key].includes(r as SurveyUserRoles)) ||
        user.roles?.some((r) => role === r)
      );
    };
  }
}

let instance: UtrvDelegationUsers;

export function getUtrvDelegationUsers() {
  if (!instance) {
    instance = new UtrvDelegationUsers();
  }
  return instance;
}