/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

/*
EXPLANATION
  1) User opens modal and selects sizing options, then clicks BUY
  2) We calculate the appropriate productCode based on sizing, then initiate a Stripe Checkout
    a) We attach metadata to the PaymentIntent object (inside the Stripe Checkout object) including userId, effectiveDate, productCode and answers to UTR questions
  3) After completing the payment, user is redirected to asssessment list with sessionId in URL
    a) We poll the the PaymentIntent object inside the Stripe Session until we get a valid surveyId
  4) Stripe sends a `checkout.session.completed` event, which means payment has been successful
    a) MaterialityAssessmentCheckoutSessionHandler processes the event and creates a survey
      i) If successful, it attaches the surveyId to the PaymentIntent of the Stripe Session.
  5) If everything works, the new surveyId will be returned to user on step (3)

WARNING
  - If for some reason the event does not get processed correctly, then survey will not get created, and user has been charged.
    - @TODO Could have a process on frontend to allow a future 'purchase' to reuse that payment.
*/

import Stripe from "stripe";
import { InitiativeWithCustomer } from '../../../models/initiative';
import { materialityTrackerProductCodes, ProductCodes } from "../../../models/customer";
import { CreateSurveyParams, getMaterialityAssessmentManager } from "../../materiality-assessment/MaterialityAssessmentManager";
import { getInnerId, HandleResult, HandlerEvent, HandlerType } from "./HandlerType";
import { LoggerInterface } from "../../wwgLogger";
import { CustomerManager } from "../CustomerManager";
import ContextError from "../../../error/ContextError";
import User from "../../../models/user";
import { CreateSurveyDto, MaterialityAssessmentUtrCodes } from "../../../routes/validation-schemas/materiality-assessment";
import { AssessmentType } from '../../../types/materiality-assessment';

const materialityAssessmentManager = getMaterialityAssessmentManager();

const materialityProducts = Object.values(materialityTrackerProductCodes);
type MaterialityProducts = typeof materialityProducts[number];

export class MaterialityAssessmentCheckoutSessionHandler implements HandlerType {

  private readonly name = 'MaterialityAssessmentCheckoutSessionHandler'

  constructor(
    private logger: LoggerInterface,
    private customerManager: CustomerManager,
  ) {
  }

  shouldHandle(event: HandlerEvent): boolean {
    return [
      "checkout.session.completed",
    ].includes(event.type);
  }

  public async handle(event: HandlerEvent<Stripe.Checkout.Session>): Promise<HandleResult> {

    const eventObject = event.data.object;
    const customer = eventObject.customer;
    if (!customer) {
      return this.createResult([`Failed to process event ${event.type} due to missing customer id`])
    }
    const customerId = typeof customer === 'string' ? customer : customer.id;

    return this.processPaid(event, customerId);
  }

  private async processPaid(event: HandlerEvent<Stripe.Checkout.Session>, customerId: string): Promise<HandleResult> {

    const sessionObject = event.data.object;
    const initiative = await this.customerManager.findInitiativeByCustomerId(customerId);
    if (!initiative) {
      return this.createResult([`Failed to find initiative for "${customerId}"`]);
    }

    switch(sessionObject.mode) {
      case "payment":
        return this.processPaymentEvent(sessionObject, initiative);
      default:
        return this.createResult([`Failed to process event ${sessionObject.id}, unknown mode "${sessionObject.mode}"`]);
    }
  }

  protected async processPaymentEvent(sessionObject: Stripe.Checkout.Session, initiative: InitiativeWithCustomer): Promise<HandleResult> {
    this.logger.info(`MaterialityAssessmentCheckoutSessionHandler Processing payment...`);

    const session = await this.customerManager.retrieveSession(sessionObject.id, [ 'payment_intent' ]);
    const sessionId = session.id;

    const metadata = typeof session.payment_intent === 'object' && session.payment_intent?.metadata ? session.payment_intent.metadata : session.metadata ?? {};

    const debugInfo = {
      sessionId,
      initiativeId: initiative._id.toHexString(),
      metadata,
    };
    const { surveyId, userId, effectiveDate, productCode, assessmentType, ...rest } = metadata;

    if (!productCode || !materialityProducts.includes(productCode as MaterialityProducts)) {
      this.logger.info('Product code is not processable by MaterialityAssessment CheckoutHandler.');
      return this.createResult();
    }

    if (surveyId) {
      this.logger.error(new ContextError('Skipping survey creation due to existing survey', debugInfo));
      return this.createResult([`Skipping survey creation due to existing survey.`]);
    }

    if (!userId || !effectiveDate) {
      this.logger.error(new ContextError('Skipping survey creation due to incomplete metadata', debugInfo));
      return this.createResult([`Skipping survey creation due to incomplete metadata.`]);
    }

    const user = await User.findById(userId).exec();
    if (!user) {
      this.logger.error(new ContextError('Skipping survey creation due to missing user', debugInfo));
      return this.createResult([`Skipping survey creation due to missing user.`]);
    }

    const context = Object.entries(MaterialityAssessmentUtrCodes).reduce(
      (acc, entry) => {
        const [, code] = entry;
        acc[code] = rest[code] ? String(rest[code]) : '';
        return acc;
    }, {} as CreateSurveyDto);

    const surveyParams: CreateSurveyParams = {
      initiative,
      productCode: productCode as ProductCodes,
      effectiveDate: new Date(effectiveDate),
      context,
      user,
      assessmentType: assessmentType as AssessmentType,
    }

    const survey = await materialityAssessmentManager.createSurvey(surveyParams);
    this.logger.info(`MaterialityAssessmentCheckoutSessionHandler created survey ${survey._id}...`);

    if (session.payment_intent && typeof session.payment_intent === 'object') {
      const paymentIntentId = getInnerId(session.payment_intent);
      await this.customerManager.updatePaymentIntentMetadata(paymentIntentId, { ...metadata, surveyId: survey._id.toHexString() });
    }

    await this.customerManager.updateCheckoutSessionMetadata(sessionId, { ...metadata, surveyId: survey._id.toHexString() });
    this.logger.info(`MaterialityAssessmentCheckoutSessionHandler updated stripe line item with surveyId ${survey._id}.`);

    return this.createResult();
  }

  private createResult(errors?: unknown[]) {
    return {
      success: !errors || errors.length === 0,
      name: this.name,
      errors: errors
    };
  }
}
