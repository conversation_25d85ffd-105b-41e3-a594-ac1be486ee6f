/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { ProductCodes } from '../../models/customer';
import { FeatureCode, FeatureDetails, RequiredTags } from '@g17eco/core';
import { ProductBundle } from '../organization/domainConfig';
import { defaultCTEnterpriseFeatures, defaultCTLFeatures, defaultCTProFeatures } from '../app/AppConfig';
import { companyTrackerStarterBundle } from "../app/company-tracker/CTStarter";
import { camelCaseToWords } from "../../util/string";
import ContextError from "../../error/ContextError";
import { CustomScope } from "../../models/customScopeSchema";

export type SubscriptionProductCode = ProductCodes | RequiredTags | BundleCodes;

export enum BundleCodes {
  AllPremium = 'all_premium',
  UserAddon1 = 'user_addon_1',
  CustomMetricAddon10 = 'custom_metric_addon_10',
}

const premiumTags = Object.values(RequiredTags);

const validCodes = new Set([
  ...Object.values(ProductCodes),
  ...Object.values(BundleCodes),
  ...premiumTags,
]);

export const isValidSubscriptionCode = (code: string): code is SubscriptionProductCode => {
  return validCodes.has(code as SubscriptionProductCode)
}

/** Need to provide an override for tags that does not match scope_${type} pattern */
const requireTagsMapping: Record<string, string | undefined> = {
  [RequiredTags.DJSI]: 'sam_csa',
  [RequiredTags.VigeoEiris]: 'vigeo_eiris',
}

const requiredTagToScope = (tag: string) => requireTagsMapping[tag] ?? tag.replace('scope_', '');

/**
 * The order here is important, when resolving bundle product code,
 * first one that is available will be used
 *
 * AllBundle ->  productCodes -> [ scope_bof, scope_cdp, scope_djsi, scope_iogp ...],
 * LVMHBundle -> productCodes -> [scope_djsi, scope_cdp],
 *
 * Searching for scope that support [scope_djsi, scope_cdp] productCodes will
 * select **AllBundle** as it's the first one that meets the criteria,
 * therefore user will be asked to buy "AllBundle" that will be more than needed.
 *
 * Alternative solution is to implement frontend page that will show all available
 * products that included requested products.
 *
 * At the moment we are doing this automatically based on the order of this map
 */
const productScopeCodesMap = new Map<string, ProductBundle | undefined>([
  companyTrackerStarterBundle,
  [
    ProductCodes.SGXESGenome,
    {
      scope: [
        {
          productCode: ProductCodes.SGXESGenome,
          scopeType: 'frameworks',
          code: 'ctl',
          // If you buy a product, it's not mandatory, just allow to use it.
          required: false
        },
      ],
      features: defaultCTLFeatures
    }
  ],
  [
    ProductCodes.CompanyTracker,
    {
      scope: [
        {
          productCode: ProductCodes.CompanyTracker,
          scopeType: 'frameworks',
          code: 'ctl',
          // If you buy a product, it's not mandatory, just allow to use it.
          required: false
        },
      ],
      features: defaultCTLFeatures
    }
  ],
  [
    ProductCodes.CompanyTrackerPro,
    {
      scope: [{ scopeType: 'standards', code: 'gri', required: false }],
      features: defaultCTProFeatures
    }
  ],
  [
    ProductCodes.CompanyTrackerEnterprise,
    {
      scope: [{ scopeType: 'standards', code: 'gri', required: false }],
      features: defaultCTEnterpriseFeatures
    }
  ],
  [
    BundleCodes.AllPremium,
    {
      scope: premiumTags.map(tag => ({
        productCode: tag,
        scopeType: 'standards',
        code: requiredTagToScope(tag),
        required: false
      }))
    }
  ],
  [
    BundleCodes.UserAddon1,
    {
      features: [
        {
          name: 'Users seats',
          code: FeatureCode.Users,
          active: true,
          config: {
            limit: 1,
          }
        }
      ]
    }
  ],
  [
    BundleCodes.CustomMetricAddon10,
    {
      features: [
        {
          name: 'Additional Custom Metrics',
          code: FeatureCode.CustomMetrics,
          active: true,
          config: {
            // Means quantity * limit, or 1 quantity give you 10
            limit: 10,
          }
        }
      ]
    }
  ],
]);

// Add from Required tags, all of them are standards
premiumTags.forEach(tag => {
  productScopeCodesMap.set(tag, {
    scope: [{
      productCode: tag,
      scopeType: 'standards',
      code: requiredTagToScope(tag),
      required: false
    }]
  })
})

const hasScopeMapping = (code: string): code is SubscriptionProductCode => {
  return productScopeCodesMap.has(code);
}

export const mustHaveScopeMapping = (code: string): SubscriptionProductCode => {
  if (!hasScopeMapping(code)) {
    throw new Error(`Product code "${code}" is not supported`);
  }
  return code;
}


export const getSubscriptionCodes = (productCodes: string[]): SubscriptionProductCode[] => {

  const codes: SubscriptionProductCode[] = [];

  for (const [key, mapper] of productScopeCodesMap.entries()) {
    if (!mapper) {
      continue;
    }

    const availableCodes = mapper.scope?.reduce((a, c) => c.productCode ? [...a, c.productCode] : a, [] as string[]);
    if (availableCodes && productCodes.every(code => availableCodes.includes(code))) {
      codes.push(key as SubscriptionProductCode);
    }
  }
  return codes;
}


export const convertProductCodesToCustomScope = (productCodes: string[]): CustomScope[] => {
  return productCodes.reduce((acc, code) => {
    const scope = productScopeCodesMap.get(code)?.scope ?? [];
    return [...acc, ...scope]
  }, [] as CustomScope[]);
}

const adjustFeatureLimit = (current: FeatureDetails, quantity: number) => {
  if (!current.config?.limit) {
    return current;
  }

  // Create features with addons based on quantity
  return {
    name: current.name,
    code: current.code,
    active: true,
    config: {
      ...current.config,
      limit: current.config.limit * quantity,
    }
  };
};

const mergeFeatureDetailsLimit = (current: FeatureDetails, quantity: number) => {

  const newLimit = (current.config?.limit ?? 0) + quantity;

  return {
    name: `${camelCaseToWords(current.code)} - Custom ${newLimit}`,
    code: current.code,
    active: true,
    config: {
      ...current.config,
      limit: newLimit,
    }
  } satisfies FeatureDetails;
};

export const convertProductCodesToFeatures = (code: string, itemQuantity: number | undefined): FeatureDetails[] => {
  const features = productScopeCodesMap.get(code)?.features ?? [];
  if (features.length === 0) {
    return [];
  }

  // Assume it must be one in case it's not set.
  const quantity = itemQuantity ?? 1;

  return features.reduce((acc, current) => {
    acc.push(adjustFeatureLimit(current, quantity))
    return acc;
  }, [] as FeatureDetails[])
}


// We are merging the limits here instead of selecting highest
export const consolidateSubscriptionFeatures = (features: FeatureDetails[]) => {

  const uniqueFeatures = new Map<FeatureCode, FeatureDetails>();
  features.forEach(feature => {
    const currentFeature = uniqueFeatures.get(feature.code);
    if (!currentFeature) {
      uniqueFeatures.set(feature.code, feature);
      return;
    }

    if (feature.config?.limit === undefined && currentFeature.config?.limit === undefined) {
      return; // Nothing to do
    }

    const newLimit = feature.config?.limit ?? 0;
    uniqueFeatures.set(feature.code, mergeFeatureDetailsLimit(currentFeature, newLimit));
  });

  return Array.from(uniqueFeatures.values());
}

export const featureCodeToBundle = (featureCode: string | FeatureCode): BundleCodes => {
  switch (featureCode) {
    case FeatureCode.Users:
      return BundleCodes.UserAddon1;
    case FeatureCode.CustomMetrics:
      return BundleCodes.CustomMetricAddon10;
    default:
      throw new ContextError(`No Bundle is available for feature "${featureCode}"`, {
        featureCode,
      })
  }
}

export const isFeatureCode = (code: unknown): code is FeatureCode => {
  return Object.values(FeatureCode).includes(code as FeatureCode)
}

export const hasRequiredTags = (groupRequiredTags: string[] | undefined): groupRequiredTags is string[] => {
  return Boolean(groupRequiredTags && groupRequiredTags.length > 0);
};

// Need to translate scope tags to scope
export const convertRequiredTagsToScope = (requiredTags: string[]) => requiredTags.map(tag => requiredTagToScope(tag));

export const canAccessCustomScopeGroup = (requiredTags: string[] | undefined, scopeConfig: CustomScope[] | undefined) => {
  if (!hasRequiredTags(requiredTags)) {
    return true;
  }

  if (!scopeConfig) {
    return false;
  }
  return convertRequiredTagsToScope(requiredTags)
    .some(code => scopeConfig.some(customScope => customScope.code === code))
};

export const isProductCode = (productCode: unknown): productCode is ProductCodes => {
  if (!productCode || typeof productCode !== 'string') {
    return false
  }

  return Object.values<string>(ProductCodes).includes(productCode);
}
