/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import Initiative, {
  InitiativeCustomer,
  InitiativeModel,
  InitiativePlain,
  InitiativeWithCustomer,
  PERMISSION_GROUPS,
  Referral,
} from '../../models/initiative';
import { ObjectId } from 'bson';
import { User<PERSON>lain } from '../../models/user';
import <PERSON><PERSON> from 'stripe';
import StripeClient, { fromStripeSubscriptionWithProduct } from './StripeClient';
import {
  getInitiativeRepository,
  InitiativeRepository,
  RootInitiativeData,
  RootInitiativeWithSubscriptionsData,
} from '../../repository/InitiativeRepository';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { getProductManager, ProductManager } from './ProductManager';
import { SubscriptionProductCode } from './subscriptionCodes';
import {
  ProductCodes,
  requireUserInteraction,
  SubscriptionCustomer,
  SubscriptionCustomerPlain,
  Subscription,
  SubscriptionItem,
  SubscriptionStatus,
  allowAccessStatuses,
} from '../../models/customer';
import { DomainConfig } from '../organization/domainConfig';
import UserError from '../../error/UserError';
import ContextError from '../../error/ContextError';
import { AppConfig } from '../app/AppConfig';
import moment from 'moment';
import { HydratedDocument, Types } from 'mongoose';
import { DataShareService, getDataShareService } from '../share/DataShareService';
import { PriceInterval } from '../../types/subscription';
import config from "../../config";

export interface ProductSubscriptionCreate {
  initiative: InitiativeModel;
  user: UserPlain;
  productCode: SubscriptionProductCode;
  quantity?: number;
  promoCode?: string;
  /**
   * In unix timestamp seconds.
   * To create a trial subscription without promotion or default. Ex: integrate CT to MT.
   */
  trialEnd?: number;
}

// One-off purchases
export interface ProducPaymentCreate {
  initiative: InitiativeModel;
  user: Pick<UserPlain, '_id' | 'email'>;
  productCode: SubscriptionProductCode;
  promoCode?: string;
  successUrl: string;
  cancelUrl: string;
  metadata: Stripe.Metadata;
}

interface ProductFakeSubscriptionCreate {
  productCode: string;
  durationInSeconds?: number;
}

interface CheckoutSession {
  initiative: InitiativePlain;
  customer: InitiativeCustomer;
  subscriptions: Subscription[];
  productCode: SubscriptionProductCode;
  domainConfig: DomainConfig | undefined;
  successUrl: string;
  cancelUrl: string;
  userId: string;
  referralCode?: string;
}

interface PriceIdLookup {
  productCode: SubscriptionProductCode;
}

interface ApplyDiscountParams {
  existingSub: Stripe.Subscription;
  promotion: Stripe.PromotionCode;
  initiative: Pick<InitiativeWithCustomer, '_id' | 'customer' | 'name'>;
  productCode: SubscriptionProductCode;
}

export type SubscriptionProrationBehavior = Stripe.InvoiceRetrieveUpcomingParams.SubscriptionProrationBehavior;

interface PreviewInvoiceParams {
  customerId: string;
  subscriptionId: string;
  items: Stripe.InvoiceRetrieveUpcomingParams['invoice_items'];
  prorationDate?: number;
  subscriptionProrationBehavior: SubscriptionProrationBehavior;
}

type PreviewInvoice = Pick<Stripe.UpcomingInvoice, 'currency' | 'total' | 'lines' | 'period_start' | 'period_end'>;

/**
 * Response to interact with Stripe customer subscriptions
 * and create a layer between direct Stripe access.
 */
export class CustomerManager {
  constructor(
    private logger: LoggerInterface,
    private stripe: Stripe,
    private pm: ProductManager,
    private initiativeRepo: InitiativeRepository,
    private dataShareService: DataShareService
  ) {}

  public async delete(initiative: InitiativeModel) {
    if (!initiative.customer) {
      return;
    }

    if (initiative.customer.id !== 'wwg') {
      await this.stripe.customers.del(initiative.customer.id);
    }

    initiative.customer = undefined;
    return SubscriptionCustomer.deleteOne({ initiativeId: initiative._id }).exec();
  }

  public async getSubscriptions(initiativeId: ObjectId) {
    try {
      const subscriptionCustomer = await SubscriptionCustomer.findOne({ initiativeId }, { subscriptions: 1 })
        .lean<Pick<SubscriptionCustomerPlain, 'subscriptions'>>()
        .exec();

      return subscriptionCustomer?.subscriptions ?? [];
    } catch (e) {
      this.logger.error(e);
      return []; // Original behaviour
    }
  }

  public async getSubscriptionsByIds(initiativeIds: ObjectId[]) {
    return SubscriptionCustomer.find({ initiativeId: { $in: initiativeIds } }, { _id: 1, initiativeId: 1, subscriptions: 1 })
      .lean<Pick<SubscriptionCustomerPlain, 'initiativeId' | 'subscriptions'>[]>()
      .exec();
  }

  // This will try to keep the customer inside initiatve and stripe-customer collections in sync while we passively 'migrate'
  public async create(initiative: InitiativeModel, user: Pick<UserPlain, 'email'>): Promise<SubscriptionCustomerPlain> {
    const subscriptionCustomer = await SubscriptionCustomer.findOne({ initiativeId: initiative._id }).lean();
    if (subscriptionCustomer) {
      this.logger.warn(
        new ContextError('Trying to create new stripe customer when one already exists. Early exit', {
          initiativeId: initiative._id.toString(),
          customerId: subscriptionCustomer.customerId,
        })
      );
      return subscriptionCustomer;
    }

    const customer = await this.stripe.customers.create({
      email: user.email,
      description: initiative.description?.slice(0, 350), // Stripe limit 350
      name: initiative.name,
      metadata: {
        initiativeId: initiative._id.toString(),
        appEnv: config.appEnv, // Start tracking where customer was created from
      },
    });

    // Backwards compatibility: Copy to initiative for now
    initiative.customer = {
      id: customer.id,
      subscriptions: [],
    };
    await initiative.save();

    return SubscriptionCustomer.create({
      customerId: customer.id,
      currency: customer.currency ?? undefined,
      initiativeId: initiative._id,
      subscriptions: [],
    });
  }

  public async getInitiativeWithCustomer(
    initiative: InitiativeModel,
    user: Pick<UserPlain, '_id' | 'email'>,
  ): Promise<InitiativeWithCustomer> {
    if (initiative.customer) {
      return initiative as InitiativeWithCustomer;
    }
    await this.create(initiative, user);
    if (!initiative.customer) {
      throw new ContextError('Expected an initiative to have a valid customer', {
        initiativeId: initiative._id.toString(),
        userId: user._id.toString(),
      });
    }
    return initiative as InitiativeWithCustomer;
  }

  public isInitiativeWithCustomer(initiative: InitiativePlain): initiative is InitiativeWithCustomer {
    return initiative.customer !== undefined;
  }

  public async getInitiativeCustomer(initiative: InitiativeModel, user: UserPlain) {
    return this.getInitiativeWithCustomer(initiative, user).then((initiative) => initiative.customer);
  }

  public async findInitiativeByCustomerId(customerId: string) {
    return this.initiativeRepo.findOne({ 'customer.id': customerId }) as Promise<InitiativeWithCustomer | null>;
  }

  private async getOrCreateSubscriptionCustomer(
    initiativeId: Types.ObjectId
  ): Promise<HydratedDocument<SubscriptionCustomerPlain>> {
    const subscriptionCustomer = await SubscriptionCustomer.findOne({ initiativeId }).exec();
    if (subscriptionCustomer) {
      return subscriptionCustomer;
    }

    const initiative: { _id: ObjectId, customer?: InitiativeCustomer } | null = await Initiative.findById(
      initiativeId,
      { _id: 1, customer: 1 }
    ).lean();
    if (!initiative?.customer) {
      throw Error(`Cannot find valid customer for initiative ${initiativeId}`);
    }
    // Legacy subscriptions stored in initiative, so we need to migrate
    // @TODO - remove once all subs are safely refreshed inside the new collection 'stripe-customers

    return SubscriptionCustomer.create({
      initiativeId: initiative._id,
      customerId: initiative.customer.id,
      currency: initiative.customer.currency,
      defaultPaymentMethod: initiative.customer.defaultPaymentMethod,
      subscriptions: initiative.customer.subscriptions,
    });
  }

  public async updateSubscription(
    initiative: Pick<InitiativeWithCustomer, '_id' | 'customer' | 'save'>,
    subscriptionId: string
  ) {
    const subscription = await this.retrieveSubscriptionWithProduct(subscriptionId);
    const sub = fromStripeSubscriptionWithProduct(subscription);

    const subscriptionCustomer = await this.getOrCreateSubscriptionCustomer(initiative._id);
    subscriptionCustomer.subscriptions = this.upsertSubscription(subscriptionCustomer.subscriptions, sub);
    await subscriptionCustomer.save();

    // Backwards compatibility:
    initiative.customer.subscriptions = this.upsertSubscription(initiative.customer.subscriptions, sub);

    const skipKey = this.getSkipDataShareKey();
    const skipTimestampInMs = subscription.metadata?.[skipKey];
    if (skipTimestampInMs) {
      const date = new Date(Number(skipTimestampInMs));
      const diffInMs = Date.now() - date.getTime();
      this.logger.warn(`Skipping data share sync due to "${skipKey}" metadata. Set ${diffInMs}ms ago`, {
        debugMessage: 'This is a solution to avoid data share sync when extending same discount',
        initiativeId: initiative._id,
        subscriptionId: subscription.id,
        customerId: initiative.customer,
        metadata: subscription.metadata,
        skipSyncDate: date.toISOString(),
      });
    } else {
      await this.dataShareService.syncDataSharesToSubscriptions(initiative._id, subscriptionCustomer.subscriptions);
    }

    return initiative.save();
  }

  /**
   * Set the key when transaction to skip started
   */
  public getSkipDataShareKey() {
    return 'skipDataShareSync';
  }

  public async cancelSubscription(
    initiative: Pick<InitiativeWithCustomer, '_id' | 'customer' | 'save'>,
    subscriptionId: string
  ) {
    // Update subscription with by applying promo and ending trial
    await this.stripe.subscriptions.cancel(subscriptionId);
    return this.updateSubscription(initiative, subscriptionId);
  }

  private async addSubscriptionToInitiative(
    initiative: Pick<InitiativeWithCustomer, '_id' | 'customer'>,
    subscription: Subscription
  ) {
    const subscriptionCustomer = await this.getOrCreateSubscriptionCustomer(initiative._id);
    subscriptionCustomer.subscriptions = this.upsertSubscription(subscriptionCustomer.subscriptions, subscription);
    await subscriptionCustomer.save();
    initiative.customer.subscriptions = this.upsertSubscription(initiative.customer.subscriptions, subscription);
  }

  private async syncDataShares(initiativeId: ObjectId) {
    const subscriptions = await this.getSubscriptions(initiativeId);
    return this.dataShareService.syncDataSharesToSubscriptions(initiativeId, subscriptions);
  }

  /**
   * This will instantly end trial and apply promotion
   * otherwise it will create a new subscription and apply promotion,
   * when current subscription status has ended
   */
  public async applyPromotion<T extends Pick<InitiativeWithCustomer, '_id' | 'customer' | 'name' | 'save' | '__v'>>(
    initiative: T,
    productCode: SubscriptionProductCode,
    referral: Required<Pick<Referral, 'code'>>,
    requesterId?: string
  ): Promise<{
    initiative: T;
    stripeSub: Stripe.Subscription;
    promotion: Stripe.PromotionCode;
  }> {
    const { code } = referral;

    const product = await this.pm.getProduct(productCode);
    if (!product) {
      throw new Error(`Failed to find product with metadata productCode="${productCode}"`);
    }

    const [promotion] = await this.getPromotionForProduct({ promoCode: code, productId: product.id });
    if (!promotion) {
      throw new UserError(`Promotion code is not valid '${code}'`);
    }

    const stripeSub = await this.applyPromotionDiscount(initiative, productCode, promotion);
    const subscription = fromStripeSubscriptionWithProduct(stripeSub);
    await this.addSubscriptionToInitiative(initiative, subscription);

    await initiative.save();

    // Start DataShare: If promotion is applied, then related data shares need to be updated too
    if (requesterId) {
      // @TODO: Get rid of this...
      this.syncDataShares(initiative._id);
    }
    // End DataShare

    this.logger.info(`Successfully applied promotion to subscription ${subscription.id}`, {
      initiativeId: initiative._id,
      customerId: initiative.customer.id,
      productCode,
      subscriptionId: subscription.id,
      promoCode: promotion.code,
      percentageOff: promotion.coupon.percent_off,
      initiativeVersion: initiative.__v,
    });

    // Ensure we are saving
    return { initiative, stripeSub, promotion };
  }

  /**
   * Apply promotion or create a new subscription with promotion discount
   *
   * It's relative long function, but we ensure we cover all possible statuses
   * in a single place
   */
  private async applyPromotionDiscount(
    initiative: Pick<InitiativeWithCustomer, '_id' | 'customer' | 'name' | 'save'>,
    productCode: SubscriptionProductCode,
    promotion: Stripe.PromotionCode
  ) {
    const subs = await this.getSubscriptions(initiative._id);

    if (this.getFirstSubscription(productCode, 'active', subs)) {
      throw new UserError(`Active subscription is already available`, {
        initiativeId: initiative._id,
        productCode,
      });
    }

    const existingTrial = this.getFirstSubscription(productCode, 'trialing', subs);
    if (existingTrial) {
      return this.applyTrialDiscount(existingTrial, initiative, productCode, promotion);
    }

    // Check recoverable subscriptions
    const recoverableSub = this.getSubscriptionByProductAndStatus(productCode, requireUserInteraction, subs);
    if (recoverableSub) {
      // Try to recover or create new one and cancel existing
      return this.activateSubscriptionWithFreePromo(recoverableSub, initiative, productCode, promotion);
    }

    // Unrecoverable or does not exist at all, create a new one
    const priceId = await this.getPriceId({ productCode });

    this.logger.info(`Creating new subscription with promotion`, {
      initiativeId: initiative._id,
      customerId: initiative.customer.id,
      productCode,
      priceId,
      promoCode: promotion.code,
      percentageOff: promotion.coupon.percent_off,
    });

    return this.stripe.subscriptions.create({
      customer: initiative.customer.id,
      items: [{ price: priceId, quantity: 1 }],
      promotion_code: promotion.id,
      expand: this.getSubscriptionExpand(),
      payment_behavior: 'default_incomplete',
    });
  }

  private async applyTrialDiscount(
    existingTrial: Subscription,
    initiative: Pick<InitiativeWithCustomer, '_id' | 'name'>,
    productCode: SubscriptionProductCode,
    promotion: Stripe.PromotionCode
  ) {
    if (existingTrial.status !== 'trialing') {
      throw new ContextError(
        `Trying to apply trial for subscription ${existingTrial.id} with status ${existingTrial.status}`,
        {
          initiativeId: initiative._id,
          subscription: existingTrial,
          productCode,
        }
      );
    }

    const subscription = await this.retrieveSubscriptionWithProduct(existingTrial.id);

    if (subscription.discount) {
      throw new UserError(`Subscription already have discount applied`, {
        debugMessage: `Initiative ${initiative.name} subscription ${subscription.id} already have discount applied`,
        initiativeId: initiative._id,
        productCode,
        subscriptionId: subscription.id,
        discountId: subscription.discount.id,
        promoCode: promotion.code,
      });
    }

    // Update subscription with by applying promo and ending trial
    return this.stripe.subscriptions.update(subscription.id, {
      promotion_code: promotion.id,
      trial_end: 'now',
      expand: this.getSubscriptionExpand(),
    });
  }

  /**
   * Take one of the available recoverable subscription and try to activate it
   * with 100% discount promo
   */
  private async activateSubscriptionWithFreePromo(
    subscription: Subscription,
    initiative: Pick<InitiativeWithCustomer, '_id' | 'customer' | 'name' | 'save'>,
    productCode: SubscriptionProductCode,
    promotion: Stripe.PromotionCode
  ): Promise<Stripe.Subscription> {
    const context = {
      initiativeId: initiative._id,
      customerId: initiative.customer.id,
      productCode,
      subscription: subscription,
      promoCode: promotion.code,
      percentageOff: promotion.coupon.percent_off,
    };

    this.logger.info(`Activating subscription with free promo`, context);

    if (!promotion.active || promotion.coupon.percent_off !== 100) {
      throw new UserError(`Unable to apply promotion at this point`, {
        ...context,
        promotionActive: promotion.active,
        debugMessage: `Promotion not active or does not give 100% off`,
      });
    }

    // Need invoice as well, so we can invalidate it
    const existingSub = await this.retrieveSubscriptionWithProduct(subscription.id, ['latest_invoice']);
    const sub = fromStripeSubscriptionWithProduct(existingSub);
    await this.addSubscriptionToInitiative(initiative, sub);

    if (!requireUserInteraction.includes(sub.status)) {
      // Ensure we save the new version, next time it will work
      await initiative.save();
      throw new UserError(`Unable to apply promotion at this point`, {
        ...context,
        subStatus: sub.status,
        debugMessage: `Subscription status no longer requires user interaction, data is out of sync`,
      });
    }
    const updatedSub = await this.applyDiscount({
      existingSub,
      promotion,
      initiative,
      productCode,
    });

    const latestInvoice = updatedSub.latest_invoice;
    if (!latestInvoice) {
      return updatedSub;
    }

    if (typeof latestInvoice !== 'object') {
      throw new UserError(`Unable to apply promotion at this point`, {
        ...context,
        subStatus: updatedSub.status,
        debugMessage: `Failed to expand latest invoice to object`,
      });
    }

    const invoice = await this.actionLastInvoice(updatedSub, latestInvoice);
    this.logger.info('Actioned last invoice', {
      invoiceId: invoice.id,
      status: invoice.status,
      subscriptionId: updatedSub.id,
    });

    return this.retrieveSubscriptionWithProduct(subscription.id);
  }

  private async actionLastInvoice(
    updatedSub: Stripe.Subscription,
    latestInvoice: Stripe.Invoice,
    expand: string[] = []
  ) {
    if (updatedSub.status === 'incomplete') {
      // Marking as uncollectible, might need a different role later
      // Since invoice is finalized, cannot be copied or modified it in any way
      // so the only option is to mark it as void or uncollectible
      // If the subscription is incomplete, and you void the first invoice that’s generated,
      // the subscription updates to incomplete_expired, therefore we must mark it as uncollectible
      return this.stripe.invoices.markUncollectible(latestInvoice.id, { expand });
    }

    return this.voidInvoice(latestInvoice, expand);
  }

  private async applyDiscount({ existingSub, promotion, initiative, productCode }: ApplyDiscountParams) {
    if (!existingSub.discount) {
      return this.stripe.subscriptions.update(existingSub.id, {
        promotion_code: promotion.id,
        expand: [...this.getSubscriptionExpand(), 'latest_invoice'],
      });
    }

    if (existingSub.discount.coupon.id === promotion.coupon.id) {
      // Already have the same one, nothing to do
      return existingSub;
    }

    // Do not allow to swap subscription to different one
    throw new UserError(`Subscription already have different discount applied`, {
      debugMessage: `Initiative ${initiative.name} subscription ${existingSub.id} already have discount applied`,
      initiativeId: initiative._id,
      productCode,
      subscriptionId: existingSub.id,
      discountId: existingSub.discount.id,
      promoCode: promotion.code,
      promotionDiscountId: promotion.coupon,
    });
  }

  public async getTrialPromoCode(productCode: SubscriptionProductCode, subs: Subscription[] | undefined) {
    const existingTrial = this.getFirstSubscription(productCode, 'trialing', subs);
    if (!existingTrial) {
      return;
    }

    const trialSub = await this.retrieveSubscription(existingTrial.id, ['discount.promotion_code']);
    const promo = trialSub.discount?.promotion_code;
    if (typeof promo === 'object' && promo !== null) {
      if (promo.active) {
        return promo.id;
      }
    }
  }

  public async getRemoteSubscriptions(initiative: Pick<InitiativeWithCustomer, '_id' | 'customer'>, options: Partial<Stripe.SubscriptionListParams> = {}) {
    if (initiative.customer.id === 'wwg') {
      throw new ContextError('Trying to retrieve remote subscription for a fake customer', {
        initiativeId: initiative._id,
      });
    }

    return this.stripe.subscriptions.list({
      ...options,
      customer: initiative.customer.id,
      status: 'all',
    });
  }

  public async retrieveSubscription(id: string, expand?: string[]) {
    return this.stripe.subscriptions.retrieve(id, { expand });
  }

  private async retrieveSubscriptionWithProduct(id: string, expand: string[] = []) {
    return this.retrieveSubscription(id, [...this.getSubscriptionExpand(), ...expand]);
  }

  public async retrieveSession(sessionId: string, expand?: string[]) {
    return this.stripe.checkout.sessions.retrieve(sessionId, { expand });
  }

  public async updatePaymentIntentMetadata(paymentIntentId: string, metadata: Stripe.Emptyable<Stripe.MetadataParam>) {
    return this.stripe.paymentIntents.update(paymentIntentId, { metadata });
  }

  public async updateCheckoutSessionMetadata(checkoutSessionId: string, metadata: Stripe.Emptyable<Stripe.MetadataParam>) {
    return this.stripe.checkout.sessions.update(checkoutSessionId, { metadata });
  }

  public async createCheckoutSession(session: CheckoutSession) {
    const { initiative, customer, productCode, successUrl, cancelUrl, userId, referralCode } = session;

    const customerData = await StripeClient.customers.retrieve(customer.id);
    const hasAddress = !customerData.deleted && customerData.address;

    const currency = this.getCustomerCurrency(customerData);
    const priceId = await this.pm.getPriceForCode(productCode);

    const [promotion] = await this.getActivePromotions(referralCode);

    // Skip if we had previous subscriptions.
    // At the moment all trials are for new customer only. This might change in the future.
    const trialEndTimestamp =
      session.subscriptions.length > 0
        ? undefined
        : this.pm.getTrialEndTimestamp({
            code: productCode,
            promoCode: referralCode,
            promotion,
          });

    return StripeClient.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      customer: customer.id,
      currency: currency,
      client_reference_id: initiative._id.toString(),
      line_items: [{ price: priceId, quantity: 1 }],
      success_url: successUrl,
      cancel_url: cancelUrl,
      allow_promotion_codes: true,
      automatic_tax: {
        enabled: true,
      },
      customer_update: {
        // Re-use existing address if possible
        address: hasAddress ? 'never' : 'auto',
      },
      subscription_data: {
        trial_end: trialEndTimestamp,
      },
      metadata: {
        userId: userId,
        initiativeId: initiative._id.toString(),
        referralCode: referralCode ?? null,
      },
    });
  }

  public async createProductSubscription(createData: ProductSubscriptionCreate) {
    const { user, productCode, quantity, promoCode, trialEnd } = createData;

    const initiative = await this.getInitiativeWithCustomer(createData.initiative, user);
    const [promotion] = await this.getActivePromotions(promoCode);

    const priceId = await this.getPriceId({ productCode });

    const subscription = await this.stripe.subscriptions.create({
      customer: initiative.customer.id,
      items: [{ price: priceId, quantity }],
      trial_end: trialEnd ?? this.pm.getTrialEndTimestamp({
        code: productCode,
        promotion,
        promoCode,
      }),
      /**
       * Use default_incomplete to create Subscriptions with status=incomplete
       * when the first invoice requires payment, otherwise start as active.
       * Subscriptions transition to status=active when successfully confirming
       * the payment intent on the first invoice. This allows simpler management
       * of scenarios where additional user actions are needed to pay a subscription’s invoice.
       *
       * Such as failed payments, SCA regulation, or collecting a mandate for a bank debit payment method.
       *
       * If the payment intent is not confirmed within 23 hours subscriptions transition
       * to status=incomplete_expired, which is a terminal state.
       */
      payment_behavior: 'default_incomplete',
      promotion_code: promotion?.id,
      expand: this.getSubscriptionExpand(),
    });

    const sub = fromStripeSubscriptionWithProduct(subscription);
    await this.addSubscriptionToInitiative(initiative, sub);

    await initiative.save();

    return { initiative, subscription, promotion };
  }

  public async createProductPayment(createData: ProducPaymentCreate) {
    const { user, productCode, promoCode, successUrl, cancelUrl, metadata } = createData;

    const initiative = await this.getInitiativeWithCustomer(createData.initiative, user);
    const [promotion] = (await this.getActivePromotions(promoCode)) as [Stripe.PromotionCode | undefined];

    const priceId = await this.getPriceId({ productCode });

    // Create a Checkout Session
    const session = await this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      customer: initiative.customer.id,
      metadata,
      payment_intent_data: {
        metadata,
      },
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      discounts: promotion ? [{ promotion_code: promotion.id }] : [],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
    });

    return { sessionId: session.id, sessionUrl: session.url };
  }

  public createEphemeralProductSubscription(createData: ProductFakeSubscriptionCreate) {
    const { productCode, durationInSeconds = 7 * 24 * 3600 } = createData;

    const startDate = moment().unix();
    const endDate = moment().unix() + durationInSeconds;
    const fakeId = `ephemoral_id_${productCode}`;

    const subscription: Subscription = {
      id: fakeId,
      status: 'active',
      items: [
        {
          id: fakeId,
          priceId: fakeId,
          productId: fakeId,
          productCode: productCode,
          created: startDate,
          quantity: 1,
        },
      ],
      startDate: startDate,
      periodStart: startDate,
      periodEnd: endDate,
      paymentProvider: 'wwg',
    };

    return subscription;
  }

  private getCustomerCurrency(customerData: Stripe.Customer | Stripe.DeletedCustomer): string | undefined {
    if (customerData.deleted) {
      return undefined;
    }
    return customerData.currency ?? undefined;
  }

  private async getPriceId({ productCode }: PriceIdLookup) {
    return this.pm.getPriceForCode(productCode);
  }

  public getPriceById(priceId: string) {
    return this.pm.getPriceById(priceId);
  }

  public getPricesForProductCode(productCode: SubscriptionProductCode, interval: PriceInterval | undefined) {
    if (interval) {
      return this.pm.getIntervalPrice({ productCode, interval });
    }

    return this.pm.getPricesForProductCode({ productCode });
  }

  public async getPromotionForProduct({ productId, promoCode }: { productId: string; promoCode: string }) {
    const promotions = await this.getActivePromotions(promoCode);

    return promotions.filter((promo) => {
      if (!promo.coupon.applies_to) {
        return true; // No restriction
      }

      return promo.coupon.applies_to.products.includes(productId);
    });
  }

  public async getActivePromotions(promoCode: string | undefined): Promise<Stripe.PromotionCode[]> {
    if (!promoCode) {
      return [];
    }

    const promotions = await this.stripe.promotionCodes.list({
      code: promoCode,
      active: true,
    });
    return promotions.data;
  }

  public async getPromotionsByCoupon(params: Pick<Stripe.PromotionCodeListParams, 'coupon' | 'limit'>) {
    return this.stripe.promotionCodes.list(params);
  }

  public async getCoupon(id: string, params?: Stripe.CouponRetrieveParams) {
    return this.stripe.coupons.retrieve(id, params);
  }

  public hasStatusForProduct(
    productCode: SubscriptionProductCode,
    statuses: SubscriptionStatus[],
    subscriptions?: Subscription[]
  ): boolean {
    if (!subscriptions) {
      return false;
    }

    return subscriptions.some((sub) => {
      return statuses.includes(sub.status) && sub.items.some((item) => item.productCode === productCode);
    });
  }

  public getFirstSubscription(
    productCode: SubscriptionProductCode,
    status: SubscriptionStatus,
    subscriptions?: Subscription[]
  ): Subscription | undefined {
    if (!subscriptions) {
      return undefined;
    }

    const subs = subscriptions.filter((sub) => {
      return sub.status === status && sub.items.some((item) => item.productCode === productCode);
    });

    // Sort by periodEnd
    return subs.sort((a, b) => b.periodEnd - a.periodEnd)[0];
  }

  public getSubscriptionByProductAndStatus(
    productCode: SubscriptionProductCode,
    statuses: SubscriptionStatus[],
    subscriptions: Subscription[]
  ) {
    for (const status of statuses) {
      const sub = this.getFirstSubscription(productCode, status, subscriptions);
      if (sub) {
        return sub;
      }
    }
  }

  public getValidSubscriptionByProduct(productCode: SubscriptionProductCode, subscriptions: Subscription[]) {
    return this.getSubscriptionByProductAndStatus(productCode, allowAccessStatuses, subscriptions);
  }

  public static shouldAllowAccess(appConfig: AppConfig, subscriptions?: Subscription[]) {
    if (!subscriptions) {
      // This is not an open app, so you must have some subscription
      return false;
    }

    const validProductCodes: string[] = appConfig.validProductCodes;
    return subscriptions.some((sub) => {
      return (
        ['active', 'trialing'].some((s) => s === sub.status) &&
        sub.items.some((item) => item.productCode && validProductCodes.includes(item.productCode))
      );
    });
  }

  public async getCalculatedSubscriptions(
    rootInitiative: Pick<RootInitiativeData, '_id' | 'permissionGroup'>,
    appConfig: AppConfig | undefined
  ): Promise<Subscription[]> {
    const subscriptions = await this.getSubscriptions(rootInitiative._id);
    return this.getSubscriptionsWithLegacy(rootInitiative, appConfig, subscriptions);
  }

  private getSubscriptionsWithLegacy(
    rootInitiative: Pick<RootInitiativeData, '_id' | 'permissionGroup'>,
    appConfig: AppConfig | undefined,
    subscriptions: Subscription[]
  ): Subscription[] {
    const activeSubscriptions = subscriptions.filter((sub) => ['active', 'trialing'].some((s) => s === sub.status));
    return [...activeSubscriptions, ...this.getLegacySubs(appConfig, activeSubscriptions, rootInitiative)];
  }

  public async hydrateSubscriptions(
    initiatives: RootInitiativeData[],
    appConfigMap: Map<string, AppConfig | undefined>
  ): Promise<RootInitiativeWithSubscriptionsData[]> {
    const ids = initiatives.map((initiative) => initiative._id);
    const stripeSubscriptions = await this.getSubscriptionsByIds(ids);

    const subMap = stripeSubscriptions.reduce((acc, sub) => {
      const key = sub.initiativeId.toString();
      const list = acc.get(key);
      list ? list.push(...sub.subscriptions) : acc.set(key, sub.subscriptions);
      return acc;
    }, new Map<string, Subscription[]>());

    return initiatives.map((rootOrg) => {
      const id = rootOrg._id.toString();
      const appConfig = rootOrg.appConfigCode ? appConfigMap.get(rootOrg.appConfigCode) : undefined;
      const subscriptions = subMap.get(id) ?? [];
      return {
        ...rootOrg,
        calculatedSubscriptions: this.getSubscriptionsWithLegacy(rootOrg, appConfig, subscriptions),
        appConfig: appConfig,
      };
    });
  }

  private getLegacySubs(
    appConfig: AppConfig | undefined,
    stripeSubscriptions: Subscription[],
    rootInitiative: Pick<RootInitiativeData, '_id' | 'permissionGroup'>
  ) {
    const requiredSubscription = appConfig?.productCode;
    if (!requiredSubscription) {
      // Nothing more to do
      return [];
    }

    const validProductCodes: string[] = appConfig.validProductCodes ?? [requiredSubscription];
    const relevantSubscriptions = stripeSubscriptions.filter((sub) => {
      return sub.items.some((item) => item.productCode && validProductCodes.includes(item.productCode));
    });

    if (relevantSubscriptions.length > 0) {
      // There is something in stripeSubscriptions already, so again, nothing more to do.
      return [];
    }

    switch (rootInitiative.permissionGroup) {
      case PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD1:
      case PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD2:
      case PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD3:
        // These permission groups mean they are old companies,
        // and won't have a subscription, so we create a fake one
        return [
          this.createEphemeralProductSubscription({
            productCode: ProductCodes.CompanyTrackerPro,
          }),
        ];

      case PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE_AND_PORTFOLIO_TRACKER:
      case PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE_OLD1:
        // These permission groups mean they are old companies,
        // and won't have a subscription, so we create a fake one
        return [
          this.createEphemeralProductSubscription({
            productCode: ProductCodes.CompanyTrackerEnterprise,
          }),
        ];

      case PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT:
      case PERMISSION_GROUPS.COMPANY_TRACKER_PRO:
      case PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE:
      default:
        return []; // If you have this permissionGroup, you should have a subscription. So this is an error?
    }
  }

  /**
   *  Add or merge update for specific subscription
   */
  public upsertSubscription(subs: Subscription[] = [], subscription: Subscription) {
    // Add if not exists
    const existingProducts = subs.filter((sub) => sub.id === subscription.id);

    if (existingProducts.length > 1) {
      // Clean up duplicates for same subId
      const [, ...duplicates] = existingProducts;
      this.logger.error(`Cleaning up subscription "${subscription.id}" ${duplicates.length} duplicates`);
      subs = subs.filter((sub) => !duplicates.includes(sub));
    }

    const existingProduct = subs.find((sub) => sub.id === subscription.id);
    if (!existingProduct) {
      subs.push(subscription);
      return subs;
    }

    if (this.shouldUpdate(existingProduct, subscription)) {
      return subs.map((s) => (s === existingProduct ? subscription : s));
    }
    this.logger.info(`Skipping subscription update for ${subscription.id}`);
    return subs;
  }

  /**
   * @param existingProduct
   * @param subscription Must be plain javascript object
   */
  private shouldUpdate(existingProduct: Subscription, subscription: Subscription & { toObject?: () => Subscription }) {
    if (existingProduct.id !== subscription.id) {
      throw new Error(
        `Trying to update different subscription id, current ${existingProduct.id} with new ${subscription.id}`
      );
    }

    // Ensure this is plain object for Object.keys to work;
    const plainUpdate = subscription.toObject ? subscription.toObject() : subscription;
    if (subscription.toObject) {
      this.logger.warn(new Error('Received update subscription that is mongoose object'));
    }

    const keys = Object.keys(plainUpdate).filter(
      (k) => !['id', 'items', 'discount', 'discounts'].includes(k)
    ) as (keyof Subscription)[];

    for (const key of keys) {
      if (existingProduct[key] !== plainUpdate[key]) {
        return true;
      }
    }

    // Ensure items are the same length
    if (existingProduct.items.length !== plainUpdate.items.length) {
      return true;
    }

    // Check items
    if (
      plainUpdate.items.some((subItem, key) => {
        // Go through each item property and compare
        return Object.entries(subItem).some((entry) => {
          const [itemKey, value] = entry as [keyof SubscriptionItem, unknown];
          // It does not match, trigger update
          return value !== existingProduct.items[key][itemKey];
        });
      })
    ) {
      return true;
    }

    if ((subscription.discount === undefined) !== (existingProduct.discount === undefined)) {
      return true;
    }

    if (subscription.discount && existingProduct.discount) {
      for (const key of Object.keys(subscription.discount) as (keyof Subscription['discount'])[]) {
        if (existingProduct.discount[key] !== subscription.discount[key]) {
          return true;
        }
      }
    }

    return false;
  }

  public getSubscriptionExpand() {
    return ['items.data.price.product', 'discounts'];
  }

  private async voidInvoice(latestInvoice: Stripe.Invoice, expand: string[] = []) {
    switch (latestInvoice.status) {
      case 'draft':
      case 'open':
      case 'uncollectible':
        return this.stripe.invoices.voidInvoice(latestInvoice.id, { expand });
      default:
        return latestInvoice;
    }
  }

  public getSubscriptionItem(itemId: string) {
    return this.stripe.subscriptionItems.retrieve(itemId);
  }

  public updateSubscriptionItem(itemId: string, params: Stripe.SubscriptionItemUpdateParams) {
    return this.stripe.subscriptionItems.update(itemId, params);
  }

  public updateRemoteSubscription(subscriptionId: string, params: Stripe.SubscriptionUpdateParams) {
    return this.stripe.subscriptions.update(subscriptionId, params);
  }

  public async createItem(item: Stripe.SubscriptionItemCreateParams) {
    return this.stripe.subscriptionItems.create(item);
  }

  public previewInvoice(params: PreviewInvoiceParams): Promise<PreviewInvoice> {
    return this.stripe.invoices.retrieveUpcoming({
      customer: params.customerId,
      subscription: params.subscriptionId,
      subscription_items: params.items,
      subscription_proration_date: params.prorationDate,
      subscription_proration_behavior: params.subscriptionProrationBehavior,
    } as Stripe.InvoiceRetrieveUpcomingParams);
  }

  public getRemoteCustomer(customerId: string) {
    return this.stripe.customers.retrieve(customerId);
  }

  public addDefaultInvoicePaymentMethod(data: { customerId: string; paymentMethodId: string }) {
    return this.stripe.customers.update(data.customerId, {
      invoice_settings: {
        default_payment_method: data.paymentMethodId,
      },
    });
  }

  public deleteRemoteSubscriptionDiscount(subscriptionId: string) {
    return this.stripe.subscriptions.deleteDiscount(subscriptionId);
  }
}

let instance: CustomerManager;
export const getCustomerManager = () => {
  if (!instance) {
    instance = new CustomerManager(
      wwgLogger,
      StripeClient,
      getProductManager(),
      getInitiativeRepository(),
      getDataShareService()
    );
  }
  return instance;
};
