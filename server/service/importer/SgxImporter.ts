/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { Excel, getExcel } from "../file/Excel";
import SgxQuestionCodes from './SgxQuestionCodes.json';
import Initiative, { InitiativeMetadata, InitiativeModel } from "../../models/initiative";
import moment from "moment";
import { SurveyModelMinData, SurveyType } from "../../models/survey";
import { SurveyScope } from "../survey/SurveyScope";
import { createSurveyCode } from "../../util/string";
import { DefaultBlueprintCode } from "../../survey/blueprints";
import { DataPeriods, UtrvType } from "../utr/constants";
import { SurveyImporter } from "../survey/SurveyImporter";
import { createStakeholderGroup } from "../stakeholder/StakeholderGroupManager";
import { UserPlain } from "../../models/user";
import { DataImporter, getDataImporter } from "../survey/DataImporter";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { UniversalTrackerRepository } from "../../repository/UniversalTrackerRepository";
import { MetricUnitManager } from "../units/MetricUnitManager";
import { customDateFormat, DateFormat } from "../../util/date";
import { UniversalTrackerModel } from "../../models/universalTracker";
import { SurveyQuery } from "../survey/SurveyQuery";

export interface MappingImporterSgxOptions {
  sheetName: string;
  filePath: string;
}

interface mapRow {
  QuestionCode: string
  ColumnCode: string
  ValueListOptionCode: string
}
interface excelRow {
  esg: string
  ric: string
  code: string
  company: string
  description: string
  'type/fx': string
  units: string
  year: number
  value: string | number
  remarks: string
}

interface ImportRow {
  QuestionCode: string
  OptionCode: string
  Value: string | number
  Comment?: string
}

interface ImportSurveyData {
  effectiveDate: string;
  sourceName?: string;
}

interface ImportInitiative {
  metadata: InitiativeMetadata
}

export class SgxImporter {

  constructor(
    protected excel: Excel,
    protected dataImporter: DataImporter,
    protected logger: LoggerInterface
  ) {
  }

  skippedErrors: string[] = []
  cannotMapInfo: string[] = []

  // Keys are from mapping file, values from frontend src/utils/units.ts
  unitMapping: { [key: string]: string } = {
    'People': '',
    'Injuries': '',
    'Hours': '',
    'Incidents': '',
    'NM': '',
    'GJ': 'GJ',
    'KJ': 'kJ',
    'MJ': 'mJ',
    'kWhs': 'kWh',
    'GWhs': 'GWh',
    'MWhs': 'MWh',
    'm3': 'm3',
    'm³': 'm3',
    'tCO2e': 'mt',
    'tonnes': 't'
  };

  public async sgxImport(options: MappingImporterSgxOptions, user: UserPlain) {

    const {
      sheetName = "Data",
      filePath,
    } = options;

    const dataJson = await this.getExcelData(filePath, sheetName)

    const questionMapByDesc = this.getQuestionMapByDesc()

    const utrCodes = Array.from(questionMapByDesc.values()).map(el => el.QuestionCode.trim())

    const utrs = await UniversalTrackerRepository.find({ code: { $in: utrCodes } })
    const utrsByCode = utrs.reduce((acc, current) => {
      acc[current.code] = current
      return acc
    }, {} as { [key: string]: UniversalTrackerModel })

    const { initiativeList, surveyRows } = this.aggregateByInitiativeYear(dataJson, questionMapByDesc, utrsByCode)

    // initiativeList = [{code, name}]
    const initiativeFound = await this.findInitiatives(initiativeList)

    let imported = 0

    // surveyRows = [initiative code => [year =>  [ survey utrvs ]]
    for (const [initiativeKey, row] of surveyRows) {
      // loop initiative surveys
      for (const [year, data] of row) {
        const initiative = initiativeFound.get(initiativeKey);
        if (initiative === undefined) {
          const msg = `${initiativeKey} Initiative not loaded/created skipping, check db metadata ric and code`
          this.skippedErrors.push(msg)
          this.logger.error(msg)
          continue
        }
        try {
          const survey = await this.createSurvey(
            initiative,
            { effectiveDate: year.toString() + '-12-31' },
            user
          )

          const updateRequest = await this.dataImporter.process({ data, survey, user, utrs })
          const utrValues = await this.dataImporter.updateActionRequests(updateRequest, user)

          this.logger.info(
            'Created/Updated utrVs',
            { utrV: utrValues.length, code: initiativeKey, year, surveyId: survey._id.toString() }
          )
          imported += utrValues.length

        } catch (e) {
          this.skippedErrors.push(e.message);
        }
      }
    }

    return { imported: imported, errors: [...this.skippedErrors, ...this.cannotMapInfo] }
  }

  private aggregateByInitiativeYear(dataJson: excelRow[], questionCodes: Map<string, mapRow>, utrsBycode: { [key: string]: UniversalTrackerModel }) {
    const outputData = new Map<string, Map<number, ImportRow[]>>()
    const initiativeList = new Map<string, ImportInitiative>()
    //group by initiative (company) / year
    dataJson.forEach((row) => {
      const questionCodeMap = questionCodes.get(row.description)
      if (questionCodeMap === undefined) {
        // many questions we cannot map them but are present in the spreadsheet, keeping msg in case of actual mapping problem
        const msg = 'Could not map question codes for: ' + row.year + ' ' + row.description
        this.cannotMapInfo.push(msg)
        this.logger.info(msg)
        return
      }
      if (!utrsBycode[questionCodeMap.QuestionCode]) {
        throw Error('Could not find utr for: ' + questionCodeMap.QuestionCode)
      }

      const initiativeKey = this.generateInitiativeKey(row.ric, row.code)
      initiativeList.set(
        initiativeKey,
        { metadata: { sgx_ric: row.ric, sgx_code: row.code } }
      )

      let importRow: ImportRow = {
        QuestionCode: questionCodeMap.QuestionCode,
        // numeric value list has column code empty and valuelist option code filled
        OptionCode: questionCodeMap.ColumnCode !== "" ? questionCodeMap.ColumnCode : questionCodeMap.ValueListOptionCode,
        Value: this.getActualValue(row, questionCodeMap, utrsBycode[questionCodeMap.QuestionCode]),
        Comment: row.remarks,
      }
      this.appendImportRow(outputData, initiativeKey, row.year, importRow)

    });
    return { initiativeList: initiativeList, surveyRows: outputData }
  }

  private getActualValue(row: excelRow, questionCodeMap: mapRow, utr: UniversalTrackerModel) {
    if (row["type/fx"] == 'Percent') {
      return Math.round(+row.value * 100) //js floating point 56.0000001 issue
    }
    if (row["type/fx"] == 'Numeric' && row.value) {
      const actualUnit = this.getUnitMapping(row.units)
      if (actualUnit === '') {
        // some unit in the sheet are just numbers like People
        return row.value
      }

      if (!utr.unit) {
        throw Error(`Utr ${utr.code} does not have a unit specified`)
      }
      const value = typeof row.value === "number" ? row.value : parseFloat(row.value);
      return MetricUnitManager.convertUnit(value, actualUnit, utr.unit)
    }

    //Data for this point is in Boolean 1 or 0 form. if value = 1, then select the valueList option code "sgx53-assurance1"
    if (row.value === 1 && row.description === "CSR Report, Assurances, External SRA") {

      return questionCodeMap.ValueListOptionCode
    }
    return row.value
  }

  getUnitMapping(sheetUnit: string) {
    if (this.unitMapping[sheetUnit] === undefined) {
      throw Error(`Missing unit config for ${sheetUnit}`)
    }
    return this.unitMapping[sheetUnit]
  }

  // outputData = [intiativeCode => [year: [importRow]]]
  // append importRow to outputData it if already set or initialize empty maps
  private appendImportRow(
    outputData: Map<string, Map<number, ImportRow[]>>,
    initiativeKey: string,
    year: number,
    importRow: ImportRow
  ) {
    const initiativeSurveyData = outputData.get(initiativeKey) ?? new Map<number, ImportRow[]>()
    const surveyData = initiativeSurveyData.get(year) ?? []
    surveyData.push(importRow)
    initiativeSurveyData.set(year, surveyData)
    outputData.set(initiativeKey, initiativeSurveyData)
  }

  private async createSurvey(initiative: InitiativeModel, survey: ImportSurveyData, user: UserPlain) {

    // YYYY-MM-DD or actual ISO
    const simpleFormat = 'YYYY-MM-DD'
    const effectiveDate = survey.effectiveDate.length === simpleFormat.length ?
      moment.utc(survey.effectiveDate, simpleFormat).endOf('day').toDate() :
      moment.utc(survey.effectiveDate).toDate();

    const type = SurveyType.Default
    const period = DataPeriods.Yearly;
    const createData: SurveyModelMinData = {
      scope: { ...SurveyScope.createEmpty(), standards: ['sgx_metrics', 'sgx_extended'], frameworks: ['ctl'] },
      visibleUtrvs: [],
      code: createSurveyCode(initiative.code),
      name: undefined,
      sourceName: survey.sourceName ?? DefaultBlueprintCode,
      period,
      effectiveDate: effectiveDate,
      utrvType: UtrvType.Actual,
      unitConfig: SurveyImporter.createUnitConfig(initiative),
      initiativeId: initiative._id,
      type,
      visibleStakeholders: [],
      stakeholders: createStakeholderGroup(),
      evidenceRequired: true,
      verificationRequired: false,
    };

    const activeSurveys = await SurveyQuery.findActiveBy({
      initiativeId: createData.initiativeId,
      effectiveDate: createData.effectiveDate,
      sourceName: createData.sourceName,
      type,
      period,
    });

    if (activeSurveys.length > 0) {
      throw Error(
        `Skipping existing survey for ${period} ${initiative.name} ${customDateFormat(
          createData.effectiveDate,
          DateFormat.YearMonth
        )}`
      );
    }
    return await SurveyImporter.create(createData, user);
  }


  private async getExcelData(filePath: string, sheetName: string) {
    const excel = await this.excel.readFile(filePath)
    const dataSheet = excel.Sheets[sheetName]
    if (!dataSheet) {
      throw Error(`Sheet ${sheetName} doesn't exist in ${filePath}`)
    }
    const dataJson: excelRow[] = this.excel.sheetToJson(dataSheet)
    return dataJson
  }

  private generateInitiativeKey(ric: string, code: string) {
    return ric.toLowerCase() + '/' + code.toLowerCase()
  }

  private async findInitiatives(initiatives: Map<string, ImportInitiative>) {
    const data = Array.from(initiatives.values())
    const initiativeMap = new Map<string, InitiativeModel>()
    const querys = data.map(el => {
      return Initiative.findOne(
        {
          $and: [{
            "metadata.sgx_ric": { $eq: el.metadata.sgx_ric }
          }, {
            "metadata.sgx_code": { $eq: el.metadata.sgx_code }
          }
          ]
        }).then((result => {
          if (!result || !el.metadata.sgx_ric || !el.metadata.sgx_code) {
            return;
          }
          initiativeMap.set(this.generateInitiativeKey(el.metadata.sgx_ric, el.metadata.sgx_code), result)
        }))
    })
    await Promise.all(querys)
    return initiativeMap
  }

  private getQuestionMapByDesc() {
    const questionMapByDesc = new Map<string, mapRow>()
    SgxQuestionCodes.forEach((el) => {
      questionMapByDesc.set(el.description, {
        QuestionCode: el.QuestionCode,
        ColumnCode: el.ColumnCode,
        ValueListOptionCode: el["valuelist option Code"]
      })
    })
    return questionMapByDesc
  }
}


let instance: SgxImporter;
export const getSgxMappingImporter = () => {
  if (!instance) {
    instance = new SgxImporter(
      getExcel(),
      getDataImporter(),
      wwgLogger
    );
  }
  return instance;
}
