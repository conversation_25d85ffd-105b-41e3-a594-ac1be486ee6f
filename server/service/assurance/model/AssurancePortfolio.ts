/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Document, Types } from "mongoose";
import { SurveyModelPlain } from '../../../models/survey';
import { InitiativePlain } from '../../../models/initiative';
import { OrganizationPlain } from '../../../models/organization';
import { AssurancePermissionType, AssurancePortfolioPermission } from '../../../models/assurancePermission';
import { UserPlain } from '../../../models/user';
import { DocumentPlain } from '../../../models/document';
import { UniversalTrackerValueAssurancePlain } from './Assurance';
import { UniversalTrackerValuePlain } from '../../../models/universalTrackerValue';
import { RootInitiativeDataMin } from '../../../repository/InitiativeRepository';

export enum AssuranceDocumentType {
  BasisOfReporting = 'basis_of_reporting',
  ManagementStatement = 'management_statement',
  AssuranceStatement = 'assurance_statement',
  EvidenceBundle = 'evidence_bundle',
}

export enum AssurancePortfolioAction {
  UpdateDocuments = 'update_documents',
  RemoveDocuments = 'remove_documents',
  Completed = 'completed',
  BundleStart = 'bundle_start',
  BundleComplete = 'bundle_complete',
  BundleFailed = 'bundle_failed',
  DeletePortfolio = 'delete_portfolio',
}

export enum AssurancePortfolioStatus {
  Created = 'created',
  Pending = 'pending',
  Processing = 'processing',
  Deleted = 'deleted',
  Completed = 'completed',
}

export interface AssuranceDocument<T = Types.ObjectId> {
  documentId: T;
  type: string;
}

export interface History<T = Types.ObjectId> {
  _id?: T;
  created?: Date;
  action: string;
  userId: T;
  utrvHistorySum?: number;
  documents?: AssuranceDocument[];
}

export interface AssurancePortfolioCommon<T = Types.ObjectId> {
  title?: string;
  description?: string;
  initiativeId: T;
  surveyId?: T;
  organizationId?: T;
  portfolioType: string;
}

export interface AssurancePortfolioCreateBody<T = Types.ObjectId> extends AssurancePortfolioCommon<T> {
  assurancePrimaryContactId: string;
}

export type AssurancePortfolioUpdateBody = Omit<AssurancePortfolioCreateBody,
  'initiativeId' | 'surveyId'>

export interface AssurancePortfolioCreate<T = Types.ObjectId> extends AssurancePortfolioCommon<T> {
  permissions: AssurancePermissionType<AssurancePortfolioPermission>[],
  status: AssurancePortfolioStatus;
  history?: History<T>[];
  documents?: AssuranceDocument[],
}

export interface AssurancePortfolioPlain<T = Types.ObjectId> extends AssurancePortfolioCreate<T> {
  _id: T;
  history: History<T>[];
  documents: AssuranceDocument[],
  created: Date;
}

export type AssurancePortfolioModel = AssurancePortfolioPlain & Document;

export interface AssurancePortfolioExpanded extends AssurancePortfolioModel {
  survey?: Pick<SurveyModelPlain,
    '_id' | 'code' | 'name' | 'sourceName' | 'effectiveDate' | 'deletedDate' | 'roles' | 'visibleUtrvs' | 'verificationRequired' | 'initiativeId'
  >;
  initiative?: Pick<InitiativePlain, '_id' | 'name' | 'tags' | 'appConfigCode'>;
  organization?: Pick<OrganizationPlain, '_id' | 'name' | 'organizationType' | 'partnerTypes' | 'profile'>;
}

export interface AssurancePortfolioExpandedExtra extends AssurancePortfolioExpanded {
  historyDocuments?: DocumentPlain[];
  assurers?: UserPlain[];
  universalTrackerValueAssurances: UniversalTrackerValueAssurancePlain[];
}

export interface AssurancePortfolioAssurer<T = Types.ObjectId> {
  _id?: T;
  firstName?: string;
  surname?: string;
  email?: string;
  active: boolean;
  isAdmin: boolean;
  isRestrictedUser: boolean;
  isAssurer: boolean;
}

export interface AssuranceListPortfolioMin<T = Types.ObjectId> {
  _id: T;
  status: AssurancePortfolioStatus;
  description: string;
  documents: { documentId: T; type: AssuranceDocumentType }[];
  universalTrackerValueAssurances: UniversalTrackerValueAssurancePlain[];
  universalTrackerValues: Pick<UniversalTrackerValuePlain, '_id' | 'universalTrackerId' | 'initiativeId' | 'status'>[];
  survey: Pick<SurveyModelPlain, '_id' | 'name' | 'effectiveDate' | 'deletedDate'>;
  initiative: RootInitiativeDataMin;
  assurers: AssurancePortfolioAssurer[];
}
