/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { FileStorageInterface, getStorage } from '../storage/fileStorage';
import Document, { DocumentModel, DocumentOwnerType } from '../../models/document';
import { parseFileExif } from '../file/ExifParser';
import { wwgLogger } from '../wwgLogger';
import { v4 as uuid } from 'uuid';
import { allowedDocumentTypes } from '../file/constants';
import UserError from '../../error/UserError';
import { isNumber } from '../../util/number';
import { ObjectId } from 'bson';

export interface UploadRequest {
  userId: ObjectId | string;
  ownerId: string;
  type: string;
  data: any;
  filesDescriptions?: string[];
  ownerType?: DocumentOwnerType;
}

export interface EvidenceUploaderInterface {
  uploadFiles(request: UploadRequest, files: Express.Multer.File[]): Promise<DocumentModel[]>;
}

export default class EvidenceUploader implements EvidenceUploaderInterface {
  private storage: FileStorageInterface;

  constructor(storage: FileStorageInterface) {
    this.storage = storage;
  }

  public async uploadFiles(request: UploadRequest, files: Express.Multer.File[] = []) {
    if (!Array.isArray(files)) {
      return [];
    }

    const requests: Promise<any>[] = [];
    const filesDescriptions: string[] = request.filesDescriptions ?? [];

    files.forEach((file, index) => requests.push(this.uploadFile(file, request, filesDescriptions[index] ?? '')));

    return Promise.all(requests);
  }

  public async uploadFile(
    file: any,
    request: Omit<UploadRequest, 'data' | 'filesDescriptions'>,
    description: string | undefined = ''
  ): Promise<DocumentModel> {
    const { type, ownerId, userId, ownerType } = request;
    const extension = this.storage.getExtensionFromMimeType(file.mimetype);

    if (!allowedDocumentTypes.includes(this.storage.getContentTypeFromMimeType(file.mimetype))) {
      wwgLogger.info(`User tried to upload an evidence of type ${file.mimetype}.`);
      throw new UserError(`Files of type ${file.mimetype} are not supported.`);
    }

    const uploadPath = `evidence/${type}/${ownerId}/${uuid()}.${extension}`;

    const isPublic = false;
    const result = await this.storage.upload(file.path, uploadPath, file.mimetype, isPublic);

    const exif = await this.loadExifData(file);

    const param = {
      ownerId: new ObjectId(ownerId),
      metadata: {
        name: file.originalname,
        mimetype: file.mimetype,
        extension: extension,
        exif,
      },
      path: result.path,
      size: file.size,
      userId: new ObjectId(userId),
      public: isPublic,
      description,
      ownerType,
    };

    const model = new Document(param);

    return model.save();
  }

  private async loadExifData(file: any) {
    if (!file.mimetype.startsWith('image')) {
      return undefined;
    }

    try {
      const { tags } = (await parseFileExif(file.path)) ?? {};
      if (!tags) {
        return undefined;
      }

      if (tags.GPSLatitude && !isNumber(tags.GPSLatitude)) {
        wwgLogger.warn('GPSLatitude is not number', { value: tags.GPSLatitude });
      }

      if (tags.GPSLongitude && !isNumber(tags.GPSLongitude)) {
        wwgLogger.warn('GPSLongitude is not number', { value: tags.GPSLongitude });
      }

      return {
        ...tags,
        GPSLatitude: isNumber(tags.GPSLatitude) ? tags.GPSLatitude : undefined,
        GPSLongitude: isNumber(tags.GPSLongitude) ? tags.GPSLongitude : undefined,
      };
    } catch (e) {
      wwgLogger.error(e);
    }
  }
}

export const createEvidenceUploader = () => new EvidenceUploader(getStorage());

