/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { IpStackResult, getInfo, ExtendedIpLocation } from './IpStackApi';
import config from '../../config';

const cache = require('memory-cache');
const shortTTL = 1000 * 60 * 60; // 1h

const ipCache = new cache.Cache();

export const isExtendedLocation = (location: IpStackResult): location is ExtendedIpLocation => {
  return 'postalCode' in location
}

export const getLocationInfo = async (ip?: string): Promise<IpStackResult> => {

  if (!ip) {
    return { ip: '' };
  }

  const cachedData = ipCache.get(ip);
  if (cachedData) {
    return cachedData;
  }

  const isLocalhost = ip === '127.0.0.1' || ip === '::1' || ip === 'localhost';
  if (isLocalhost) {
    return { ip: ip };
  }
  const locationData = await getInfo(ip);

  const ttl = locationData.latitude ? config.service.ipStack.cache.TTL : shortTTL;
  ipCache.put(ip, locationData, ttl);

  return locationData;
};
