import { ESRSMappingItem } from "./ESRSMappingList";
import { UniversalTrackerPlain } from "../../../models/universalTracker";
import { UniversalTrackerValuePlain } from "../../../models/universalTrackerValue";
import { UtrValueType } from "../../../models/public/universalTrackerType";

export interface XBRLMapping {
  [key: string]: ESRSMappingItem | undefined;
}

type BaseUtrv = Pick<UniversalTrackerValuePlain, '_id' | 'effectiveDate' | 'value' | 'valueData' | 'status'>;
export interface UtrvData extends BaseUtrv {
  universalTracker: UniversalTrackerPlain;
}

export function getData(
  factName: string,
  mapping: XBRLMapping,
  utrvData: UtrvData[],
  fallback = ''
): string | number {
  const item = mapping[factName];
  if (!item) return fallback;
  const utrv = utrvData.find((x) => x.universalTracker.code === item.utrCode);
  if (!utrv) return fallback;
  if (!item.valueListCode) return utrv.value ?? fallback;
  switch (utrv.universalTracker.valueType) {
    case UtrValueType.Table: {
      const firstRow = utrv.valueData?.table?.[0];
      const value = firstRow?.find((col) => col.code === item.valueListCode)?.value;
      return Array.isArray(value) ? value.join(', ') : value ?? fallback;
    }
    case UtrValueType.Text: {
      return utrv.valueData?.data ?? fallback;
    }
    case UtrValueType.NumericValueList:
    case UtrValueType.TextValueList:
      return utrv.valueData?.data?.[item.valueListCode] ?? fallback;
    default:
      return fallback;
  }
}

type StringDataParams = {
  factName: string,
  mapping: XBRLMapping,
  utrvData: UtrvData[],
  fallback?: string
};

export const getStringData = (params: StringDataParams) => {
  return String(getData(
    params.factName,
    params.mapping,
    params.utrvData,
    params.fallback ?? '-'
  ))
}
