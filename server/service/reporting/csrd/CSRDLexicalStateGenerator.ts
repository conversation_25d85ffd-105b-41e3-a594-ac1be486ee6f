/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from "../../wwgLogger";
import { SurveyModelPlain } from "../../../models/survey";
import { UserPlain } from "../../../models/user";
import { InitiativePlain } from "../../../models/initiative";
import { getDefaultESRSMappingList } from "./ESRSMappingList";
import UniversalTrackerValue from "../../../models/universalTrackerValue";
import { universalTrackerLookup } from "../../../repository/utrvAggregations";
import { excludeSoftDeleted } from "../../../repository/aggregations";
import { createHeadlessEditor } from "@lexical/headless";
import { HeadingNode } from "@lexical/rich-text";
import { ListItemNode, ListNode } from "@lexical/list";
import { $getRoot, type CreateEditorArgs } from "lexical";
import { UtrvData, XBRLMapping } from "./util";
import { IXBRLNode } from "../lexical/nodes/IXBRLNode";
import { XbrlTracker } from "../XbrlTracker";
import { SectionData } from "../xhtml/csrdTypes";

// Import section builders
import { buildIntro } from "./intro";
import { buildE1Section } from "./sections/E1";
import { buildE2Section } from "./sections/E2";
import { buildE3Section } from "./sections/E3";
import { buildE4Section } from "./sections/E4";
import { buildE5Section } from "./sections/E5";
import { buildS1Section } from "./sections/S1";
import { buildS2Section } from "./sections/S2";
import { buildS3Section } from "./sections/S3";
import { buildS4Section } from "./sections/S4";
import { buildG1Section } from "./sections/G1";
import { buildGeneralInformation } from "./sections/general";
import { buildEnvironmentalInformation } from "./sections/environmental/environmental";

interface GenerateLexicalStateParams {
  initiative: Pick<InitiativePlain, '_id' | 'name'>;
  survey: SurveyModelPlain;
  mapping: XBRLMapping;
  preview?: boolean;
  user: Pick<UserPlain, '_id'>;
}


const editorConfig: CreateEditorArgs = {
  onError: (error: Error) => {
    wwgLogger.error(error);
  },
  theme: {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      overflowed: 'editor-text-overflowed',
      underline: 'editor-text-underline',
    },
    ixbrlNode: 'ixbrl-plugin-node',
  },
  namespace: 'ReportEditor',
  nodes: [
    ListNode,
    ListItemNode,
    HeadingNode,
    IXBRLNode,
  ],
};

export class CSRDLexicalStateGenerator {
  constructor(
    private logger: LoggerInterface,
  ) {}

  private getDefaultMapping(overrides: XBRLMapping): XBRLMapping {
    return getDefaultESRSMappingList().reduce((acc, item) => {
      if (!acc[item.factName]) {
        acc[item.factName] = {
          factName: item.factName,
          utrCode: item.utrCode,
          valueListCode: item.valueListCode,
        }
      }
      return acc;
    }, overrides)
  }

  public async generateCSRDLexicalState(params: GenerateLexicalStateParams) {
    const { initiative, survey } = params;
    this.logger.info(`Generating Lexical state for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const utrvData = await this.getUtrvData(survey);
    const mapping = this.getDefaultMapping(params.mapping);

    const tracker = new XbrlTracker();
    const sectionData: SectionData = {
      initiative,
      mapping,
      utrvData,
      tracker,
    };

    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    editor.update(() => {
      $getRoot().append(
        ...buildGeneralInformation(sectionData),
        ...buildIntro(sectionData),
        ...buildEnvironmentalInformation(sectionData),
        ...buildE1Section(sectionData),
        ...buildE2Section(sectionData),
        ...buildE3Section(sectionData),
        ...buildE4Section(sectionData),
        ...buildE5Section(sectionData),
        ...buildS1Section(sectionData),
        ...buildS2Section(sectionData),
        ...buildS3Section(sectionData),
        ...buildS4Section(sectionData),
        ...buildG1Section(sectionData),
      );
    }, { discrete: true });

    return editor.getEditorState().toJSON();
  }

  private async getUtrvData(survey: SurveyModelPlain) {
    const aggregations = [
      {
        $match: {
          _id: { $in: survey.visibleUtrvs },
          ...excludeSoftDeleted(),
        },
      },
      universalTrackerLookup,
      {
        $project: {
          _id: 1,
          value: 1,
          valueData: 1,
          status: 1,
          effectiveDate: 1,
          universalTracker: {
            $arrayElemAt: ['$universalTracker', 0]
          },
        }
      },
    ];
    return UniversalTrackerValue.aggregate<UtrvData>(aggregations).exec();
  }
}

let instance: CSRDLexicalStateGenerator;
export const getCSRDLexicalStateGenerator = () => {
  if (!instance) {
    instance = new CSRDLexicalStateGenerator(
      wwgLogger,
    );
  }
  return instance;
}

