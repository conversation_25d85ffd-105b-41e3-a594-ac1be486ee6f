import { getData } from "../util";
import { LexicalNode } from "lexical/LexicalNode";
import { $createHeadingNode } from "@lexical/rich-text";
import { $createParagraphNode, $createTextNode } from "lexical";
import { SectionData } from "../../xhtml/csrdTypes";


export function buildE4Section(sectionData: SectionData): LexicalNode[] {
  const { mapping, utrvData } = sectionData;
  const heading = $createHeadingNode('h2');
  heading.append($createTextNode('[E4-3] Actions and resources related to biodiversity and ecosystems'));

  const para = $createParagraphNode();
  para.append(
    $createTextNode(
      String(getData('esrs:DisclosureOfBiodiversityAndEcosystemsrelatedActionsAndResourcesAllocatedToTheirImplementationExplanatory', mapping, utrvData, ''))
    )
  );

  // Add more nodes as needed for facts, tables, etc.

  return [heading, para];
}
