/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { SectionData } from "../../xhtml/csrdTypes";
import { $createHeadingNode } from "@lexical/rich-text";
import { $createParagraphNode, $createTextNode } from "lexical";

export function buildS3Section(sectionData: SectionData) {
  const nodes = [];

  const titleNode = $createHeadingNode('h2');
  titleNode.append($createTextNode('🧑‍🤝‍🧑 Social Standards (S-Series)'));
  nodes.push(titleNode);

  const subtitleNode = $createHeadingNode('h3');
  subtitleNode.append($createTextNode('ESRS S3 – Affected Communities'));
  nodes.push(subtitleNode);

  const introNode = $createParagraphNode();
  introNode.append(
    $createTextNode('This section covers disclosures related to the entity\'s impact on local communities, including community engagement, indigenous rights, and societal contributions.')
  );
  nodes.push(introNode);

  // Additional content can be added here based on specific requirements

  return nodes;
}
