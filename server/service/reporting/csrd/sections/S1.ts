/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { SectionData } from "../../xhtml/csrdTypes";
import { $createHeadingNode } from "@lexical/rich-text";
import { $createParagraphNode, $createTextNode } from "lexical";

export function buildS1Section(sectionData: SectionData) {
  const nodes = [];

  const titleNode = $createHeadingNode('h2');
  titleNode.append($createTextNode('🧑‍🤝‍🧑 Social Standards (S-Series)'));
  nodes.push(titleNode);

  const subtitleNode = $createHeadingNode('h3');
  subtitleNode.append($createTextNode('ESRS S1 – Own Workforce'));
  nodes.push(subtitleNode);

  const introNode = $createParagraphNode();
  introNode.append(
    $createTextNode('This section covers disclosures related to the entity\'s own workforce, including employment practices, working conditions, equal opportunities, and employee well-being.')
  );
  nodes.push(introNode);

  // Additional content can be added here based on specific requirements

  return nodes;
}
