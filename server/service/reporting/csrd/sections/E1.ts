import { $createTextNode } from "lexical";
import { $createHeadingNode } from "@lexical/rich-text";
import { LexicalNode } from "lexical/LexicalNode";
import { SectionData } from "../../xhtml/csrdTypes";

export function buildE1Section(_sectionData: SectionData): LexicalNode[] {
  const heading = $createHeadingNode('h1');
  heading.append($createTextNode('🌿 ESRS E1 – Climate Change'));
  return [heading];
}
