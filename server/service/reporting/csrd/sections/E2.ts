import { $createTextNode } from "lexical";
import { $createHeadingNode } from "@lexical/rich-text";
import { LexicalNode } from "lexical/LexicalNode";
import { SectionData } from "../../xhtml/csrdTypes";

export function buildE2Section(_sectionData: SectionData): LexicalNode[] {
  const heading = $createHeadingNode('h1');
  heading.append($createTextNode('🌿 ESRS E2 – Pollution'));
  return [heading];
}
