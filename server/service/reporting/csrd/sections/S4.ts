/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { SectionData } from "../../xhtml/csrdTypes";
import { $createHeadingNode } from "@lexical/rich-text";
import { $createParagraphNode, $createTextNode } from "lexical";

export function buildS4Section(sectionData: SectionData) {
  const nodes = [];

  const titleNode = $createHeadingNode('h2');
  titleNode.append($createTextNode('🧑‍🤝‍🧑 Social Standards (S-Series)'));
  nodes.push(titleNode);

  const subtitleNode = $createHeadingNode('h3');
  subtitleNode.append($createTextNode('ESRS S4 – Consumers and End-Users'));
  nodes.push(subtitleNode);

  const introNode = $createParagraphNode();
  introNode.append(
    $createTextNode('This section covers disclosures related to the entity\'s relationships with consumers and end-users, including product safety, consumer information, and responsible marketing practices.')
  );
  nodes.push(introNode);

  // Additional content can be added here based on specific requirements

  return nodes;
}
