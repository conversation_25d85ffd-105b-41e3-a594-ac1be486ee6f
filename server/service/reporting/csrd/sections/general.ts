/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { SectionData } from "../../xhtml/csrdTypes";
import { LexicalNode } from "lexical/LexicalNode";
import { $createHeadingNode } from "@lexical/rich-text";
import { $createParagraphNode, $createTextNode } from "lexical";

export function buildGeneralInformation(_sectionData: SectionData): LexicalNode[] {
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🏢 1. General Information'));

  const overviewParagraph = $createParagraphNode();
  overviewParagraph.append(
    $createTextNode(
      'In this section, we provide an overview of our organization\'s sustainability context, including our business model, strategy, and the processes in place for identifying and managing sustainability-related impacts, risks, and opportunities.'
    )
  );

  const subHeading1 = $createHeadingNode('h3');
  subHeading1.append($createTextNode('1.1 Business Model and Strategy'));

  const paragraph1 = $createParagraphNode();
  paragraph1.append(
    $createTextNode(
      'Our business model focuses on delivering innovative solutions while minimizing environmental impact and promoting social responsibility. Sustainability is integrated into our core strategy, guiding decision-making and long-term planning.'
    )
  );

  const subHeading2 = $createHeadingNode('h3');
  subHeading2.append($createTextNode('1.2 Sustainability Governance'));

  const paragraph2 = $createParagraphNode();
  paragraph2.append(
    $createTextNode(
      'We have established a Sustainability Committee responsible for overseeing ESG initiatives, ensuring compliance with relevant standards, and fostering a culture of continuous improvement.'
    )
  );

  return [
    sectionHeading,
    overviewParagraph,
    subHeading1,
    paragraph1,
    subHeading2,
    paragraph2,
  ];
}
