/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { wwgLogger } from '../wwgLogger';
import { AuditFilterOptions, AuditService } from './AuditService';
import { AuditRepository } from '../../repository/AuditRepository';
import type { Actor, ActorType } from './AuditModels';
import {
  fromRequest,
  getContext,
  getDebugContext,
  operationFromRequest
} from '../../middleware/audit/contextMiddleware';
import { AuditEvent, CreateAuditEntry } from './AuditModels';
import { UserPlain } from '../../models/user';
import { ObjectId } from 'bson';
import { SurveyModelPlain } from '../../models/survey';
import { AuthenticatedRequest } from '../../http/AuthRouter';
import { AuditLookup } from './AuditLookup';
import { LEVEL } from '../event/Events';
import { generatedUUID } from '../crypto/token';
import { Request } from 'express';
import { InitiativePlain } from '../../models/initiative';
import { UniversalTrackerPlain } from "../../models/universalTracker";
import { MetricGroupPlain } from "../../models/metricGroup";
import { SurveyUpdateProps } from "../survey/SurveyManager";
import { unitConfigHasChanged } from "../survey/surveyChange";
import { UserApiKeyPlain } from "../../models/userApiKey";

interface SaveAuditData extends Partial<CreateAuditEntry> {
  targets: Actor[];
  auditEvent: AuditEvent;
  user?: Pick<UserPlain, '_id' | 'oktaUserId'>;
  debugData?: Record<string, unknown>;
}

type SystemCreate = Pick<CreateAuditEntry, 'debugContext' | 'outcome' | 'message'>;

interface SystemAuditEntry extends Partial<SystemCreate> {
  req: Request,
  initiativeId: ObjectId;
  actor: Actor;
  targets: Actor[];
  auditEvent: AuditEvent;
  severity?: LEVEL;
}

type ToActor = { _id: ObjectId, name?: string, code?: string };

type UtrCompare = Record<string, string | undefined>;

export class AuditLogger {

  private readonly system = {
    userAgent: 'G17Eco System',
  }

  constructor(
    private auditService: AuditService,
    private lookupService: AuditLookup,
  ) {
  }

  public createSystem(auditEntry: SystemAuditEntry) {

    const { req, auditEvent, debugContext, outcome } = auditEntry;
    const method = req.method.toLowerCase();
    const requestId = generatedUUID();

    return this.auditService.add({
      debugContext: debugContext ?? getDebugContext(req, requestId),
      actor: auditEntry.actor,
      client: {
        userAgent: { rawUserAgent: this.system.userAgent },
        ipAddress: req.ip,
      },
      operation: operationFromRequest(method),
      severity: auditEntry.severity ?? method === 'delete' ? LEVEL.NOTICE : LEVEL.INFO,
      transaction: { id: requestId, type: 'WEB' },
      service: auditEvent.service,
      outcome: outcome ?? { result: 'SUCCESS' },
      eventDate: new Date(),
      event: auditEvent.code,
      message: auditEntry.message ?? auditEvent.name,
      initiativeId: auditEntry.initiativeId,
      targets: auditEntry.targets,
    })
  }

  public buildMessage(message: string, vars?: string | [string]) {
    if (!vars) {
      return message
    }
    if (!Array.isArray(vars)) {
      return message.replace('%s', vars)
    }
    return vars.reduce((result, replace) => replace ? result.replace('%s', replace) : result, message)
  }

  public getPreferredSurveyName(survey: SurveyModelPlain, initiative?: InitiativePlain) {
    if (!survey.name) {
      const initiativeName = initiative?.name
      const date = survey.effectiveDate.getUTCFullYear() + '-' + survey.effectiveDate.getUTCMonth()
      return initiativeName ? initiativeName + ' ' + date : date
    }
    return survey.name
  }

  public fromContext(outcomeData: SaveAuditData) {

    const { event, auditEvent, message, outcome, user, ...contextUpdate } = outcomeData;
    const context = getContext();

    const auditEntry: CreateAuditEntry = {
      ...context,
      ...contextUpdate,
      service: auditEvent.service,
      outcome: outcome ?? { result: 'SUCCESS' },
      eventDate: outcomeData.eventDate ?? new Date(),
      event: auditEvent.code,
      message: message ?? auditEvent.name ?? '',
    }

    if (outcomeData.debugData) {
      auditEntry.debugContext = {
        debugData: { ...context.debugContext?.debugData, ...outcomeData.debugData },
      }
    }

    return this.auditService.add(auditEntry)
  }

  public async fromRequest(req: AuthenticatedRequest, outcomeData: SaveAuditData) {

    const {
      event,
      auditEvent,
      message,
      outcome,
      debugData,
      user,
      ...contextUpdate
    } = outcomeData;

    const context = await fromRequest(req);
    const auditEntry: CreateAuditEntry = {
      ...context,
      ...contextUpdate,
      service: auditEvent.service,
      outcome: outcome ?? { result: 'SUCCESS' },
      eventDate: outcomeData.eventDate ?? new Date(),
      event: auditEvent.code,
      message: message ?? auditEvent.name ?? '',
    }

    if (debugData) {
      auditEntry.debugContext = {
        debugData: { ...context.debugContext.debugData, ...debugData },
      }
    }

    return this.auditService.add(auditEntry)
  }


  private createActor(type: ActorType, base: ToActor): Actor {
    return {
      id: base._id,
      type,
      alternateId: base.code,
      displayName: base.name,
    }
  }

  public initiativeTarget(initiative: ToActor): Actor {
    return this.createActor('Initiative', initiative);
  }

  public metricGroupTarget(metricGroup: Pick<MetricGroupPlain, '_id' | 'groupName'>): Actor {
    return this.createActor('MetricGroup', { _id: metricGroup._id, name: metricGroup.groupName });
  }

  public utrTarget(utr: Pick<UniversalTrackerPlain, '_id' | 'code' | 'name'>): Actor {
    return this.createActor('UniversalTracker', utr);
  }

  public userTarget(user: UserPlain): Actor {
    return this.createActor('User', {
      _id: user._id,
      name: this.getName(user),
      code: user.oktaUserId
    });
  }

  public userApiKeyTarget(apiKey: Pick<UserApiKeyPlain, '_id' | 'name' | 'shortToken'>): Actor {
    return this.createActor('UserApiKey', {
      _id: apiKey._id,
      name: apiKey.name,
      code: apiKey.shortToken
    });
  }

  private getName(user: { firstName?: string, surname?: string }) {
    return `${user.firstName ?? ''} ${user.surname ?? ''}`.trim();
  }

  public surveyTarget(initiative: SurveyModelPlain) {
    return this.createActor('Survey', initiative);
  }

  public utrvTarget(utrv: ToActor) {
    return this.createActor('UniversalTrackerValue', utrv);
  }

  public async findForInitiative(initiativeId: string | ObjectId, filters: AuditFilterOptions) {
    const logs = await this.auditService.getAuditForInitiative(new ObjectId(initiativeId), filters);
    return this.lookupService.addLookupInfo(logs)
  }

  debugUtrChange({ before, after }: { before: UniversalTrackerPlain; after: UniversalTrackerPlain }) {

    const original = this.utrCompareProps(before);
    const updated = this.utrCompareProps(after);

    // Only comparing strings at this point and will always have matching keys
    return Object.keys(original).reduce((diff, key) => {
      if (original[key] !== updated[key]) {
        diff.before[key] = original[key];
        diff.after[key] = updated[key];
      }
      return diff
    }, { before: {}, after: {} } as { before: UtrCompare, after: UtrCompare })
  }

  public utrCompareProps(utr: UniversalTrackerPlain): UtrCompare {
    return {
      name: utr.name,
      valueLabel: utr.valueLabel,
      instructions: utr.instructions,
      typeCode: utr.typeCode,
      valueType: utr.valueType,
      unitType: utr.unitType,
      unit: utr.unit,
    }
  }

  public getMetricGroupDebugData(metricGroup: MetricGroupPlain) {
    return {
      type: metricGroup.type,
      groupName: metricGroup.groupName,
      groupData: metricGroup.groupData,
      description: metricGroup.description,
      universalTrackers: metricGroup.universalTrackers,
      share: metricGroup.share,
    };
  }

  debugSettingsChange({ before, after }: { before: SurveyUpdateProps; after: SurveyUpdateProps }) {

    // Need to get a plain object to ensure we can do comparison correctly,
    // otherwise this can cause memory leak, due logger trying to serialise
    // mongoose SingleSet object
    const plainBefore = typeof before.toObject === 'function' ? before.toObject() : before;
    const plainAfter = typeof after.toObject === 'function' ? after.toObject() : before;

    const original = this.geSurveySettingsData(plainBefore);
    const updated = this.geSurveySettingsData(plainAfter);

    // Only comparing strings at this point and will always have matching keys
    return Object.keys(original).reduce((diff, key) => {

      const k = key as keyof SurveyUpdateProps;
      if (key === 'unitConfig') {
        if (unitConfigHasChanged({ before: original[key], after: updated[key] })) {
          diff.before[k] = original[k];
          diff.after[k] = updated[k];
        }
        return diff;
      }

      if (original[k] !== updated[k]) {
        diff.before[k] = original[k];
        diff.after[k] = updated[k];
      }

      return diff
    }, { before: {}, after: {} } as { before: Record<string, unknown>, after: Record<string, unknown> })
  }

  geSurveySettingsData(survey: SurveyUpdateProps): SurveyUpdateProps {
    return {
      evidenceRequired: survey.evidenceRequired,
      isPrivate: survey.isPrivate,
      name: survey.name,
      period: survey.period,
      sourceName: survey.sourceName,
      unitConfig: survey.unitConfig,
      verificationRequired: survey.verificationRequired
    }

  }
}

let instance: AuditLogger;
export const getAuditLogger = () => {
  if (!instance) {
    instance = new AuditLogger(
      new AuditService(wwgLogger, new AuditRepository()),
      new AuditLookup(),
    );
  }
  return instance;
}
