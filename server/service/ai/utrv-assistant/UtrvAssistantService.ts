import { Logger } from 'winston';
import { wwgLogger } from '../../wwgLogger';
import { UtrvPromptInput } from './types';
import { UtrvAssistantPromptGenerator } from './UtrvAssistantPromptGenerator';
import { AIModelFactory, AIModelType, getAIModelFactory } from '../AIModelFactory';
import { zodResponseFormat } from 'openai/helpers/zod';
import { utrvAssistantResponseDto } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { DEFAULT_MAX_TOKEN } from './constants';
import { ChatGPT } from '../models/ChatGPT';

export type AIUtrvSuggestion = {
  predictedAnswer?: string | number | { [key: string]: string | number | string[] };
  questionExplanation: string;
  bestPractice: string[];
  keyInfo: string[];
  suggestedEvidence: {
    primaryDocumentation: string[];
    supportingDocumentation: string[];
  };
  whereToFind: {
    externalSource: string[];
    internalSource: string[];
  };
};

export const fallbackSuggestion: AIUtrvSuggestion = {
  predictedAnswer: '',
  questionExplanation: '',
  bestPractice: [],
  keyInfo: [],
  suggestedEvidence: {
    primaryDocumentation: [],
    supportingDocumentation: [],
  },
  whereToFind: {
    externalSource: [],
    internalSource: [],
  },
};

export class UtrvAssistantService {
  constructor(private logger: Logger, private aiModelFactory: AIModelFactory) {}

  private getModel() {
    return this.aiModelFactory.getModel(AIModelType.ChatGPT) as ChatGPT;
  }

  public async getResponse(input: UtrvPromptInput): Promise<AIUtrvSuggestion> {
    const prompt = new UtrvAssistantPromptGenerator(input).generatePrompt();
    const responseFormat = zodResponseFormat(utrvAssistantResponseDto, 'utrvAssistantExtraction');
    const response = await this.getModel().parseCompletion<AIUtrvSuggestion>(
      [prompt],
      DEFAULT_MAX_TOKEN,
      responseFormat
    );
    return response.content || fallbackSuggestion;
  }
}

let instance: UtrvAssistantService | undefined;
export const getUtrvAssistantManager = () => {
  if (!instance) {
    instance = new UtrvAssistantService(wwgLogger, getAIModelFactory());
  }
  return instance;
};
