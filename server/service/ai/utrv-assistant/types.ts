import { SupportedMeasureUnits } from '../../units/unitTypes';
import { DataPeriods } from '../../utr/constants';
import { TableColumn, UtrValueType } from '../../../models/public/universalTrackerType';

export type SupportedValueType =
  | UtrValueType.Number
  | UtrValueType.Percentage
  | UtrValueType.Text
  | UtrValueType.NumericValueList
  | UtrValueType.TextValueList
  | UtrValueType.Table;

export interface MultiInputData extends Pick<TableColumn, 'code'> {
  value: number | string | string[] | undefined;
}

export type UtrvInputData = string | number | MultiInputData[] | undefined;

export type UtrvReferenceData = {
  effectiveDate: string;
  inputData: UtrvInputData;
};

export type UtrvPromptInput = {
  title: string;
  type: string;
  period: DataPeriods;
  effectiveDate: string;
  valueType: SupportedValueType;
  unitType?: SupportedMeasureUnits;
  numberScale?: string;
  unit?: string;
  industry?: string;
  furtherExplanation?: string;
  instructions?: string;
  columns?: Pick<TableColumn, 'code' | 'name'>[];
  previousUtrvs: UtrvReferenceData[];
};

type MultiRowTableContext = Record<string, string | number | undefined>[];

export interface AdditionalContext {
  inputData?: MultiRowTableContext;
};
