import { ColumnType, UtrValueType } from '../../models/public/universalTrackerType';
import { UniversalTrackerValueListPlain } from '../../models/universalTracker';
import { AIUtrvSuggestion } from './utrv-assistant/UtrvAssistantService';
import { RowData, ValueData } from '../../models/public/universalTrackerValueType';
import { isNumericString } from '../../util/string';
import { isEmpty } from 'lodash';

interface GetValueDataParams {
  utr: UniversalTrackerValueListPlain;
  predictedAnswer: AIUtrvSuggestion['predictedAnswer'];
}

export class PredictedDataResolver {
  private getNumberData({ predictedAnswer }: GetValueDataParams) {
    const value = isNumericString(predictedAnswer) ? Number(predictedAnswer) : undefined;
    return {
      value,
      valueData: {
        input: {
          value,
        },
      },
    };
  }

  private getTextData({ predictedAnswer }: GetValueDataParams) {
    const isTextData = typeof predictedAnswer === 'string' || typeof predictedAnswer === 'number';
    const inputData = isTextData ? String(predictedAnswer) : undefined;
    return {
      value: undefined,
      valueData: {
        data: inputData,
        input: {
          data: inputData,
        },
      },
    };
  }

  private getNumericValueListData({ utr, predictedAnswer }: GetValueDataParams) {
    if (typeof predictedAnswer !== 'object' || !utr.valueListOptions) {
      return {
        value: undefined,
        valueData: undefined,
      };
    }

    const options = utr.valueListOptions.options;
    const { value, inputData } = Object.entries(predictedAnswer).reduce(
      (acc, [code, value]) => {
        const hasCode = options.some((o) => o.code === code);
        if (hasCode && isNumericString(value)) {
          acc.inputData[code] = Number(value);
          acc.value = (acc.value ?? 0) + Number(value);
        }
        return acc;
      },
      { value: undefined as number | undefined, inputData: {} as Record<string, number | undefined> }
    );

    return {
      value,
      // prevent empty object to make unnecessary update
      valueData: isEmpty(inputData)
        ? undefined
        : {
            data: inputData,
            input: {
              value,
              data: inputData,
            },
          },
    };
  }

  private getTextValueListData({ utr, predictedAnswer }: GetValueDataParams) {
    if (typeof predictedAnswer !== 'object' || !utr.valueListOptions) {
      return {
        value: undefined,
        valueData: undefined,
      };
    }

    const options = utr.valueListOptions.options;
    const inputData = Object.entries(predictedAnswer).reduce((acc, [code, value]) => {
      const hasCode = options.some((o) => o.code === code);
      if (hasCode) {
        acc[code] = String(value);
      }
      return acc;
    }, {} as Record<string, string>);

    return {
      value: undefined,
      // prevent empty object to make unnecessary update
      valueData: isEmpty(inputData)
        ? undefined
        : {
            data: inputData,
            input: {
              data: inputData,
            },
          },
    };
  }

  // Single row table or Multi row table is not matter
  // Because we only use predicted answer for the first row in the table
  private getTableData({ utr, predictedAnswer }: GetValueDataParams) {
    if (typeof predictedAnswer !== 'object') {
      return {
        value: undefined,
        valueData: undefined,
      };
    }

    const valueLists = utr.tableColumnValueListOptions;
    const columns = utr.valueValidation?.table?.columns;
    const inputTable = Object.entries(predictedAnswer).reduce((acc, [code, value]) => {
      if (typeof value !== 'string' && typeof value !== 'number') {
        return acc;
      }

      const column = columns?.find((col) => col.code === code);
      if (column) {
        const list = column.listId ? valueLists?.find((l) => l._id.equals(column.listId)) : undefined;
        // if column is a value list column,
        // then find an option have either name or code with predicted value
        // if not found then fallback to the first option of list when column is required
        if (list) {
          const existOption = list.options.find((o) => o.code === value || o.name === value);
          const isRequiredColumn = column.validation?.required;
          if (existOption || isRequiredColumn) {
            acc.push({ code, value: existOption?.code ?? list.options[0]?.code });
          }
          return acc;
        }

        // if column is a plain text or number
        if(column.type === ColumnType.Number) {
          acc.push({ code, value: isNumericString(value) ? Number(value): undefined });
        } else {
          acc.push({ code, value: String(value) });
        }
        return acc;
      }
      return acc;
    }, [] as RowData[]);

    return {
      value: undefined,
      // prevent empty object to make unnecessary update
      valueData: isEmpty(inputTable)
        ? undefined
        : {
            table: [inputTable],
            input: {
              table: [inputTable],
            },
          },
    };
  }

  public getValueDataPredictedAnswer(
    params: GetValueDataParams
  ): { value: number | undefined; valueData: ValueData | undefined } {
    const { utr } = params;

    switch (utr.valueType) {
      case UtrValueType.Number:
      case UtrValueType.Percentage:
        return this.getNumberData(params);
      case UtrValueType.Text:
        return this.getTextData(params);
      case UtrValueType.NumericValueList:
        return this.getNumericValueListData(params);
      case UtrValueType.TextValueList:
        return this.getTextValueListData(params);
      case UtrValueType.Table:
        return this.getTableData(params);
      default:
        return {
          value: undefined,
          valueData: undefined,
        };
    }
  }
}

let instance: PredictedDataResolver;
export const getPredictedDataResolver = () => {
  if (!instance) {
    instance = new PredictedDataResolver();
  }
  return instance;
};
