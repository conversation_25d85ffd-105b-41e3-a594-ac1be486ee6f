/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { ClaudeAI, getClaudeAI } from './models/ClaudeAI';
import { ChatGPT, getChatGPT } from './models/ChatGPT';

let instance: AIModelFactory | undefined;

export enum AIModelType {
  ChatGPT = 'chatgpt',
  Claude = 'claude',
}

export class AIModelFactory {
  constructor(
    protected logger: Logger,
    private aiService: {
      chatgpt: ChatGPT,
      claude: ClaudeAI,
    }
  ) {}

  getModel(model: AIModelType) {
    switch (model) {
      case AIModelType.Claude:
        return this.aiService.claude;
      case AIModelType.ChatGPT:
      default:
        return this.aiService.chatgpt;
    }
  }
}

export const getAIModelFactory = () => {
  if (!instance) {
    instance = new AIModelFactory(
      wwgLogger,
      {
        chatgpt: getChatGPT(),
        claude: getClaudeAI(),
      }
    );
  }
  return instance;
};
