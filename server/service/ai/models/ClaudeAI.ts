/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { AIModel, AIPrompt, AIResponse } from './AIModel';
import { Logger } from 'winston';
import config from '../../../config';
import { wwgLogger } from '../../wwgLogger';
import UserError from '../../../error/UserError';
import Anthropic from '@anthropic-ai/sdk';

const CLAUDE_MODEL = 'claude-3-5-sonnet-20240620';
const TOKEN_LIMIT = 4096;

export class ClaudeAI implements AIModel {
  private claude = new Anthropic({
    apiKey: config.ai.claude.apiKey,
  });

  constructor(private logger: Logger) {}

  private async query(system: string, messages: Anthropic.Messages.MessageParam[], maxTokens = 1000) {
    try {
      return this.claude.messages.create({
        model: CLAUDE_MODEL,
        max_tokens: Math.min(TOKEN_LIMIT, maxTokens),
        temperature: 1,
        system,
        messages,
      });
    } catch (e) {
      throw new UserError('Unable to communicate with <PERSON><PERSON><PERSON>. Please try again later', { messages, cause: e });
    }
  }

  public async runCompletion(messages: AIPrompt[], maxTokens?: number): Promise<AIResponse> {
    const userMessages = messages.filter((m) => m.role === 'user') as Anthropic.Messages.MessageParam[];
    const systemMessages = messages.filter((m) => m.role === 'system');
    const system = systemMessages.map((m) => m.content).join('\n');

    const response = await this.query(system, userMessages, maxTokens);
    const content = response.content[0].type === 'text' ? response.content[0] : { text: '' };

    return {
      content: content.text,
      usage: {
        completion_tokens: response.usage.output_tokens,
        prompt_tokens: response.usage.input_tokens,
        total_tokens: response.usage.output_tokens + response.usage.input_tokens,
      }
    };
  }

  public getModelVersion(): string {
    return CLAUDE_MODEL;
  }
}

let instance: ClaudeAI;
export const getClaudeAI = () => {
  if (!instance) {
    instance = new ClaudeAI(wwgLogger);
  }
  return instance;
}
