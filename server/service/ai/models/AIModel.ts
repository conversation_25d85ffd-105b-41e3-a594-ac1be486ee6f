/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

export type AIPrompt = {
  role: "user" | "system",
  content: string;
};

export type AIResponse = {
  content: string;
  usage?: {
    total_tokens: number;
    prompt_tokens?: number;
    completion_tokens?: number;
  }
}

export interface AIModel {
  runCompletion(messages: AIPrompt[], maxTokens?: number): Promise<AIResponse>;
  getModelVersion(): string;
}
