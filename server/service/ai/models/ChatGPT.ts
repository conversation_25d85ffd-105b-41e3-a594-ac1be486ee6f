/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { OpenAI } from 'openai';
import { AIModel, AIPrompt } from './AIModel';
import config from '../../../config';
import { Logger } from 'winston';
import UserError from '../../../error/UserError';
import { wwgLogger } from '../../wwgLogger';
import { ResponseFormat } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { ChatCompletionParseParams } from 'openai/resources/beta/chat/completions';
import { DEFAULT_MAX_TOKEN } from '../utrv-assistant/constants';

const OPENAI_MODEL = 'gpt-4o';
const TOKEN_LIMIT = 4096;

export interface OpenAiResponse extends Partial<OpenAI.Chat.Completions.ChatCompletion> {
  usage?: OpenAI.Completions.CompletionUsage | undefined;
}

export class ChatGPT implements AIModel {
  private openai = new OpenAI({
    apiKey: config.ai.chatGPT.apiKey,
  });

  constructor(private logger: Logger) {}

  public async runCompletion(messages: AIPrompt[], maxTokens?: number) {
    try {
      const response = await this.openai.chat.completions.create({
        model: OPENAI_MODEL,
        messages,
        max_tokens: Math.min(TOKEN_LIMIT, maxTokens || DEFAULT_MAX_TOKEN),
      });
      return {
        content: response.choices[0].message.content ?? '',
        usage: response.usage,
      };
    } catch (e) {
      throw new UserError('Unable to generate a response. Please try again later', { messages, cause: e });
    }
  }

  public async parseCompletion<T = any>(messages: AIPrompt[], maxTokens?: number, responseFormat?: ResponseFormat) {
    try {
      const response = await this.openai.beta.chat.completions.parse<ChatCompletionParseParams, T>({
        model: OPENAI_MODEL,
        messages,
        max_tokens: Math.min(TOKEN_LIMIT, maxTokens || DEFAULT_MAX_TOKEN),
        ...(responseFormat ? { response_format: responseFormat } : {}),
      });
      return {
        content: response.choices[0].message.parsed,
        usage: response.usage,
      };
    } catch (e) {
      throw new UserError('Unable to communicate with ChatGPTAI. Please try again later', { messages, cause: e });
    }
  }

  public getModelVersion(): string {
    return OPENAI_MODEL;
  }
}

let instance: ChatGPT;
export const getChatGPT = () => {
  if (!instance) {
    instance = new ChatGPT(wwgLogger);
  }
  return instance;
};
