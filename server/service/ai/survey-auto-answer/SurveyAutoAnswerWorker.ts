/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { BackgroundWorker, JobResult } from '../../../service/background-process/types';
import { JobType } from '../../../models/backgroundJob';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { SupportedJobModel } from './types';
import ContextError from '../../../error/ContextError';
import { getSurveyAutoAnswerWorkflow, SurveyAutoAnswerWorkflow } from './SurveyAutoAnswerWorkflow';

export class SurveyAutoAnswerWorker implements BackgroundWorker {
  constructor(private logger: LoggerInterface, protected workflow: SurveyAutoAnswerWorkflow) {}

  public canHandle(jobType: JobType): boolean {
    return jobType === JobType.AIAutoAnswerSurvey;
  }

  public async process(jobId: string): Promise<JobResult> {
    this.logger.info(`Start processing, jobId: ${jobId || '-'}`);
    const job = jobId ? await this.workflow.findJob(jobId) : await this.workflow.findPendingJob();

    if (!this.workflow.canHandle(job.type)) {
      throw new ContextError(`Received invalid job ${job._id} type ${job.type}`, {
        jobId: job._id,
        type: job.type,
        created: job.created,
      });
    }

    // Progress job to the next stage, allow to retry at failed task
    return this.workflow.progressJob(job as unknown as SupportedJobModel, { retry: true });
  }
}

let instance: SurveyAutoAnswerWorker;
export const getSurveyAutoAnswerWorker = () => {
  if (!instance) {
    instance = new SurveyAutoAnswerWorker(wwgLogger, getSurveyAutoAnswerWorkflow());
  }
  return instance;
};
