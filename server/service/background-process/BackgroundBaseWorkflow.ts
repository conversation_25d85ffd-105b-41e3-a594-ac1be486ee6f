/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { LoggerInterface } from '../wwgLogger';
import { ObjectId } from 'bson';
import BackgroundJob, {
  BackgroundJobModel,
  BackgroundJobPlain,
  JobStatus,
  JobType,
  Task,
  TaskStatus,
} from '../../models/backgroundJob';
import { finalStatuses, isJobFinished, createLogEntry } from '../../service/jobs';
import ContextError from '../../error/ContextError';
import UserError from '../../error/UserError';
import { BackgroundJobService } from './BackgroundJobService';
import { SupportedJobModel } from '../internal-release/types';
import { UserPlain } from "../../models/user";

// Flat list of props
export interface JobInfo {
  jobId: ObjectId;
  taskId: string;
}

interface IdempotencyKey {
  id: string;
  idempotencyKey?: string | undefined;
}

export interface TaskResult<J extends BackgroundJobPlain = BackgroundJobPlain> {
  job: J;

  /** Allow to continue next task **/
  executeNextTask?: boolean;
}

interface ProgressOptions {
  retry: boolean;
}

export abstract class BackgroundBaseWorkflow<JobModel extends BackgroundJobModel> {
  protected abstract jobType: JobType;
  protected abstract logger: LoggerInterface;

  public async findJob(jobId: string | ObjectId) {
    return BackgroundJob.findOne({
      _id: new ObjectId(jobId),
      type: this.jobType,
      completedDate: { $exists: false },
      deletedDate: { $exists: false },
    })
      .orFail()
      .exec();
  }

  public async findById(jobId: string | ObjectId) {
    return BackgroundJob.findOne<SupportedJobModel>({
      _id: new ObjectId(jobId),
      type: this.jobType,
    })
      .orFail()
      .exec();
  }

  /**
   * This is for checking if the job is being processed
   */
  public async findProcessingJob(initiativeId: string | ObjectId) {
    return BackgroundJob.findOne({
      initiativeId: new ObjectId(initiativeId),
      type: this.jobType,
      status: { $nin: finalStatuses },
    })
      .lean()
      .exec();
  }

  public async findPendingJob() {
    return BackgroundJob.findOne({
      type: this.jobType,
      status: JobStatus.Pending,
    })
      .orFail()
      .exec();
  }

  public getIdempotencyKey({ id, idempotencyKey }: IdempotencyKey) {
    return [this.jobType, id, idempotencyKey].filter(Boolean).join('|');
  }

  protected async startTask(job: JobModel, task: Task) {
    task.status = TaskStatus.Processing;
    job.logs.push(createLogEntry(`Start ${task.name} processing`));
    job.markModified('tasks');

    await job.save();
  }

  protected failTask(job: JobModel, task: Task, e: Error) {
    this.logger.error(
      new ContextError(`Failed to process ${job.type} job`, {
        jobId: job._id,
        jobType: job.type,
        taskId: task.id,
        taskType: task.type,
        cause: e,
      })
    );

    job.logs.push(
      createLogEntry(`Failed task ${task.name}`, {
        metadata: {
          errorMessage: e.message,
        },
      })
    );

    task.status = TaskStatus.Error;
    job.markModified('tasks');

    return job.save();
  }

  protected abstract processTask(job: JobModel, task: Task): Promise<TaskResult<JobModel>>;

  public canHandle(jobType: JobType) {
    return jobType === this.jobType;
  }

  public async executeTask(job: JobModel, task: Task, options?: ProgressOptions) {
    try {
      this.logger.info(`Executing ${job.type} task ${task.name}`, {
        jobId: job._id,
        jobType: job.type,
        taskId: task.id,
        taskType: task.type,
      });
      const result = await this.processTask(job, task);

      if (result.executeNextTask) {
        // Might have added a lot more tasks, restart the loop
        return this.progressJob(job, options);
      }

      return result.job;
    } catch (e) {
      return this.failTask(job, task, e as Error);
    }
  }

  public async progressJob(job: JobModel, options?: ProgressOptions): Promise<JobModel> {
    const context = {
      jobId: job._id,
      jobType: job.type,
      jobStatus: job.status,
    };

    if (isJobFinished(job.status)) {
      throw new ContextError(`Job is already finished with state: ${job.status}`, context);
    }

    // Mark as processing
    job.status = JobStatus.Processing;
    await job.save();

    for (const task of job.tasks) {
      if (task.status === TaskStatus.Completed) {
        continue;
      }

      // Pending or we allow to re-try failed tasks.
      if (task.status === TaskStatus.Pending || (options?.retry && task.status === TaskStatus.Error)) {
        const updatedJob = await this.executeTask(job, task, options);

        if (updatedJob.status === JobStatus.Processing) {
          // return back to pending, as we are done processing;
          updatedJob.status = JobStatus.Pending;
          return updatedJob.save();
        }
        return updatedJob;
      }

      if (task.status === TaskStatus.Error) {
        this.logger.error(
          new ContextError(`Processing job ${job.id} with failed task`, {
            ...context,
            taskId: task.id,
            taskType: task.type,
          })
        );
        job.status = JobStatus.Error;
        return job.save();
      }

      if (task.status === TaskStatus.Processing) {
        this.logger.error(
          new ContextError(`Processing job ${job.id} with task that is currently in progress`, {
            ...context,
            taskId: task.id,
            taskType: task.type,
          })
        );
        return job;
      }

      throw new ContextError(`Processing job ${job.id} ${job.type} with unhandled task type ${task.type}`, {
        ...context,
        taskId: task.id,
        taskType: task.type,
      });
    }

    // All task must be completed now
    job.completedDate = new Date();
    job.status = JobStatus.Completed;
    job.logs.push(createLogEntry('All tasks completed. Marking job as completed'));
    return job.save();
  }

  protected async checkDidJobExist({ initiativeId, errorMessage }: { initiativeId: ObjectId; errorMessage?: string }) {
    const exists = await this.findProcessingJob(initiativeId);

    if (exists) {
      throw new UserError(errorMessage ? errorMessage : `${this.jobType} job already exists for this configuration`, {
        initiativeId,
        jobType: this.jobType,
        existingJobId: exists._id,
      });
    }

    return;
  }

  static async resetJob(job: BackgroundJobModel, user: Pick<UserPlain, '_id'>) {

    for (const task of job.tasks) {
      if (task.status !== TaskStatus.Error) {
        continue;
      }
      task.status = TaskStatus.Pending;
    }

    job.logs.push(createLogEntry(`Job and all tasks were reset to pending state`, {
      metadata: {
        userId: user._id.toString()
      },
    }));

    job.markModified('tasks');
    job.completedDate = undefined;
    job.status = JobStatus.Pending;

    return job.save();
  }

  static async getJob(jobId: string | ObjectId) {
    return BackgroundJob
      .findOne({
        _id: new ObjectId(jobId)
      })
      .orFail()
      .lean();
  }

  protected runJobInstantly(job: JobModel, bgJobService: BackgroundJobService) {
    bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          initiativeId: job.initiativeId,
          cause: e,
        })
      );
    });
  }
}
