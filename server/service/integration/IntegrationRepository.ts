/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { IntegrationCheck } from "./IntegrationProvider";
import IntegrationConnection, {
  IntegrationConnectionCreate,
  IntegrationConnectionPlain
} from "../../models/integrationConnection";
import { FilterQuery } from "mongoose";
import { ObjectId } from "bson";
import { KeysEnum } from '../../models/public/projectionUtils';
import { UserMin, userMinFields } from '../../models/user';
import UserError from '../../error/UserError';

type ProviderQueryFilter = FilterQuery<IntegrationConnectionPlain>;

export const baseProjectFields: KeysEnum<IntegrationConnectionPlain> = {
  _id: 1,
  created: 1,
  name: 1,
  initiativeId: 1,
  integrationCode: 1,
  createdBy: 1,
  lastUpdated: 1,
  status: 1,
  data: 1,
};

export class IntegrationRepository {
  public async getConnection<D = unknown>(integrationCheck: IntegrationCheck) {
    return IntegrationConnection
      .findOne({
        initiativeId: integrationCheck.initiativeId,
        integrationCode: integrationCheck.integrationCode
      })
      .lean<IntegrationConnectionPlain<D>>()
      .exec();
  }

  public async getConnectionWithUser<D = unknown>(integrationCheck: IntegrationCheck) {
    const aggregate = [
      {
        $match: {
          initiativeId: integrationCheck.initiativeId,
          integrationCode: integrationCheck.integrationCode,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'creators',
          pipeline: [{ $project: userMinFields }],
        },
      },
      {
        $project: {
          ...baseProjectFields,
          creator: { $arrayElemAt: ['$creators', 0] },
        },
      },
    ];

    const [connection] = await IntegrationConnection.aggregate<IntegrationConnectionPlain<D> & { creator: UserMin }>(
      aggregate
    ).exec();

    if (!connection) {
      throw new UserError('No connection found', { ...integrationCheck });
    }

    return connection;
  }

  public async getInitiativeConnections<D = unknown>(initiativeId: ObjectId) {
    return IntegrationConnection
      .find({
        initiativeId,
        status: 'active'
      })
      .lean<IntegrationConnectionPlain<D>[]>()
      .exec();
  }

  public async getConnectionsByProvider(filter: ProviderQueryFilter) {
    return IntegrationConnection.find(filter).exec();
  }

  public async createConnection<D = unknown>(createData: IntegrationConnectionCreate<D>) {
    return IntegrationConnection.create<IntegrationConnectionCreate<D>>(createData)
  }
}

let instance: IntegrationRepository;
export const getIntegrationRepository = () => {
  if (!instance) {
    instance = new IntegrationRepository();
  }
  return instance;
}
