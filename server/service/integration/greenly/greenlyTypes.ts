/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { DataPeriods } from "../../utr/constants";

/**
 * Greenly API Setup for a company
 */

interface SetupContextValue {
  code: string,
  name: string,
  value?: string | number | boolean,
  unit?: string
}

type Address = {
  street: string;
  city: string;
  state: string
  country: string;
  postalCode: string;
};

interface FacilityCreate {
  name: string;
  address: Address
  floorSpace: number;
  floorSpaceUnit: 'sqm' | 'sqft';
  ownerType: 'owner' | 'lessor' | 'lessee';
}

export interface GreenlyCreateData {

  user: {
    email: string
    jobTitle?: string;
    firstName: string;
    surname: string;
  }

  company: {
    _id: string;
    name: string
    logo: string | undefined;
    description?: string;
    address: Address
  }
  additionalContext: SetupContextValue[];

  facilities?: FacilityCreate[]
}

/**
 * Greenly API response for a reglementaryEntryEmissions
 *   {
 *         "category": "5.4",
 *         "description": {
 *             "fr": "Investissements",
 *             "en": "Investments"
 *         },
 *         "emissionsInKgCO2": 0,
 *         "kgCO2": 0,
 *         "kgCH4fossilAsCO2e": 0,
 *         "kgCH4biogenicAsCO2e": 0,
 *         "kgN2OAsCO2e": 0,
 *         "kgOtherAsCO2e": 0,
 *         "kgCO2Biogenic": 0,
 *         "frenchOfficialCategory": 5,
 *         }
 *
 */
export interface EntryEmissions {
  category: string
  description: Description
  emissionsInKgCO2: number
  kgCO2: number
  kgCH4fossilAsCO2e: number
  kgCH4biogenicAsCO2e: number
  kgN2OAsCO2e: number
  kgOtherAsCO2e: number
  kgCO2Biogenic: number
  frenchOfficialCategory: number
  scope: number
}

interface Description {
  fr?: string
  en: string
}


export interface ListResponse<T = unknown> {
  data: T[]
  totalCount: number
  totalNbPages: number
  order: Order[]
  limit: number
  offset: number
  language: string
}

export interface ExportableActivity {
  id: string
  ownerCompanyId: string
  ownerProfileId: string
  companyId: string
  profileId: string
  activityId: string
  language: string
  subScope: string
  marketBased: string
  companyName: string
  industry: string
  ownershipPercentage: number
  fiscalYear: string
  temporalScopeStart?: string
  temporalScopeEnd?: string
  parentBusinessCategory: string
  businessCategory: string
  activityName: string
  section: string
  subType?: string
  engineType?: string
  trainType: any
  flightType?: string
  travelClass?: string
  computedDistance?: number
  locationCountryId?: string
  activityType: string
  source: string
  specificModuleId?: string
  moduleCategory: string
  regulatoryMethodology: string
  revenueM: number
  employeesNumber: number
  scope: string
  subScopeName: string
  buildingId?: string
  siteName?: string
  buildingSurfaceAreaDoesuserhavetheinfo?: boolean | null;
  buildingSurfaceAreaValue?: number
  buildingSurfaceAreaUnit?: string
  buildingEmployees?: number
  isCrossEntity: boolean
  crossEntityFilter: string
  purchaseCategoryId: string
  isGenericPurchaseCategory: boolean
  entity?: string
  entityCountry?: string
  businessUnit?: string
  shouldBeExcluded: boolean
  quantity: number
  quantityForSubScope: number
  unit: string
  entityCreatedAt: string
  versionCreatedAt: string
  granularType: string
  firstFiscalMonth: string
  emissionsInKGCO2e: number
  emissionsIntCO2e: number
  emissionsIntCO2ePerEmployee: number
  emissionsIntCO2ePerRevenueM: number
  emissionFactorName: string
  emissionFactorId: string
  emissionFactorVersion: number
  emissionFactorNameAndUnit: string
  emissionFactorValue: number
  emissionFactorInputUnit: string
  emissionFactorOutputUnit: string
  emissionFactorReferentialType: string
  emissionFactorReferentialYear?: string
  emissionFactorGeographicalValidity?: string
  emissionFactorReferentialDescription?: string
  emissionFactorReferentialCategory?: string
  emissionFactorMethodology: string
  percentEmissionsInScope: number
  quarter?: string
  supplierName?: string
  amortizationDurationInYears: any
  acquisitionDate?: string
  activityAndServicesEmissions: number
  parentBusinessCategoryEnum: string
}

interface Order {
  field: string
  direction: string
}


export interface SurveyEmissionData {
  emissionData: EntryEmissions[];
  effectiveDate: Date
  period: DataPeriods;
}

export interface GreenlyCompany {
  id: string
  createdAt: string
  lastUpdatedAt: string
  industry: string
  companyName: string
  companyLanguage: string
  countryId: string
  type: string
  companyLogoUrl: string
  settings: Settings
  identifier?: { type: string, value: string | null }
  organisationBoundaries: string
  stringifiedFirstFiscalMonth: string
  fiscalYearIsYearOfFirstMonth: boolean
  defaultRegulatoryMethodology: string
  parentIndustry: string
  hasAcceptedToShareCarbonData: boolean
  hasAccessToSupplierQuestionnaire: boolean
  companyAccountOwnerId: any
  invitedByCompanyId: any
  invitedByUserId: any
  matchedSupplierId: string
  isParentCompany: boolean
  isChildCompany: boolean
  groupCompanyId: string
  dashboardIntegrationId: any
  validityStatus: string
  managedByExternalConsultant: boolean
  mfaEnabled: boolean
  name: string
  likelyLanguage: string
  firstFiscalMonth: string
  profiles: Profile[]
  ownedCompanyHasCompanyList: any[]
  groupCompany: GroupCompany
}


export type GreenlyCompanyMinimal = Pick<GreenlyCompany, 'id' | 'name' | 'identifier'>;

export interface Settings {
  notifications: Notifications
}

export interface Notifications {
  enableTaskNotifications: boolean
}

export interface Profile {
  id: string
  createdAt: string
  lastUpdatedAt: string
  companyId: string
  year: string
  startDate: string
  endDate: string
  revenue: number
  revenueCurrency: string
  employeesNumber: number
  isActive: boolean
  regulatoryMethodology: string
  hasInvolvedSuppliersInPercent: number
  suppliersContactFileURL: any
  supplierImportFromExpenseDataUrl: any
  supplierImportFromManualInputUrl: any
  supplierExpenseDataImportUrlFr: any
  supplierManualImportUrlFr: any
  hasAskedToScrapSuppliers: boolean
  willDuplicateSuppliersLater: boolean
  reportIsReady: boolean
  reportPrecisionInPercent: number
  isFrozen: boolean
  hasCommunicatedInternally: boolean
  hasCommunicatedInternallyOnResults: boolean
  hasDisclosedEmissions: boolean
  hasActivatedClimateTraining: boolean
  hasPublishedHisReportOnRegulatoryAgency: boolean
  hasContributedOnScopeOneAndTwoInPercent: number
  hasContributedOnScopeThreeInPercent: number
  actionsPlanProgressionInPercent: number
  hasPurchasedFresqueDuClimatTraining: boolean
  hasNoModules: any
  offsetAmountInTons: number
  offsetAmountInEuros: number
  createdFromDuplication: boolean
  questionnaireVersion: string
  skipCSRQuestionsOfEmployeeQuestionnaire: boolean
  reductionIsAbsolute: boolean
  reductionInVolumePerYear: number
  hasObjectivesInScopeThree: boolean
  hasCommunicatedClimateObjectives: boolean
  satisfactionSurveyInfo: any[]
  unaccountedActivitySubTypes: UnaccountedActivitySubTypes
  companyVertical: any
  isPaymentOverdue: boolean
  electricityReferentials: any
  clientScoreVersion: string
  supplierTransactionIdentificationGoalMetric: any
  accountingInternalLogs: any
  useMaterializedExportableActivity: any
  sbtiSector: any
  sbtiTargetYear: any
  sbtiSubmissionYear: any
  products: any[]
}

export interface UnaccountedActivitySubTypes {
  employeeActivities: string[]
}

export interface GroupCompany {
  id: string
  createdAt: string
  lastUpdatedAt: string
  industry: string
  companyName: string
  companyLanguage: string
  countryId: string
  type: string
  companyLogoUrl: string
  settings: Settings2
  identifier: any
  organisationBoundaries: string
  stringifiedFirstFiscalMonth: string
  fiscalYearIsYearOfFirstMonth: boolean
  defaultRegulatoryMethodology: string
  parentIndustry: string
  hasAcceptedToShareCarbonData: boolean
  hasAccessToSupplierQuestionnaire: boolean
  companyAccountOwnerId: any
  invitedByCompanyId: any
  invitedByUserId: any
  matchedSupplierId: string
  isParentCompany: boolean
  isChildCompany: boolean
  groupCompanyId: string
  dashboardIntegrationId: any
  validityStatus: string
  managedByExternalConsultant: boolean
  mfaEnabled: boolean
  name: string
  likelyLanguage: string
  firstFiscalMonth: string
}

export interface Settings2 {
  notifications: Notifications2
}

export interface Notifications2 {
  enableTaskNotifications: boolean
}


export interface ServiceAccountMeReponse {
  id: string
  createdAt: string
  lastUpdatedAt: string
  email: string
  firstName: string
  lastName: string
  phoneNumber: any
  jobTitle: string
  dateOfConsentTermsAndConditions: string
  password: any
  role: string
  supplierInvitationId: any
  meetingLinks: any
  lastConnectedAt: any
  lastSeenAt: any
  lastRepliedAt: any
  lastContactedAt: any
  lastEmailOpenedAt: any
  lastEmailClickedAt: any
  name: string
  isGreenlyPro: boolean
  companies: GreenlyCompany[]
}
