/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { getEmailService } from "../../email/EmailService";
import config from "../../../config";
import DOMPurify from "isomorphic-dompurify";
import { SingleIdentityService } from "../api/SingleIdentityService";
import axios, { type AxiosInstance, isAxiosError } from "axios";
import { AuthInterceptor } from "../api/AuthInterceptor";
import { EntryEmissions, GreenlyCompany, GreenlyCreateData, ServiceAccountMeReponse } from "./greenlyTypes";
import ContextError from "../../../error/ContextError";

export class GreenlyApi {

  constructor(
    private readonly emailService: ReturnType<typeof getEmailService>,
    private readonly httpClient: AxiosInstance,
  ) {
  }

  public async listCompanies() {
    return this.httpClient.get<ServiceAccountMeReponse>('/users/me', {
      params: {
        language: 'en',
      }
    }).then(res => res.data.companies);
  }

  public async getCompany(companyId: string): Promise<GreenlyCompany> {
    const companies = await this.listCompanies();
    const company = companies.find(c => c.id === companyId);
    if (!company) {
      throw new ContextError(`Company ${companyId} not found`, {
        companyId,
        availableIds: companies.map(c => c.id),
      });
    }
    return company;
  }

  /**
   * Eventually this will be replaced by API call once enabled, now we rely on email
   */
  public async createConnection(createData: GreenlyCreateData) {

    // Send data through email
    const { company, user, additionalContext } = createData;


    const msg = this.emailService.getNewMessageInstance();
    msg.addTo(config.integrations.greenly.email, 'Greenly');
    msg.setSubject('Greenly Connection Setup');

    const createRow = (label: string, value: string | undefined) => {
      return `<tr><td>${label}</td><td>${value || '-'}</td></tr>`;
    }

    const userDetails = [
      createRow('Job Title', user.jobTitle),
      createRow('First Name', user.firstName),
      createRow('Surname', user.surname),
      createRow('email', user.email),
    ]

    const companyDetails = [
      createRow('Id', company._id.toString()),
      createRow('Name', company.name),
      createRow('Logo', company.logo),
      createRow('Description', company.description),
    ];

    const context = additionalContext.reduce((acc, ctx) => {
      acc.push(createRow(ctx.name, [ctx.value, ctx.unit].filter(Boolean).join(' ')));
      return acc;
    }, [] as string[])

    const addressDetails = Object.entries(company.address).reduce((acc, [k, v]) => {
      acc.push(createRow(k, v));
      return acc;
    }, [] as string[])

    const html = (`
      <body>
        <h3>New G17Eco website integration request.</h3>
        <table border='1' width='100%'>
          <tr><td colspan="2">User</td></tr>
          ${userDetails.join('')}

          <tr><td colspan="2">Company</td></tr>
          ${companyDetails.join('')}

          <tr><td colspan="2">Address</td></tr>
          ${addressDetails.join('')}

          <tr><td colspan="2">Additional Data</td></tr>
          ${context.join('')}
        </table>
      </body>`
    );

    const sanitizedText = DOMPurify.sanitize(html);
    msg.setHtml(sanitizedText);

    const response = await this.emailService.send(msg);

    return {
      emailTransactionId: response.getId(),
      companyId: company._id.toString(),
    };
  }

  public async getEmissionsDataTotal(companyId: string, year: number, protocol = 'GHGProtocol'): Promise<EntryEmissions[]> {
    return this.httpClient
      .get<EntryEmissions[]>(`/reglementaryEntryEmissions`, {
        params: {
          reglementaryEntryEmissions: protocol,
          companyId,
          year,
        },
      })
      .then(res => res.data)
      .catch((e) => {
        throw new ContextError(`Failed to get emissions data for company ${companyId} and year ${year}`, {
          companyId,
          year,
          protocol,
          error: e.message,
          body: isAxiosError(e) ? e.response?.data : undefined,
        });
      });
  }
}

let instance: GreenlyApi;
export const getGreenlyApi = () => {
  if (!instance) {
    const { baseUrl, credentials, timeoutMs, tokenUrl } = config.integrations.greenly.api;
    const identityService = new SingleIdentityService({ credentials, baseUrl: tokenUrl });
    const httpClient = axios.create({ baseURL: baseUrl, timeout: timeoutMs });

    httpClient.interceptors.request.use(
      AuthInterceptor(identityService),
      error => Promise.reject(error),
    );

    instance = new GreenlyApi(
      getEmailService(),
      httpClient,
    );
  }
  return instance;
}
