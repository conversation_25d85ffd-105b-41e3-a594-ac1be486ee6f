/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ProductCodes } from '../../../models/customer';
import { AppCode, AppConfig, defaultCTLFeatures } from '../AppConfig';
import config from '../../../config';
import { availableAddonsCtl, companyTrackerServicesAgreement } from './ctl-common';
import { BrandingTemplate } from "../../organization/domainConfig";
import { PERMISSION_GROUPS } from '../../../models/initiative';
import { FeatureTag } from '@g17eco/core';

export const defaultCTBase: AppConfig = {
  code: AppCode.CompanyTracker,
  productCode: ProductCodes.CompanyTracker,
  validProductCodes: [
    ProductCodes.CompanyTracker,
    ProductCodes.SGXESGenome
  ],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT,
  name: 'Company Tracker',
  logo: `${config.assets.cdn}/apps/default/Company_Tracker_logo.svg`,
  onboardingPath: 'company-tracker',
  rootAppPath: 'company-tracker',

  settings: {
    overviewRecommendedAddons: ['ctl'],
    settingsRecommendedAddons: ['ctl'],
    availableAddons: [...availableAddonsCtl],
    baseFeatures: defaultCTLFeatures,
    betaFeatures: [
      FeatureTag.MetricAssistantAI,
      FeatureTag.DraftFurtherExplanationAI,
      FeatureTag.SDGInsightAI,
      FeatureTag.PPTXReportAI,
    ],
    companyAgreementsRequired: [
      companyTrackerServicesAgreement
    ],
  },

  // Setup whitelabel as well to ensure we can override domainConfig if selected
  whitelabel: {
    brandingTemplate: BrandingTemplate.G17Eco,
    emailLogo: config.assets.defaultLogo,
  }
};
