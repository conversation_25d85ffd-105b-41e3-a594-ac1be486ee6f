/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import config from '../../../config';
import { ProductCodes } from '../../../models/customer';
import { AppCode, AppConfig } from '../AppConfig';
import { companyTrackerServicesAgreement } from './ctl-common';
import { PERMISSION_GROUPS } from '../../../models/initiative';
import { FeatureCode, FeatureTag, getFeatureDetails } from "@g17eco/core";
import { BrandingTemplate } from "../../organization/domainConfig";

export const toli: AppConfig = {
  code: AppCode.TOLI,
  productCode: ProductCodes.CompanyTrackerStarter,
  validProductCodes: [ProductCodes.CompanyTrackerStarter],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_STARTER,
  name: 'Tree of Life Institute',
  logo: `${config.assets.cdn}/apps/toli/toli-logo.svg`,
  onboardingPath: 'toli',
  rootAppPath: 'company-tracker',

  settings: {
    overviewRecommendedAddons: ['gri2021', 'eesg_2024'],
    settingsRecommendedAddons: ['gri2021', 'eesg_2024'],
    // Only allow single pack
    availableAddons: ['gri2021', 'eesg_2024'],
    baseFeatures: [
      getFeatureDetails(FeatureTag.CustomMetricsXS),
      getFeatureDetails(FeatureTag.ReportingLevelsS),
      {
        name: 'Users - TOLI',
        code: FeatureCode.Users,
        active: true,
        config: {
          limit: 2
        },
      },
      {
        name: 'Scope Modules TOLI',
        code: FeatureCode.ScopePacks,
        active: true,
        config: {
          scopePacks: {
            access: 'custom',
            scope: {
              standards: [],
              frameworks: ['eesg_2024'],
            },
            restricted: true,
          }
        },
      },
      getFeatureDetails(FeatureTag.MetricAssistantAI),
      getFeatureDetails(FeatureTag.DraftFurtherExplanationAI),
      getFeatureDetails(FeatureTag.SDGInsightAI),
    ],
    betaFeatures: [FeatureTag.PPTXReportAI],
    companyAgreementsRequired: [
      companyTrackerServicesAgreement
    ],
  },

  // Setup whitelabel as well to ensure we can override domainConfig if selected
  whitelabel: {
    brandingTemplate: BrandingTemplate.G17Eco,
    emailLogo: config.assets.defaultLogo,
  }
};
