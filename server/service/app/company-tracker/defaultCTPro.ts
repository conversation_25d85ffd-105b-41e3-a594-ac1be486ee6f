/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ProductCodes } from '../../../models/customer';
import { AppCode, AppConfig, defaultCTProFeatures } from '../AppConfig';
import { PERMISSION_GROUPS } from '../../../models/initiative';
import { defaultCTBase } from './defaultCTBase';
import { DisplayOption, DownloadUtrvStatusCombined } from '../../../types/download';
import { ActionList } from '../../../service/utr/constants';
import { FeatureTag } from '@g17eco/core';


export const defaultCTPro: AppConfig = {
  ...defaultCTBase,
  code: AppCode.CompanyTrackerPro,
  productCode: ProductCodes.CompanyTrackerPro,
  validProductCodes: [ProductCodes.CompanyTrackerPro],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_PRO,
  name: 'Company Tracker Pro',
  onboardingPath: 'company-tracker-pro',
  settings: {
    ...defaultCTBase.settings,
    // No additional recommendations
    overviewRecommendedAddons: [],
    baseFeatures: defaultCTProFeatures,
    betaFeatures: [
      FeatureTag.MetricAssistantAI,
      FeatureTag.DraftFurtherExplanationAI,
      FeatureTag.SDGInsightAI,
      FeatureTag.PPTXReportAI,
    ],
    canViewAllPacks: true,
    canEditInsightOverview: true,
    canShowSDGDetails: true,
    defaultDownloadOptions: {
      metricStatuses: [DownloadUtrvStatusCombined.All, DownloadUtrvStatusCombined.AllAnswered, ActionList.Verified],
      metricOverrides: [DisplayOption.UserInput, DisplayOption.MetricOverrides, DisplayOption.SystemDefault],
    },
  },
};
