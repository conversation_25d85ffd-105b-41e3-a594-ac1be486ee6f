import { ObjectId } from 'bson';
import { getSurveyScopeService } from '../organization/SurveyScopeService';
import { getIntegrationManager } from '../integration/IntegrationManager';
import { getGreenProjectService } from '../integration/green-project/GreenProjectService';
import { getGroup } from '@g17eco/core';
import { DashboardTemplateType, templateDashboard } from './template';
import UserError from '../../error/UserError';
import { InsightDashboard, InsightDashboardType } from '../../models/insightDashboard';
import { getDashboardItemManager, PopulateDataParams } from './DashboardItemManager';
import { WFN_MODULE_CODE } from '../../types/wfn';
import { mapValueListToNumber } from '../../util/wfn';

interface Template {
  value: DashboardTemplateType;
  label: string;
  icon?: string;
}

export class DashboardTemplatesService {
  constructor(
    private surveyScopeService: ReturnType<typeof getSurveyScopeService>,
    private integrationManager: ReturnType<typeof getIntegrationManager>,
    private insightDashboardModel: typeof InsightDashboard,
    private dashboardItemManager: ReturnType<typeof getDashboardItemManager>
  ) {}

  public async getTemplates({ initiativeId }: { initiativeId: ObjectId }): Promise<Template[]> {
    const templates = [];
    for (const templateType of Object.values(DashboardTemplateType)) {
      if (await this.hasTemplate(initiativeId, templateType)) {
        const template = this.getTemplate(templateType);
        if (template) {
          templates.push(template);
        }
      }
    }
    return templates;
  }

  private getTemplate(templateType: DashboardTemplateType): Template | undefined {
    switch (templateType) {
      case DashboardTemplateType.GPT: {
        const gptInfo = getGreenProjectService().getInfo();
        return {
          value: DashboardTemplateType.GPT,
          label: 'GPT integration',
          icon: gptInfo.logo,
        };
      }
      case DashboardTemplateType.WFN: {
        return {
          value: DashboardTemplateType.WFN,
          label: 'Workforce Nutrition',
          icon: getGroup('standards', WFN_MODULE_CODE)?.src,
        };
      }
    }
  }

  private async hasTemplate(initiativeId: ObjectId, templateType: DashboardTemplateType) {
    switch (templateType) {
      case DashboardTemplateType.GPT: {
        const gptInfo = getGreenProjectService().getInfo();
        const integrationServices = await this.integrationManager.getActiveServices(initiativeId);
        return integrationServices.some((service) => service.code === gptInfo.code);
      }
      case DashboardTemplateType.WFN: {
        const { standards: usedStandards } = await this.surveyScopeService.getUsedScopes(initiativeId);
        return Boolean(usedStandards[WFN_MODULE_CODE]);
      }
    }
  }

  public async createDashboard({
    initiativeId,
    creatorId,
    templateType,
  }: {
    initiativeId: ObjectId;
    creatorId: ObjectId;
    templateType: DashboardTemplateType;
  }) {
    if (!(await this.hasTemplate(initiativeId, templateType))) {
      throw new UserError('Template not support for this initiative', {
        initiativeId,
        creatorId,
        templateType,
      });
    }

    const dashboard = templateDashboard[templateType];
    if (!dashboard) {
      throw new UserError('Template not support yet', {
        initiativeId,
        creatorId,
        templateType,
      });
    }

    return this.insightDashboardModel.create({ ...dashboard, initiativeId, creatorId });
  }

  public async populateDashboardData({ dashboard, initiative, filters }: PopulateDataParams) {
    const populatedDashboard = await this.dashboardItemManager.populateData({ dashboard, initiative, filters });

    switch (dashboard.type) {
      case InsightDashboardType.WFNTemplate: {
        const { utrsData } = populatedDashboard;
        return { ...populatedDashboard, utrsData: mapValueListToNumber(utrsData) };
      }

      default: {
        return populatedDashboard;
      }
    }
  }
}

let instance: DashboardTemplatesService;
export const getDashboardTemplatesService = () => {
  if (!instance) {
    instance = new DashboardTemplatesService(
      getSurveyScopeService(),
      getIntegrationManager(),
      InsightDashboard,
      getDashboardItemManager()
    );
  }
  return instance;
};
