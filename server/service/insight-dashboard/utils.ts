import {
  Filters,
  InsightDashboardItem,
  InsightDashboardItemType,
  InsightDashboardPlain,
  InsightDashboardType,
  PrivacyFilter,
  SurveyFilter,
  TimeFrameFilter,
  TimeFrameType,
  UtrvFilter,
} from '../../models/insightDashboard';
import { COLUMNS } from './constants';
import { environmental } from './summary/environmental';
import { social } from './summary/social';
import { governance } from './summary/governance';
import { overview } from './summary/overview';
import { ParsedQs } from 'qs';
import { PreloadOptions } from '../utr/aggregation/aggregatorDataService';
import { AppCode } from '../app/AppConfig';
import { ctStarterDashboards } from './summary/ctStarterDashboards';
import { ObjectId } from 'bson';
import { sgxEnvironmental } from './summary/sgx/sgx_environmental';
import { sgxSocial } from './summary/sgx/sgx_social';
import { sgxGovernance } from './summary/sgx/sgx_governance';
import { mustValidate } from '../../util/validation';
import { getUtrsHistoryQueryDtoSchema } from '../../routes/validation-schemas/universal-trackers';
import { DataPeriods } from '../utr/constants';
import { SurveyType } from '../../models/survey';
import { UtrvFilters } from '../utr/historical-data/UniversalTrackerHistoricalDataManager';
import { DateRange, subtractDate } from '../../util/date';

export const dashboards = {
  environmental: environmental,
  social: social,
  governance: governance,
  sgx_environmental: sgxEnvironmental,
  sgx_social: sgxSocial,
  sgx_governance: sgxGovernance,
  overview: overview,
} as const;

type ESGDashboard =
  | InsightDashboardType.Social
  | InsightDashboardType.Governance
  | InsightDashboardType.Environmental
  | InsightDashboardType.SgxEnvironmental
  | InsightDashboardType.SgxSocial
  | InsightDashboardType.SgxGovernance;
export type StaticDashboardType = InsightDashboardType.Overview | ESGDashboard;

export const getOrdinate = (newItemWidth: number, items: InsightDashboardItem[]) => {
  if (!items.length) {
    return { x: 0, y: 0 };
  }

  const finalItem = items.reduce((acc, current) => {
    return acc.y > current.gridSize.y ? acc : current.gridSize;
  }, items[0].gridSize);

  const hasRowEnoughSpace = finalItem.x + finalItem.w + newItemWidth <= COLUMNS;
  const x = hasRowEnoughSpace ? finalItem.x + finalItem.w : 0;
  const y = hasRowEnoughSpace ? finalItem.y : finalItem.y + finalItem.h;

  return { x, y };
};

export const isESGDashboard = (type: unknown): type is ESGDashboard => {
  return [
    InsightDashboardType.Environmental,
    InsightDashboardType.Social,
    InsightDashboardType.Governance,
    InsightDashboardType.SgxEnvironmental,
    InsightDashboardType.SgxSocial,
    InsightDashboardType.SgxGovernance,
  ].includes(type as ESGDashboard);
};

export const isStaticDashboardType = (type: unknown): type is StaticDashboardType => {
  return type === InsightDashboardType.Overview || isESGDashboard(type);
};

export const isSDGContributionChartType = (item: Pick<InsightDashboardItem, 'type'>) => {
  return item.type === InsightDashboardItemType.SDGContributionChart;
};

export const getDefaultSummaryDashboard = (type: StaticDashboardType) => {
  return dashboards[type] as InsightDashboardPlain;
};

interface DashboardByAppConfigCode {
  type: StaticDashboardType;
  appConfigCode: string | AppCode | undefined;
  userId: ObjectId;
  initiativeId: ObjectId;
}

export const getDashboardByAppConfigCode = ({
  type,
  appConfigCode,
  initiativeId,
  userId,
}: DashboardByAppConfigCode) => {
  if (appConfigCode === AppCode.CompanyTrackerStarter) {
    if (ctStarterDashboards[type]) {
      return {
        ...ctStarterDashboards[type],
        initiativeId,
        creatorId: userId,
        _id: new ObjectId(),
      } satisfies InsightDashboardPlain;
    }
  }

  return {
    ...(dashboards[type] as InsightDashboardPlain),
    initiativeId,
    creatorId: userId,
    _id: new ObjectId(),
  } satisfies InsightDashboardPlain;
};

export const getSummaryDashboardItems = (type: StaticDashboardType) => {
  const dashboard = dashboards[type] as InsightDashboardPlain;
  return [...dashboard.items];
};

export type PreloadQueryOptions = Pick<PreloadOptions, 'period' | 'timeFrameType' | 'dateRange' | 'surveyType'>;

export const toPreloadOptions = (query: ParsedQs) => {
  const queryFilters = mustValidate(query, getUtrsHistoryQueryDtoSchema);
  return {
    ...queryFilters,
    period: queryFilters.period === 'all' ? undefined : queryFilters.period,
    surveyType: queryFilters.surveyType === 'all' ? undefined : queryFilters.surveyType,
  };
};

export type AdditionalQueryOptions = Pick<PreloadOptions, 'timeFrameType' | 'dateRange'> & {
  period?: DataPeriods | 'all';
  surveyType?: SurveyType | 'all';
};

const getDateRangeFromTimeFrame = (timeFrameType: TimeFrameType) => {
  switch (timeFrameType) {
    case TimeFrameType.TwelveMonths:
      return {
        startDate: subtractDate(new Date(), 12, 'months'),
        endDate: new Date(),
      };
    case TimeFrameType.SixMonths:
      return {
        startDate: subtractDate(new Date(), 6, 'months'),
        endDate: new Date(),
      };
    case TimeFrameType.ThreeMonths:
      return {
        startDate: subtractDate(new Date(), 3, 'months'),
        endDate: new Date(),
      };
    case TimeFrameType.OneMonth:
      return {
        startDate: subtractDate(new Date(), 1, 'months'),
        endDate: new Date(),
      };
    case TimeFrameType.AllTime:
    case TimeFrameType.Custom:
      return undefined;
  }
}

const convertDateRangeToISOString = (dateRange: { startDate: Date; endDate: Date } | undefined): DateRange | undefined => {
  return dateRange ? {
    startDate: dateRange.startDate.toISOString(),
    endDate: dateRange.endDate.toISOString(),
  } : undefined;
};

const getDateRange = ({
  currentFilters,
  additionalFilters,
}: {
  currentFilters: TimeFrameFilter | undefined;
  additionalFilters: Pick<AdditionalQueryOptions, 'dateRange' | 'timeFrameType'>;
}): DateRange | undefined => {
  if (additionalFilters.dateRange) {
    return additionalFilters.dateRange;
  }

  if (additionalFilters.timeFrameType) {
    const dateRange = getDateRangeFromTimeFrame(additionalFilters.timeFrameType);
    return convertDateRangeToISOString(dateRange);
  }

  if (currentFilters?.startDate && currentFilters?.endDate) {
    return convertDateRangeToISOString({
      startDate: currentFilters.startDate,
      endDate: currentFilters.endDate,
    });
  }

  if (currentFilters?.type) {
    const dateRange = getDateRangeFromTimeFrame(currentFilters.type);
    return convertDateRangeToISOString(dateRange);
  }

  return undefined;
};

export const getUtrvFiltersFromDashboardFilters = ({
  filters,
  additionalFilters = {},
}: {
  filters: Filters;
  additionalFilters?: AdditionalQueryOptions;
}): UtrvFilters => {
  const isCompletedData = filters.survey === SurveyFilter.Completed;
  const assuredOnly = filters.utrv === UtrvFilter.Assured;
  const isPublicOnly = filters.privacy === PrivacyFilter.Public;
  const showTargets = !!filters.baselinesTargets?.enabled;
  const showBaselines = !!filters.baselinesTargets?.enabled;
  const period = additionalFilters.period === 'all' ? undefined : additionalFilters.period ?? filters.period;
  // should fallback to filters dateRange if additionalFilters.dateRange and additionalFilters.timeFrameType is not provided
  const dateRange = getDateRange({
    currentFilters: filters.timeFrame,
    additionalFilters,
  });
  const surveyType =
    additionalFilters.surveyType === 'all' ? undefined : additionalFilters.surveyType ?? filters.surveyType;

  return {
    isCompletedData,
    assuredOnly,
    isPublicOnly,
    showBaselines,
    showTargets,
    period,
    dateRange,
    surveyType,
    status: filters.utrv,
  };
};

export const getDashboardTypeFromParams = ({
  dashboard,
  mainDownloadCode,
}: {
  dashboard: string;
  mainDownloadCode: string;
}) => {
  return dashboard !== InsightDashboardType.Overview && mainDownloadCode === 'sgx_metrics'
    ? `sgx_${dashboard}`
    : dashboard;
};

export const shouldLoadScorecard = ({ filters, items }: InsightDashboardPlain) => {
  return filters.sdgContribution?.enabled || items.some((item) => isSDGContributionChartType(item));
};

export const shouldLoadInitiative = (dashboard: InsightDashboardPlain) => {
  return dashboard.filters.sdgContribution?.enabled || dashboard.filters.initiativeInfo?.enabled;
};

export interface DashboardDocumentCleanupParams {
  dashboardId: ObjectId;
  initiativeId: ObjectId;
  deletedDashboardDocumentIds: string[];
}

export interface CategorizedDocument {
  documentsToDelete: string[];
  documentsToReOwn: { _id: string; ownerId: ObjectId }[];
}