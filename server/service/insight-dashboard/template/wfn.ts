import { DataPeriods } from '../../../service/utr/constants';
import {
  CalculationType,
  ChartSubType,
  getDefaultFilters,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
} from '../../../models/insightDashboard';
import { ObjectId } from 'bson';
import { createArrayOfNumbers } from '../../../util/array';
import { WFN_CODE_PREFIX, WFN_UTRS_LENGTH, WfnSubModule } from '../../../types/wfn';

const config = {
  hideQuestionReference: true,
  editDisabled: true,
  deleteDisabled: true,
  duplicateDisabled: true,
};

// {wfn/2024/1-1: { code: 'wfn/2024/1-1' }}
const getVariables = (subModule: WfnSubModule) => {
  return createArrayOfNumbers(1, WFN_UTRS_LENGTH[subModule]).reduce((variables, i) => {
    // wfn/2024/1-1, wfn/2024/1-2, wfn/2024/1-3
    const code = `${WFN_CODE_PREFIX}${subModule}-${i}`;

    variables[code] = {
      code,
    };
    return variables;
  }, {} as Record<string, { code: string }>);
};

const nutritionEducationVariables = getVariables(WfnSubModule.NutritionEducation);
const NUTRITION_EDUCATION = {
  title: 'NUTRITION EDUCATION',
  variables: nutritionEducationVariables,
  gridSize: {
    x: 0,
    y: 15,
    w: 3,
    h: 12,
    minW: 3,
    minH: 12,
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.SparkLine,
  calculation: {
    type: CalculationType.Sum,
    values: [
      {
        name: 'NUTRITION EDUCATION',
      },
    ],
  },
  _id: new ObjectId(),
  note: {
    prefix: 'Maximum 20',
  },
  config,
};

const nutritionRelatedHealthChecksAndFollowUpVariables = getVariables(
  WfnSubModule.NutritionRelatedHealthChecksAndFollowUp
);
const NUTRITION_RELATED_HEALTH_CHECKS_AND_FOLLOW_UP = {
  title: 'NUTRITION RELATED HEALTH CHECKS AND FOLLOW-UP',
  variables: nutritionRelatedHealthChecksAndFollowUpVariables,
  gridSize: {
    x: 3,
    y: 15,
    w: 3,
    h: 12,
    minW: 3,
    minH: 12,
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.SparkLine,
  calculation: {
    type: CalculationType.Sum,
    values: [
      {
        name: 'NUTRITION RELATED HEALTH CHECKS AND FOLLOW-UP',
      },
    ],
  },
  _id: new ObjectId(),
  note: {
    prefix: 'Maximum 20',
  },
  config,
};

const healthyFoodAtWorkVariables = getVariables(WfnSubModule.HealthyFoodAtWork);
const HEALTHY_FOOD_AT_WORK = {
  title: 'HEALTHY FOOD AT WORK',
  variables: healthyFoodAtWorkVariables,
  gridSize: {
    x: 6,
    y: 15,
    w: 3,
    h: 12,
    minW: 3,
    minH: 12,
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.SparkLine,
  calculation: {
    type: CalculationType.Sum,
    values: [
      {
        name: 'HEALTHY FOOD AT WORK',
      },
    ],
  },
  _id: new ObjectId(),
  note: {
    prefix: 'Maximum 30',
  },
  config,
};

const breastFeedingSupportVariables = getVariables(WfnSubModule.BreastfeedingSupport);
const BREASTFEEDING_SUPPORT = {
  title: 'BREASTFEEDING SUPPORT',
  variables: breastFeedingSupportVariables,
  gridSize: {
    x: 9,
    y: 15,
    w: 3,
    h: 12,
    minW: 3,
    minH: 12,
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.SparkLine,
  calculation: {
    type: CalculationType.Sum,
    values: [
      {
        name: 'BREASTFEEDING SUPPORT',
      },
    ],
  },
  _id: new ObjectId(),
  note: {
    prefix: 'Maximum 30',
  },
  config,
};

const wfnVariables = {
  ...nutritionEducationVariables,
  ...nutritionRelatedHealthChecksAndFollowUpVariables,
  ...healthyFoodAtWorkVariables,
  ...breastFeedingSupportVariables,
};
const WORKFORCE_NUTRITION_LATEST_SCORE = {
  title: 'WORKFORCE NUTRITION LATEST SCORE',
  variables: wfnVariables,
  gridSize: {
    x: 0,
    y: 3,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.SingleValue,
  calculation: {
    type: CalculationType.Sum,
    values: [
      {
        name: 'WORKFORCE NUTRITION LATEST SCORE',
      },
    ],
  },
  _id: new ObjectId(),
  note: {
    prefix: 'Maximum 100',
  },
  config,
};

const WORKFORCE_NUTRITION_PROGRESS = {
  title: 'WORKFORCE NUTRITION PROGRESS',
  variables: wfnVariables,
  gridSize: {
    x: 4,
    y: 0,
    w: 8,
    h: 15,
    minW: 8,
    minH: 15,
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.Line,
  calculation: {
    type: CalculationType.Sum,
    values: [
      {
        name: 'WORKFORCE NUTRITION PROGRESS',
      },
    ],
  },
  _id: new ObjectId(),
  config,
};

const WORKFORCE_NUTRITION_RATING = {
  variables: wfnVariables,
  gridSize: {
    x: 0,
    y: 0,
    w: 4,
    h: 3,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Media,
  calculation: {
    type: CalculationType.Sum,
    values: [
      {
        name: 'WORKFORCE NUTRITION RATING',
      },
    ],
  },
  _id: new ObjectId(),
  config: {
    ...config,
    hideBorder: true,
  },
};

export const wfn: StaticDashboard = {
  title: 'Workforce Nutrition Scorecard',
  type: InsightDashboardType.WFNTemplate,
  filters: { ...getDefaultFilters(), period: DataPeriods.Monthly },
  items: [
    WORKFORCE_NUTRITION_RATING,
    WORKFORCE_NUTRITION_LATEST_SCORE,
    WORKFORCE_NUTRITION_PROGRESS,
    NUTRITION_EDUCATION,
    NUTRITION_RELATED_HEALTH_CHECKS_AND_FOLLOW_UP,
    HEALTHY_FOOD_AT_WORK,
    BREASTFEEDING_SUPPORT,
  ],
};
