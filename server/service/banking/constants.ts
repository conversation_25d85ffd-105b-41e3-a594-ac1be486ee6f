import config from '../../config';

export enum BankType {
  AbsBank = 'AbsBank',
  ManualBank = 'ManualBank',
}

export interface Country {
  name: string;
  code: string;
}

export interface Bank {
  name?: string;
  code: string;
  licenceType?: string;
  logo?: string;
  type?: BankType;
  countryCodes?: string[];
  popularCountryCodes?: string[];
  countries?: Country[];
  popularCountries?: Country[];
}

export const LIST_OF_BANKS: Bank[] = [
  {
    name: 'UBS AG',
    licenceType: 'Wholesale Bank',
    code: 'ubs-ag',
    logo: `${config.assets.cdn}/banks/ubs.png`,
    popularCountryCodes: ['SG'],
    countryCodes: ['CH'],
  },
    {
    name: 'HSBC',
    licenceType: 'Full Bank',
    code: 'hsbc',
    logo: `${config.assets.cdn}/banks/hsbc.svg`,
    popularCountryCodes: ['SG'],
    countryCodes: ['HK'],
  },
  {
    name: 'DBS Bank Ltd (First Vice Chairman)',
    licenceType: 'Full Bank',
    code: 'dbs-bank-ltd-first-vice-chairman',
    logo: `${config.assets.cdn}/banks/dbs.png`,
    popularCountryCodes: ['SG'],
    countryCodes: ['SG'],
  },
  {
    name: 'OCBC Bank',
    licenceType: 'Full Bank',
    code: 'ocbc-bank',
    logo: `${config.assets.cdn}/banks/ocbc.png`,
    countryCodes: ['SG'],
  },
  {
    name: 'United Overseas Bank Limited (Chairman)',
    licenceType: 'Full Bank',
    code: 'united-overseas-bank-limited-chairman',
    logo: `${config.assets.cdn}/banks/uob.png`,
    countryCodes: ['SG'],
  },
  {
    name: 'ABN Amro Bank N.V., Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'abn-amro-bank-nv-singapore-branch',
    countryCodes: ['NL'],
  },
  {
    name: 'Agricultural Bank of China Limited',
    licenceType: 'Wholesale Bank',
    code: 'agricultural-bank-of-china-limited',
    countryCodes: ['CN'],
  },
  {
    name: 'Anext Bank Pte. Ltd.',
    licenceType: 'Digital Wholesale Bank',
    code: 'anext-bank-pte-ltd',
    countryCodes: ['SG'],
  },
  {
    name: 'Arab Bank PLC',
    licenceType: 'Wholesale Bank',
    code: 'arab-bank-plc',
    countryCodes: ['JO'],
  },
  {
    name: 'Arab Banking Corporation (B.S.C.), Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'arab-banking-corporation-bsc-singapore-branch',
    countryCodes: ['BH'],
  },
  {
    name: 'Australia and New Zealand Banking Group Limited',
    licenceType: 'Wholesale Bank',
    code: 'australia-and-new-zealand-banking-group-limited',
    countryCodes: ['AU'],
  },
  {
    name: 'Banco Bilbao Vizcaya Argentaria, S.A.',
    licenceType: 'Wholesale Bank',
    code: 'banco-bilbao-vizcaya-argentaria-sa',
    countryCodes: ['ES'],
  },
  {
    name: 'Banco Santander, S.A.',
    licenceType: 'Wholesale Bank',
    code: 'banco-santander-sa',
    countryCodes: ['ES'],
  },
  {
    name: 'Bangkok Bank Public Company Limited',
    licenceType: 'Full Bank',
    code: 'bangkok-bank-public-company-limited',
    countryCodes: ['TH'],
  },
  {
    name: 'Bank J Safra Sarasin Ltd, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'bank-j-safra-sarasin-ltd-singapore-branch',
    countryCodes: ['CH'],
  },
  {
    name: 'Bank Julius Baer & Co Ltd',
    licenceType: 'Wholesale Bank',
    code: 'bank-julius-baer-amp-co-ltd',
    countryCodes: ['CH'],
  },
  {
    name: 'Bank of America N.A',
    licenceType: 'Full Bank',
    code: 'bank-of-america-na',
    countryCodes: ['US'],
  },
  {
    name: 'Bank of Baroda',
    licenceType: 'Wholesale Bank',
    code: 'bank-of-baroda',
    countryCodes: ['IN'],
  },
  {
    name: 'Bank of China Limited Singapore Branch',
    licenceType: 'Qualifying Full Bank',
    code: 'bank-of-china-limited-singapore-branch',
    countryCodes: ['CN'],
  },
  {
    name: 'Bank of Communications Co., Ltd',
    licenceType: 'Wholesale Bank',
    code: 'bank-of-communications-co-ltd',
    countryCodes: ['CN'],
  },
  {
    name: 'Bank of East Asia Limited, The',
    licenceType: 'Full Bank',
    code: 'bank-of-east-asia-limited-the',
    countryCodes: ['HK'],
  },
  {
    name: 'Bank of India',
    licenceType: 'Full Bank',
    code: 'bank-of-india',
    countryCodes: ['IN'],
  },
  {
    name: 'Bank of Montreal',
    licenceType: 'Wholesale Bank',
    code: 'bank-of-montreal',
    countryCodes: ['CA'],
  },
  {
    name: 'Bank of New York Mellon, The',
    licenceType: 'Wholesale Bank',
    code: 'bank-of-new-york-mellon-the',
    countryCodes: ['US'],
  },
  {
    name: 'Bank of Nova Scotia, The',
    licenceType: 'Wholesale Bank',
    code: 'bank-of-nova-scotia-the',
    countryCodes: ['CA'],
  },
  {
    name: 'Bank of Singapore',
    licenceType: 'Full Bank',
    code: 'bank-of-singapore',
    countryCodes: ['SG'],
  },
  {
    name: 'Bank of Taiwan',
    licenceType: 'Wholesale Bank',
    code: 'bank-of-taiwan',
    countryCodes: ['TW'],
  },
  {
    name: 'Bank Pictet & CIE (Asia) Limited',
    licenceType: 'Wholesale Bank',
    code: 'bank-pictet-amp-cie-asia-limited',
    countryCodes: ['CH'],
  },
  {
    name: 'Barclays Bank PLC',
    licenceType: 'Wholesale Bank',
    code: 'barclays-bank-plc',
    countryCodes: ['GB'],
  },
  {
    name: 'BDO Unibank, Inc. Singapore',
    licenceType: 'Wholesale Bank',
    code: 'bdo-unibank-inc-singapore',
    countryCodes: ['PH'],
  },
  {
    name: 'BNP Paribas',
    licenceType: 'Qualifying Full Bank',
    code: 'bnp-paribas',
    countryCodes: ['FR'],
  },
  {
    name: 'CA Indosuez (Switzerland) SA, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'ca-indosuez-switzerland-sa-singapore-branch',
    countryCodes: ['CH'],
  },
  {
    name: 'Canadian Imperial Bank of Commerce',
    licenceType: 'Wholesale Bank',
    code: 'canadian-imperial-bank-of-commerce',
    countryCodes: ['CA'],
  },
  {
    name: 'Cathay United Bank, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'cathay-united-bank-singapore-branch',
    countryCodes: ['TW'],
  },
  {
    name: 'Chang Hwa Commercial Bank Limited',
    licenceType: 'Wholesale Bank',
    code: 'chang-hwa-commercial-bank-limited',
    countryCodes: ['TW'],
  },
  {
    name: 'China CITIC Bank International Limited',
    licenceType: 'Wholesale Bank',
    code: 'china-citic-bank-international-limited',
    countryCodes: ['HK'],
  },
  {
    name: 'China Construction Bank Corporation',
    licenceType: 'Qualifying Full Bank',
    code: 'china-construction-bank-corporation',
    countryCodes: ['CN'],
  },
  {
    name: 'China Merchants Bank Co., Ltd',
    licenceType: 'Wholesale Bank',
    code: 'china-merchants-bank-co-ltd',
    countryCodes: ['CN'],
  },
  {
    name: 'CIMB Bank Berhad',
    licenceType: 'Full Bank',
    code: 'cimb-bank-berhad',
    countryCodes: ['MY'],
  },
  {
    name: 'Citibank NA',
    licenceType: 'Full Bank',
    code: 'citibank-na',
    countryCodes: ['US'],
  },
  {
    name: 'Citibank Singapore Limited (Second Vice Chairman)',
    licenceType: 'Qualifying Full Bank',
    code: 'citibank-singapore-limited-second-vice-chairman',
    countryCodes: ['SG'],
  },
  {
    name: 'Clearstream Banking S.A. Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'clearstream-banking-sa-singapore-branch',
    countryCodes: ['LU'],
  },
  {
    name: 'Commerzbank AG',
    licenceType: 'Wholesale Bank',
    code: 'commerzbank-ag',
    countryCodes: ['DE'],
  },
  {
    name: 'Commonwealth Bank of Australia',
    licenceType: 'Wholesale Bank',
    code: 'commonwealth-bank-of-australia',
    countryCodes: ['AU'],
  },
  {
    name: 'Crédit Agricole Corporate and Investment Bank',
    licenceType: 'Full Bank',
    code: 'crdit-agricole-corporate-and-investment-bank',
    countryCodes: ['FR'],
  },
  {
    name: 'Credit Industriel ET Commercial',
    licenceType: 'Wholesale Bank',
    code: 'credit-industriel-et-commercial',
    countryCodes: ['FR'],
  },
  {
    name: 'Credit Suisse AG',
    licenceType: 'Wholesale Bank',
    code: 'credit-suisse-ag',
    countryCodes: ['CH'],
  },
  {
    name: 'CTBC Bank Co., Ltd',
    licenceType: 'Wholesale Bank',
    code: 'ctbc-bank-co-ltd',
    countryCodes: ['TW'],
  },
  {
    name: 'Deutsche Bank AG, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'deutsche-bank-ag-singapore-branch',
    countryCodes: ['DE'],
  },
  {
    name: 'DNB Bank ASA',
    licenceType: 'Wholesale Bank',
    code: 'dnb-bank-asa',
    countryCodes: ['NO'],
  },
  {
    name: 'DZ Bank AG Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'dz-bank-ag-singapore-branch',
    countryCodes: ['DE'],
  },
  {
    name: 'E.SUN Commercial Bank, Ltd.',
    licenceType: 'Wholesale Bank',
    code: 'esun-commercial-bank-ltd',
    countryCodes: ['TW'],
  },
  {
    name: 'EFG Bank AG Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'efg-bank-ag-singapore-branch',
    countryCodes: ['CH'],
  },
  {
    name: 'Emirates NBD Bank (P.J.S.C) Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'emirates-nbd-bank-pjsc-singapore-branch',
    countryCodes: ['AE'],
  },
  {
    name: 'First Abu Dhabi Bank PJSC Singapore branch',
    licenceType: 'Wholesale Bank',
    code: 'first-abu-dhabi-bank-pjsc-singapore-branch',
    countryCodes: ['AE'],
  },
  {
    name: 'First Commercial Bank',
    licenceType: 'Wholesale Bank',
    code: 'first-commercial-bank',
    countryCodes: ['TW'],
  },
  {
    name: 'Green Link Digital Bank Pte. Ltd.',
    licenceType: 'Digital Wholesale Bank',
    code: 'green-link-digital-bank-pte-ltd',
    countryCodes: ['SG'],
  },
  {
    name: 'GXS Bank Pte. Ltd.',
    licenceType: 'Digital Full Bank',
    code: 'gxs-bank-pte-ltd',
    countryCodes: ['SG'],
  },
  {
    name: 'Habib Bank Limited',
    licenceType: 'Wholesale Bank',
    code: 'habib-bank-limited',
    countryCodes: ['PK'],
  },
  {
    name: 'Hang Seng Bank Limited',
    licenceType: 'Wholesale Bank',
    code: 'hang-seng-bank-limited',
    countryCodes: ['HK'],
  },
  {
    name: 'HL Bank',
    licenceType: 'Full Bank',
    code: 'hl-bank',
    countryCodes: ['MY'],
  },
  {
    name: 'HSBC Bank (Singapore) Limited',
    licenceType: 'Qualifying Full Bank',
    code: 'hsbc-bank-singapore-limited',
    countryCodes: ['SG'],
  },
  {
    name: 'Hua Nan Commercial Bank Ltd',
    licenceType: 'Wholesale Bank',
    code: 'hua-nan-commercial-bank-ltd',
    countryCodes: ['TW'],
  },
  {
    name: 'ICBC Standard Bank PLC Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'icbc-standard-bank-plc-singapore-branch',
    countryCodes: ['GB'],
  },
  {
    name: 'ICICI Bank Limited',
    licenceType: 'Qualifying Full Bank',
    code: 'icici-bank-limited',
    countryCodes: ['IN'],
  },
  {
    name: 'Indian Bank',
    licenceType: 'Full Bank',
    code: 'indian-bank',
    countryCodes: ['IN'],
  },
  {
    name: 'Indian Overseas Bank',
    licenceType: 'Full Bank',
    code: 'indian-overseas-bank',
    countryCodes: ['IN'],
  },
  {
    name: 'Industrial and Commercial Bank of China Limited Singapore Branch',
    licenceType: 'Qualifying Full Bank',
    code: 'industrial-and-commercial-bank-of-china-limited-singapore-branch',
    countryCodes: ['CN'],
  },
  {
    name: 'ING Bank N.V., Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'ing-bank-nv-singapore-branch',
    countryCodes: ['NL'],
  },
  {
    name: 'Intesa Sanpaolo SPA',
    licenceType: 'Wholesale Bank',
    code: 'intesa-sanpaolo-spa',
    countryCodes: ['IT'],
  },
  {
    name: 'JP Morgan Chase Bank, N.A.',
    licenceType: 'Full Bank',
    code: 'jp-morgan-chase-bank-na',
    countryCodes: ['US'],
  },
  {
    name: 'KBC Bank N.V.',
    licenceType: 'Wholesale Bank',
    code: 'kbc-bank-nv',
    countryCodes: ['BE'],
  },
  {
    name: 'KEB Hana Bank, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'keb-hana-bank-singapore-branch',
    countryCodes: ['KR'],
  },
  {
    name: 'Kookmin Bank Co., Ltd. Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'kookmin-bank-co-ltd-singapore-branch',
    countryCodes: ['KR'],
  },
  {
    name: 'Korea Development Bank, The',
    licenceType: 'Wholesale Bank',
    code: 'korea-development-bank-the',
    countryCodes: ['KR'],
  },
  {
    name: 'Krung Thai Bank Public Company Limited',
    licenceType: 'Wholesale Bank',
    code: 'krung-thai-bank-public-company-limited',
    countryCodes: ['TH'],
  },
  {
    name: 'Land Bank of Taiwan',
    licenceType: 'Wholesale Bank',
    code: 'land-bank-of-taiwan',
    countryCodes: ['TW'],
  },
  {
    name: 'Landesbank Baden-Wuerttemberg (LBBW)',
    licenceType: 'Wholesale Bank',
    code: 'landesbank-baden-wuerttemberg-lbbw',
    countryCodes: ['DE'],
  },
  {
    name: 'Macquarie Bank Limited Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'macquarie-bank-limited-singapore-branch',
    countryCodes: ['AU'],
  },
  {
    name: 'Malayan Banking Berhad, Singapore Branch',
    licenceType: 'Full Bank',
    code: 'malayan-banking-berhad-singapore-branch',
    countryCodes: ['MY'],
  },
  {
    name: 'MariBank Singapore Private Limited',
    licenceType: 'Digital Full Bank',
    code: 'maribank-singapore-private-limited',
    countryCodes: ['SG'],
  },
  {
    name: 'Maybank Singapore Limited (Vice Chairman)',
    licenceType: 'Qualifying Full Bank',
    code: 'maybank-singapore-limited-vice-chairman',
    countryCodes: ['SG'],
  },
  {
    name: 'Mega International Commercial Bank Co., Ltd',
    licenceType: 'Wholesale Bank',
    code: 'mega-international-commercial-bank-co-ltd',
    countryCodes: ['TW'],
  },
  {
    name: 'Mitsubishi UFJ Trust and Banking Corporation',
    licenceType: 'Wholesale Bank',
    code: 'mitsubishi-ufj-trust-and-banking-corporation',
    countryCodes: ['JP'],
  },
  {
    name: 'Mizuho Bank, Ltd',
    licenceType: 'Full Bank',
    code: 'mizuho-bank-ltd',
    countryCodes: ['JP'],
  },
  {
    name: 'Morgan Stanley Bank Asia Limited',
    licenceType: 'Wholesale Bank',
    code: 'morgan-stanley-bank-asia-limited',
    countryCodes: ['HK'],
  },
  {
    name: 'MUFG Bank, Ltd,.',
    licenceType: 'Full Bank',
    code: 'mufg-bank-ltd',
    countryCodes: ['JP'],
  },
  {
    name: 'National Australia Bank Limited',
    licenceType: 'Wholesale Bank',
    code: 'national-australia-bank-limited',
    countryCodes: ['AU'],
  },
  {
    name: 'National Bank of Kuwait S.A.K. P. Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'national-bank-of-kuwait-sak-p-singapore-branch',
    countryCodes: ['KW'],
  },
  {
    name: 'Natixis',
    licenceType: 'Wholesale Bank',
    code: 'natixis',
    countryCodes: ['FR'],
  },
  {
    name: 'NatWest Market PLC',
    licenceType: 'Wholesale Bank',
    code: 'natwest-market-plc',
    countryCodes: ['GB'],
  },
  {
    name: 'Norddeutsche Landesbank Girozentrale',
    licenceType: 'Wholesale Bank',
    code: 'norddeutsche-landesbank-girozentrale',
    countryCodes: ['DE'],
  },
  {
    name: 'Norinchukin Bank, The',
    licenceType: 'Wholesale Bank',
    code: 'norinchukin-bank-the',
    countryCodes: ['JP'],
  },
  {
    name: 'Northern Trust Company, The',
    licenceType: 'Wholesale Bank',
    code: 'northern-trust-company-the',
    countryCodes: ['US'],
  },
  {
    name: 'P.T. Bank Negara Indonesia (Persero) TBK',
    licenceType: 'Full Bank',
    code: 'pt-bank-negara-indonesia-persero-tbk',
    countryCodes: ['ID'],
  },
  {
    name: 'Philippine National Bank',
    licenceType: 'Wholesale Bank',
    code: 'philippine-national-bank',
    countryCodes: ['PH'],
  },
  {
    name: 'PT Bank Mandiri (Persero) TBK',
    licenceType: 'Wholesale Bank',
    code: 'pt-bank-mandiri-persero-tbk',
    countryCodes: ['ID'],
  },
  {
    name: 'PT Bank Rakyat Indonesia (Persero) Tbk, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'pt-bank-rakyat-indonesia-persero-tbk-singapore-branch',
    countryCodes: ['ID'],
  },
  {
    name: 'Qatar National Bank (Q.P.S.C.)',
    licenceType: 'Wholesale Bank',
    code: 'qatar-national-bank-qpsc',
    countryCodes: ['QA'],
  },
  {
    name: 'Rabobank Singapore',
    licenceType: 'Wholesale Bank',
    code: 'rabobank-singapore',
    countryCodes: ['NL'],
  },
  {
    name: 'Raiffeisen Bank International AG, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'raiffeisen-bank-international-ag-singapore-branch',
    countryCodes: ['AT'],
  },
  {
    name: 'RHB Bank Berhad',
    licenceType: 'Full Bank',
    code: 'rhb-bank-berhad',
    countryCodes: ['MY'],
  },
  {
    name: 'Royal Bank of Canada',
    licenceType: 'Wholesale Bank',
    code: 'royal-bank-of-canada',
    countryCodes: ['CA'],
  },
  {
    name: 'Shanghai Pudong Development Bank Co. Ltd, Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'shanghai-pudong-development-bank-co-ltd-singapore-branch',
    countryCodes: ['CN'],
  },
  {
    name: 'Shinhan Bank',
    licenceType: 'Wholesale Bank',
    code: 'shinhan-bank',
    countryCodes: ['KR'],
  },
  {
    name: 'Siam Commercial Bank Public Company Limited, The',
    licenceType: 'Wholesale Bank',
    code: 'siam-commercial-bank-public-company-limited-the',
    countryCodes: ['TH'],
  },
  {
    name: 'Skandinaviska Enskilda Banken AB (publ)',
    licenceType: 'Wholesale Bank',
    code: 'skandinaviska-enskilda-banken-ab-publ',
    countryCodes: ['SE'],
  },
  {
    name: 'Societe Generale',
    licenceType: 'Wholesale Bank',
    code: 'societe-generale',
    countryCodes: ['FR'],
  },
  {
    name: 'Standard Chartered Bank (Singapore)',
    licenceType: 'Qualifying Full Bank',
    code: 'standard-chartered-bank-singapore',
    countryCodes: ['SG'],
  },
  {
    name: 'State Bank of India',
    licenceType: 'Qualifying Full Bank',
    code: 'state-bank-of-india',
    countryCodes: ['IN'],
  },
  {
    name: 'State Street Bank and Trust Company',
    licenceType: 'Wholesale Bank',
    code: 'state-street-bank-and-trust-company',
    countryCodes: ['US'],
  },
  {
    name: 'Sumitomo Mitsui Banking Corporation',
    licenceType: 'Full Bank',
    code: 'sumitomo-mitsui-banking-corporation',
    countryCodes: ['JP'],
  },
  {
    name: 'Sumitomo Mitsui Trust Bank, Limited Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'sumitomo-mitsui-trust-bank-limited-singapore-branch',
    countryCodes: ['JP'],
  },
  {
    name: 'Taipei Fubon Commercial Bank Co., Ltd',
    licenceType: 'Wholesale Bank',
    code: 'taipei-fubon-commercial-bank-co-ltd',
    countryCodes: ['TW'],
  },
  {
    name: 'Taishin International Bank Co. Ltd',
    licenceType: 'Wholesale Bank',
    code: 'taishin-international-bank-co-ltd',
    countryCodes: ['TW'],
  },
  {
    name: 'The Bank of Yokohama, Ltd. Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'the-bank-of-yokohama-ltd-singapore-branch',
    countryCodes: ['JP'],
  },
  {
    name: 'The Iyo Bank, Ltd., Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'the-iyo-bank-ltd-singapore-branch',
    countryCodes: ['JP'],
  },
  {
    name: 'The Saudi National Bank Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'the-saudi-national-bank-singapore-branch',
    countryCodes: ['SA'],
  },
  {
    name: 'The Shanghai Commercial & Savings Bank, Ltd. Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'the-shanghai-commercial-amp-savings-bank-ltd-singapore-branch',
    countryCodes: ['TW'],
  },
  {
    name: 'Toronto-Dominion Bank, The',
    licenceType: 'Wholesale Bank',
    code: 'toronto-dominion-bank-the',
    countryCodes: ['CA'],
  },
  {
    name: 'Trust Bank Singapore Limited',
    licenceType: 'Full Bank',
    code: 'trust-bank-singapore-limited',
    countryCodes: ['SG'],
  },
  {
    name: 'UCO Bank',
    licenceType: 'Full Bank',
    code: 'uco-bank',
    countryCodes: ['IN'],
  },
  {
    name: 'UniCredit Bank AG',
    licenceType: 'Wholesale Bank',
    code: 'unicredit-bank-ag',
    countryCodes: ['DE'],
  },
  {
    name: 'Union Bancaire Privee, UBP SA Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'union-bancaire-privee-ubp-sa-singapore-branch',
    countryCodes: ['CH'],
  },
  {
    name: 'Union de Banques Arabes et Francaises',
    licenceType: 'Wholesale Bank',
    code: 'union-de-banques-arabes-et-francaises',
    countryCodes: ['FR'],
  },
  {
    name: 'VP Bank Ltd Singapore Branch',
    licenceType: 'Wholesale Bank',
    code: 'vp-bank-ltd-singapore-branch',
    countryCodes: ['SG'],
  },
  {
    name: 'Wells Fargo Bank, National Association',
    licenceType: 'Wholesale Bank',
    code: 'wells-fargo-bank-national-association',
    countryCodes: ['US'],
  },
  {
    name: 'Westpac Banking Corporation',
    licenceType: 'Wholesale Bank',
    code: 'westpac-banking-corporation',
    countryCodes: ['AU'],
  },
  {
    name: 'Woori Bank',
    licenceType: 'Wholesale Bank',
    code: 'woori-bank',
    countryCodes: ['KR'],
  },
  {
    name: 'Axis Bank Limited, Singapore Branch',
    licenceType: 'Merchant Bank',
    code: 'axis-bank-limited-singapore-branch',
    countryCodes: [],
  },
  {
    name: 'Bordier & CIE (Singapore) Ltd',
    licenceType: 'Merchant Bank',
    code: 'bordier-amp-cie-singapore-ltd',
    countryCodes: [],
  },
  {
    name: 'Daiwa Capital Markets Singapore Limited',
    licenceType: 'Merchant Bank',
    code: 'daiwa-capital-markets-singapore-limited',
    countryCodes: [],
  },
  {
    name: 'Deloitte & Touche Corporate Finance Pte Ltd',
    licenceType: 'Capital Market Services',
    code: 'deloitte-amp-touche-corporate-finance-pte-ltd',
    countryCodes: [],
  },
  {
    name: 'Goldman Sachs (Singapore) Pte.',
    licenceType: 'Capital Market Services',
    code: 'goldman-sachs-singapore-pte',
    countryCodes: [],
  },
  {
    name: 'Hong Leong Finance Limited',
    licenceType: 'Finance Company',
    code: 'hong-leong-finance-limited',
    countryCodes: [],
  },
  {
    name: 'Islamic Bank of Asia Limited, The',
    licenceType: 'Merchant Bank',
    code: 'islamic-bank-of-asia-limited-the',
    countryCodes: [],
  },
  {
    name: 'KEXIM Global (Singapore) Ltd.',
    licenceType: 'Merchant Bank',
    code: 'kexim-global-singapore-ltd',
    countryCodes: ['SG'],
  },
  {
    name: 'KfW IPEX-Bank Asia Ltd.',
    licenceType: 'Merchant Bank',
    code: 'kfw-ipex-bank-asia-ltd',
    countryCodes: ['DE'],
  },
  {
    name: 'LGT Bank (Singapore) Ltd',
    licenceType: 'Merchant Bank',
    code: 'lgt-bank-singapore-ltd',
    countryCodes: [],
  },
  {
    name: 'Lombard Odier (Singapore) Ltd',
    licenceType: 'Merchant Bank',
    code: 'lombard-odier-singapore-ltd',
    countryCodes: [],
  },
  {
    name: 'MUFG Securities Asia Limited, Singapore Branch',
    licenceType: 'Capital Market Services',
    code: 'mufg-securities-asia-limited-singapore-branch',
    countryCodes: [],
  },
  {
    name: 'Nomura Singapore Limited',
    licenceType: 'Merchant Bank',
    code: 'nomura-singapore-limited',
    countryCodes: [],
  },
  {
    name: 'PricewaterhouseCoopers Corporate Finance Pte Ltd',
    licenceType: 'Capital Market Services',
    code: 'pricewaterhousecoopers-corporate-finance-pte-ltd',
    countryCodes: [],
  },
  {
    name: 'PrimePartners Corporate Finance Pte Ltd',
    licenceType: 'Capital Market Services',
    code: 'primepartners-corporate-finance-pte-ltd',
    countryCodes: [],
  },
  {
    name: 'Resona Merchant Bank Asia Limited',
    licenceType: 'Merchant Bank',
    code: 'resona-merchant-bank-asia-limited',
    countryCodes: [],
  },
  {
    name: 'Schroder & Co. (Asia) Limited',
    licenceType: 'Merchant Bank',
    code: 'schroder-amp-co-asia-limited',
    countryCodes: [],
  },
  {
    name: 'Siemens Bank GmbH Singapore Branch',
    licenceType: 'Merchant Bank',
    code: 'siemens-bank-gmbh-singapore-branch',
    countryCodes: [],
  },
  {
    name: 'Xandar Capital Pte Ltd',
    licenceType: 'Capital Market Services',
    code: 'xandar-capital-pte-ltd',
    countryCodes: [],
  },
  {
    name: 'ZICO Capital Pte Ltd',
    licenceType: 'Capital Market Services',
    code: 'zico-capital-pte-ltd',
    countryCodes: [],
  },
  {
    name: 'Bank Central Asia',
    licenceType: 'Representative Office',
    code: 'bank-central-asia',
    countryCodes: ['ID'],
  },
  {
    name: 'Euroclear Bank',
    licenceType: 'Representative Office',
    code: 'euroclear-bank',
    countryCodes: ['BE'],
  },
  {
    name: 'Joint Stock Commercial Bank for Foreign Trade of Vietnam',
    licenceType: 'Representative Office',
    code: 'joint-stock-commercial-bank-for-foreign-trade-of-vietnam',
    countryCodes: ['VN'],
  },
  {
    name: 'Zürcher Kantonalbank',
    licenceType: 'Representative Office',
    code: 'zrcher-kantonalbank',
    countryCodes: ['CH'],
  },
];
