/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { UserPlain } from "../../models/user";
import { InitiativePlain } from "../../models/initiative";
import {
  getDashboardByAppConfigCode,
  getUtrvFiltersFromDashboardFilters,
  PreloadQueryOptions,
  StaticDashboardType,
} from '../insight-dashboard/utils';
import { SurveyType } from "../../models/survey";
import {
  ExtendedDashboard,
  HistoricalUtrs,
  InsightDashboard,
  InsightDashboardPlain,
  InsightDashboardType,
  PrivacyFilter,
  SurveyFilter,
  UtrvFilter
} from "../../models/insightDashboard";
import { ObjectId } from "bson";
import { DataSharePermissions } from "../share/DataSharePermissions";
import { DataScopeAccess, RequesterType } from "../../models/dataShare";
import { DownloadScope } from "../survey/scope/downloadScope";
import { SurveyScope } from "../survey/SurveyScope";
import { UniversalTrackerRepository } from "../../repository/UniversalTrackerRepository";
import { UniversalTrackerPlain } from "../../models/universalTracker";
import { KeysEnum } from "../../models/public/projectionUtils";
import {
  getUniversalTrackerHistoricalDataManager,
  UniversalTrackerHistoricalDataManager,
} from '../utr/historical-data/UniversalTrackerHistoricalDataManager';
import { wwgLogger } from '../wwgLogger';
import { getPortfolioDashboardService, PortfolioDashboardService } from './PortfolioDashboardService';
import { DashboardItemManager, getDashboardItemManager } from '../insight-dashboard/DashboardItemManager';

export interface PortfolioCompanyDashboardLookupData {
  portfolioId: ObjectId;
  dashboardType: StaticDashboardType;
  initiative: InitiativePlain;
  user: UserPlain;
  filters: PreloadQueryOptions;
}

interface GetDashboardParams {
  dashboardType: StaticDashboardType;
  initiative: InitiativePlain;
  user: UserPlain;
  hasScorecard: boolean;
}

type PopulateUtrDataParams = {
  dashboard: InsightDashboardPlain;
  initiativeId: ObjectId | string;
  filters: PreloadQueryOptions;
  utrCodes: string[];
};

interface UtrCodesFiltering {
  user: UserPlain;
  portfolioId: ObjectId;
  initiative: InitiativePlain;
  dashboard: Pick<InsightDashboardPlain, "items">;
}

type MinUtrLookup = Pick<UniversalTrackerPlain, '_id' | 'code'>;
const minUtrProjection: KeysEnum<MinUtrLookup> = {
  _id: 1,
  code: 1,
}

export class PortfolioCompaniesDashboardService {

  constructor(
    private historicalDataManager: UniversalTrackerHistoricalDataManager,
    private portfolioDashboardService: PortfolioDashboardService,
    private dashboardItemManager: DashboardItemManager,
  ) {
  }


  public async getSummaryDashboard(lookupData: PortfolioCompanyDashboardLookupData): Promise<ExtendedDashboard> {
    const { portfolioId, dashboardType, initiative, user, filters } = lookupData;

    const dashboard = await this.getDashboard({
      dashboardType: dashboardType,
      initiative: initiative,
      user: user,
      hasScorecard: false, // Never need to load scorecard for PT
    });

    const utrCodes = await this.getUtrCodes({
      user,
      portfolioId,
      initiative,
      dashboard: dashboard
    });

    return this.populateUtrData({
      dashboard,
      initiativeId: initiative._id,
      utrCodes,
      filters: {
        surveyType: SurveyType.Default,
        ...filters,
      }
    });
  }

  /**
   * Get utr codes based on data-share scope access.
   * Ensure we are not returning more data than we should.
   */
  private async getUtrCodes({ user, portfolioId, initiative, dashboard }: UtrCodesFiltering): Promise<string[]> {
    const requiredUtrCodes = dashboard.items.reduce((acc, item) => {
      if (item.variables) {
        Object.values(item.variables).forEach((variable) => acc.add(variable.code));
      }
      return acc;
    }, new Set<string>());

    if (requiredUtrCodes.size === 0) {
      return [];
    }
    const dataScopeAccess = await DataSharePermissions.getDataScopeAccess({
      user,
      requesterType: RequesterType.Portfolio,
      requesterId: portfolioId,
      initiativeId: initiative._id,
    });

    if (dataScopeAccess.access === DataScopeAccess.Full) {
      return Array.from(requiredUtrCodes);
    }

    const utrMatch = await DownloadScope.generateMultiScopeMatch(
      {
        access: dataScopeAccess.access,
        scope: SurveyScope.toStringScope(dataScopeAccess.scope),
      },
      '',
      '_id',
    );

    const utrs = await UniversalTrackerRepository.getBlueprintUtrs<MinUtrLookup>(utrMatch, minUtrProjection)

    return utrs.reduce((acc, utr) => {
      // From available utr codes, only that one that were request should be returned
      if (requiredUtrCodes.has(utr.code)) {
        acc.push(utr.code);
      }
      return acc;
    }, [] as string[]);
  }

  private async getUtrData({
    dashboard,
    filters,
    initiativeId,
    utrCodes,
  }: {
    dashboard: InsightDashboardPlain;
    initiativeId: ObjectId | string;
    filters: PreloadQueryOptions;
    utrCodes: string[];
  }) {
    if (utrCodes.length === 0) {
      return [];
    }

    const universalTrackers = await UniversalTrackerRepository.populateValueValidationByCodes(utrCodes);
    if (universalTrackers.length === 0) {
      return [];
    }

    const utrIds = universalTrackers.map((utr) => String(utr._id));
    const utrvFilters = getUtrvFiltersFromDashboardFilters({ filters: dashboard.filters, additionalFilters: filters });

    const now = Date.now();
    const dashboardId = dashboard._id.toString();
    const inputInitiativeId = initiativeId;

    const utrsData = await this.historicalDataManager.getUtrsHistoricalData({
      initiativeId: inputInitiativeId,
      utrIds,
      utrvFilters,
    });

    const totalTime = Date.now() - now;
    wwgLogger.info(`Processed dashboard ${dashboardId} loading. Took ${totalTime}ms`, {
      totalTime,
      dashboardId: dashboardId,
      initiativeId: inputInitiativeId.toString(),
      ...utrvFilters,
      utrCount: utrIds.length,
    });

    return utrsData;
  }

  public async populateUtrData(params: PopulateUtrDataParams): Promise<ExtendedDashboard> {
    const { dashboard, initiativeId, filters, utrCodes } = params;
    const utrsData: HistoricalUtrs[] = await this.getUtrData({ dashboard, filters, initiativeId, utrCodes });
    return {
      _id: dashboard._id,
      type: dashboard.type ?? InsightDashboardType.Custom,
      creatorId: dashboard.creatorId,
      initiativeId: dashboard.initiativeId,
      title: dashboard.title,
      filters: dashboard.filters,
      items: dashboard.items,
      share: dashboard.share,
      utrsData,
    };
  }

  private async getDashboard({ dashboardType, initiative, user, hasScorecard }: GetDashboardParams) {
    const dashboard = getDashboardByAppConfigCode({
      type: dashboardType,
      initiativeId: initiative._id,
      userId: user._id,
      appConfigCode: initiative.appConfigCode
    });

    const summary = await InsightDashboard
      .findOne({ initiativeId: initiative._id, type: dashboardType })
      .lean<InsightDashboardPlain>()
      .exec();

    const summaryDashboard = summary
      ? { ...summary, items: [...dashboard.items, ...summary.items] }
      : dashboard;

    return {
      ...summaryDashboard,
      filters: {
        ...summaryDashboard.filters,
        utrv: UtrvFilter.Verified,
        survey: SurveyFilter.Completed,
        privacy: PrivacyFilter.Public,
        sdgContribution: { enabled: hasScorecard },
      }
    } satisfies InsightDashboardPlain;
  }

  public async getDefaultDashboards(portfolioId: string) {
    const dashboards = await InsightDashboard.find(
      {
        initiativeId: portfolioId,
        'filters.displayAsDefault.enabled': { $eq: true },
      },
      {
        _id: 1,
        title: 1,
      }
    )
      .lean<Pick<InsightDashboardPlain, '_id' | 'title'>[]>()
      .exec();
    return dashboards;
  }

  public async getPortfolioCompanyInsightsDashboard({
    portfolio,
    initiativeId,
    dashboardId,
  }: {
    portfolio: InitiativePlain;
    initiativeId: string;
    dashboardId: string;
  }) {
    const dashboard = await InsightDashboard.findOne({
      _id: dashboardId,
      initiativeId: portfolio._id,
      'filters.displayAsDefault.enabled': { $eq: true },
    })
      .orFail()
      .lean<InsightDashboardPlain>()
      .exec();

    const utrPopulatedDashboardItems = await this.portfolioDashboardService.populateUtrDataByPortfolioId({
      dashboard,
      portfolio,
      filters: {
        dataShareInitiativeId: initiativeId,
      },
    });

    const dashboardWithFiles = await this.dashboardItemManager.populateFiles(utrPopulatedDashboardItems);

    const dashBoardWithScoreCard = await this.portfolioDashboardService.populateScorecard(
      dashboardWithFiles,
      portfolio
    );

    return dashBoardWithScoreCard;
  }
}

let instance: PortfolioCompaniesDashboardService;
export const getPortfolioCompaniesDashboardService = () => {
  if (!instance) {
    instance = new PortfolioCompaniesDashboardService(
      getUniversalTrackerHistoricalDataManager(),
      getPortfolioDashboardService(),
      getDashboardItemManager(),
    );
  }
  return instance;
}
