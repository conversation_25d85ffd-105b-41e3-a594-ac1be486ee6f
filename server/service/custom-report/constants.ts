import { SurveyFilter } from '../../models/customReport';
import { InitiativePlain } from '../../models/initiative';
import { SurveyModelPlain } from '../../models/survey';
import { InitiativeSurveyStatsQuestionStatus } from '../initiative/InitiativeStatsService';
import { ActionList, DataPeriods } from '../utr/constants';

export const DATA_PERIODS_UI_MAP = {
  [DataPeriods.Monthly]: 'Monthly',
  [DataPeriods.Quarterly]: 'Quarterly',
  [DataPeriods.Yearly]: 'Annual',
} as const;

export const STATUSES_UI_MAP = {
  [ActionList.Created]: 'Not submitted',
  [ActionList.Updated]: 'Submitted',
  [ActionList.Rejected]: 'Rejected',
  [ActionList.Verified]: 'Verified',
} as const;

export type RecordRow = unknown[];

export const BATCH_SIZE = 10;

export const DECIMAL_PLACES = 2;

export interface InitiativeMapType extends Omit<InitiativeSurveyStatsQuestionStatus, 'status' | 'surveys'> {
  parentNames: string[];
  type: string;
}

export type InitiativeWithMatchedSurveyFilters = Pick<InitiativePlain, '_id' | 'name'> & {
  matchedSurveyFilters: SurveyFilter[];
};

export type SurveyMapType = Pick<SurveyModelPlain, '_id' | 'effectiveDate' | 'name' | 'unitConfig' | 'period' | 'type'>;
