/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const CT_STARTER_SOCIAL_SLIDE_UTRS = {
  26: {
    utrs: [{ utrCode: 'gri/2020/401-1/a', columnCodes: ['gender', 'total_number_new_hires'] }],
  },
  27: {
    utrs: [{ utrCode: 'gri/2020/404-1/a', columnCodes: ['male', 'female'] }],
  },
  28: {
    utrs: [
      { utrCode: 'gri/2020/405-1/b', columnCodes: ['employees_per_category', 'under_30', 'age_30_50', 'over_50'] },
      { utrCode: 'gri/2020/401-1/b', columnCodes: ['total_turnover'] },
      { utrCode: 'gri/2020/401-1/a', columnCodes: ['under_30', 'age_30_50', 'over_50'] },
    ],
  },
  29: {
    utrs: [
      {
        utrCode: 'gri/2020/403-9/a',
        columnCodes: [
          'injuries_company_employees_number_fatalities',
          'injuries_company_employees_high-consequence_work-related_injuries_number',
          'injuries_company_employees_recordable_injuries_number',
        ],
      },
      { utrCode: 'gri/2020/403-10/a' },
      { utrCode: 'gri/2020/401-1/a' },
    ],
  },
};

export const getPPTXConfigSocial = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Social
    slideId: 25,
  },
  // Staffing: New Hires
  {
    slideId: 26,
    appendix: async () => CT_STARTER_SOCIAL_SLIDE_UTRS[26].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a', 26).toJSONString();
            const response = await builder
              .createAIBuilder()
              .ask(
                hires
                  ? [
                      `As a company my new staff hires in JSON format are:`,
                      hires,
                      `Write a narrative about this but focus on the gender breakdown.`,
                    ]
                  : 'As a company I have not reported my new staff hires.'
              )
              .addTarget('gri/2020/401-1/a')
              .addFurtherExplanation('gri/2020/401-1/a')
              .and(
                'Be mindful not to use language that would be contravene gender and discrimination policies in your narrative.'
              )
              .max(300)
              .exec();
            builder.setKeyStore('NewHires', response);
            const words = response.split(' ') ?? [];
            return words.slice(0, 100).join(' ');
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => ({
            series: [
              {
                label: 'Male',
              },
              {
                label: 'Female',
              },
            ],
            categories: [
              {
                label: String(await builder.getYear(-1)),
                values: [
                  (await builder
                    .createUTRTableBuilder('gri/2020/401-1/a', 26)
                    .periodOffset(-1)
                    .filterByColumn('gender', ['male1'])
                    .sum(['total_number_new_hires'])) ?? '',
                  (await builder
                    .createUTRTableBuilder('gri/2020/401-1/a', 26)
                    .periodOffset(-1)
                    .filterByColumn('gender', ['females1'])
                    .sum(['total_number_new_hires'])) ?? '',
                ],
              },
              {
                label: String(await builder.getYear()),
                values: [
                  (await builder
                    .createUTRTableBuilder('gri/2020/401-1/a', 26)
                    .filterByColumn('gender', ['male1'])
                    .sum(['total_number_new_hires'])) ?? '',
                  (await builder
                    .createUTRTableBuilder('gri/2020/401-1/a', 26)
                    .filterByColumn('gender', ['females1'])
                    .sum(['total_number_new_hires'])) ?? '',
                ],
              },
            ],
          }),
        },
      ],
    ],
  },
  // Staffing: Average training hours
  {
    slideId: 27,
    appendix: async () => CT_STARTER_SOCIAL_SLIDE_UTRS[27].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const avgTrainingHours = await builder.createUTRTableBuilder('gri/2020/404-1/a', 27).toJSONString();
            return builder
              .createAIBuilder()
              .ask(
                avgTrainingHours
                  ? [
                      `As a company my staff receive the following average training hours:`,
                      '',
                      avgTrainingHours,
                      '',
                      'Write a narrative about this but only focus on gender breakdown.',
                    ]
                  : 'As a company I have not reported my staff training numbers.'
              )
              .addTarget('gri/2020/404-1/a')
              .addFurtherExplanation('gri/2020/404-1/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
    ],
    chartReplacements: [
      [
        'CHART',
        {
          chartData: async () => {
            const categories = [];
            const year1 = await builder.getYear(-1);
            if (year1) {
              categories.push({
                label: String(year1),
                values: [
                  (await builder.createUTRTableBuilder('gri/2020/404-1/a', 27).periodOffset(-1).getRawValue('male')) ??
                    '',
                  (await builder
                    .createUTRTableBuilder('gri/2020/404-1/a', 27)
                    .periodOffset(-1)
                    .getRawValue('female')) ?? '',
                ],
              });
            }

            categories.push({
              label: String(await builder.getYear()),
              values: [
                (await builder.createUTRTableBuilder('gri/2020/404-1/a', 27).getRawValue('male')) ?? '',
                (await builder.createUTRTableBuilder('gri/2020/404-1/a', 27).getRawValue('female')) ?? '',
              ],
            });

            return {
              series: [
                {
                  label: 'Male',
                },
                {
                  label: 'Female',
                },
              ],
              categories,
            };
          },
        },
      ],
    ],
  },
  // Staff profile: Age
  {
    slideId: 28,
    appendix: async () => CT_STARTER_SOCIAL_SLIDE_UTRS[28].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const employees = await builder
              .createUTRTableBuilder('gri/2020/405-1/b', 28)
              .sum(['employees_per_category']);
            const employeeTurnover = await builder
              .createUTRTableBuilder('gri/2020/401-1/b', 28)
              .sum(['total_turnover']);
            return builder
              .createAIBuilder()
              .ask([
                `As a company aged based diversity is important to me.`,
                `Here is all the information on age based diversity at the company:`,
                '',
                `Current employees: ${employees}`,
                `Employee turnover: ${employeeTurnover}`,
                '',
              ])
              .addTarget('gri/2020/405-1/b', 'employees_per_category')
              .addTarget('gri/2020/401-1/b', 'total_turnover')
              .addFurtherExplanation('gri/2020/405-1/b')
              .addFurtherExplanation('gri/2020/401-1/b')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a', 28).toJSONString();
            return builder
              .createAIBuilder()
              .ask(
                hires
                  ? [
                      `As a company aged-based diversity is important to me.`,
                      `Here is some information about new hires at the company:`,
                      '',
                      hires,
                      '',
                    ]
                  : 'As a company I have not reoprted on my aged-based diversity for new hires.'
              )
              .addTarget('gri/2020/401-1/a', 'total_number_new_hires')
              .addFurtherExplanation('gri/2020/401-1/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_UNDER_30',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b', 28)
                .periodOffset(-1)
                .maxDecimals(2)
                .multiply('employees_per_category', 'under_30')
            ) ?? '',
        },
      ],
      [
        'YEAR_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_UNDER_30',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b', 28)
                .maxDecimals(2)
                .multiply('employees_per_category', 'under_30')
            ) ?? '',
        },
      ],
      [
        'CHANGE_UNDER_30',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_UNDER_30')),
              Number(builder.getKeyStore('YEAR_UNDER_30'))
            ),
        },
      ],
      [
        'PREV_YEAR_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_30_50',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b', 28)
                .periodOffset(-1)
                .maxDecimals(2)
                .multiply('employees_per_category', 'age_30_50')
            ) ?? '',
        },
      ],
      [
        'YEAR_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_30_50',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b', 28)
                .maxDecimals(2)
                .multiply('employees_per_category', 'age_30_50')
            ) ?? '',
        },
      ],
      [
        'CHANGE_30_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_30_50')),
              Number(builder.getKeyStore('YEAR_30_50'))
            ),
        },
      ],
      [
        'PREV_YEAR_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_OVER_50',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b', 28)
                .periodOffset(-1)
                .maxDecimals(2)
                .multiply('employees_per_category', 'over_50')
            ) ?? '',
        },
      ],
      [
        'YEAR_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_OVER_50',
              await builder
                .createUTRTableBuilder('gri/2020/405-1/b', 28)
                .maxDecimals(2)
                .multiply('employees_per_category', 'over_50')
            ) ?? '',
        },
      ],
      [
        'CHANGE_OVER_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_OVER_50')),
              Number(builder.getKeyStore('YEAR_OVER_50'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIRES_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIRES_UNDER_30',
              await builder.createUTRTableBuilder('gri/2020/401-1/a', 28).periodOffset(-1).sum(['under_30'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIRES_UNDER_30',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIRES_UNDER_30',
              await builder.createUTRTableBuilder('gri/2020/401-1/a', 28).sum(['under_30'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIRES_UNDER_30',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIRES_UNDER_30')),
              Number(builder.getKeyStore('YEAR_HIRES_UNDER_30'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIRES_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIRES_30_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a', 28).periodOffset(-1).sum(['age_30_50'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIRES_30_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIRES_30_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a', 28).sum(['age_30_50'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIRES_30_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIRES_30_50')),
              Number(builder.getKeyStore('YEAR_HIRES_30_50'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIRES_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIRES_OVER_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a', 28).periodOffset(-1).sum(['over_50'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIRES_OVER_50',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIRES_OVER_50',
              await builder.createUTRTableBuilder('gri/2020/401-1/a', 28).sum(['over_50'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIRES_OVER_50',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIRES_OVER_50')),
              Number(builder.getKeyStore('YEAR_HIRES_OVER_50'))
            ),
        },
      ],
    ],
  },
  {
    // Staff
    slideId: 29,
    appendix: async () => CT_STARTER_SOCIAL_SLIDE_UTRS[29].utrs.map(({ utrCode }) => utrCode),
    textReplacements: [
      [
        'COLUMN_1_BODY',
        {
          text: async () => {
            const injuries = await builder.createUTRTableBuilder('gri/2020/403-9/a', 29).toJSONString();
            const healthCases = await builder.createUTRTableBuilder('gri/2020/403-10/a', 29).toJSONString();
            return builder
              .createAIBuilder()
              .ask(
                injuries || healthCases
                  ? [
                      `As a company health and safety in the workplace is important to me.`,
                      `Here is information on Work-related injuries and fatalities:`,
                      '',
                      injuries ?? 'Not Reported',
                      '',
                      `and information on Work-related ill health cases:`,
                      '',
                      healthCases ?? 'Not Reported',
                      '',
                    ]
                  : 'As a company I have not reported on my work-related injuries of fatalities.'
              )
              .addTarget('gri/2020/403-9/a')
              .addTarget('gri/2020/403-10/a')
              .addFurtherExplanation('gri/2020/403-9/a')
              .addFurtherExplanation('gri/2020/403-10/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'COLUMN_2_TITLE',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a', 29).toJSONString();
            return hires ? 'Disability' : '';
          },
        },
      ],
      [
        'COLUMN_2_BODY',
        {
          text: async () => {
            const hires = await builder.createUTRTableBuilder('gri/2020/401-1/a', 29).toJSONString();
            if (!hires) {
              return '';
            }
            return builder
              .createAIBuilder()
              .ask(
                hires
                  ? [
                      `As a company aged-based diversity is important to me.`,
                      `Here is some information about new hires at the company:`,
                      '',
                      hires,
                      '',
                    ]
                  : 'As a company I have not reoprted on my aged-based diversity for new hires.'
              )
              .addTarget('gri/2020/401-1/a')
              .addFurtherExplanation('gri/2020/401-1/a')
              .narrative()
              .max(100)
              .exec();
          },
        },
      ],
      [
        'PREV_YEAR_FATALITIES',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_FATALITIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a', 29)
                .periodOffset(-1)
                .sum(['injuries_company_employees_number_fatalities'])
            ) ?? '',
        },
      ],
      [
        'YEAR_FATALITIES',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_FATALITIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a', 29)
                .sum(['injuries_company_employees_number_fatalities'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_FATALITIES',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_FATALITIES')),
              Number(builder.getKeyStore('YEAR_FATALITIES'))
            ),
        },
      ],
      [
        'PREV_YEAR_HIGH_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_HIGH_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a', 29)
                .periodOffset(-1)
                .sum(['injuries_company_employees_high-consequence_work-related_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'YEAR_HIGH_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_HIGH_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a', 29)
                .sum(['injuries_company_employees_high-consequence_work-related_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_HIGH_INJURIES',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_HIGH_INJURIES')),
              Number(builder.getKeyStore('YEAR_HIGH_INJURIES'))
            ),
        },
      ],
      [
        'PREV_YEAR_REC_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'PREV_YEAR_REC_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a', 29)
                .periodOffset(-1)
                .sum(['injuries_company_employees_recordable_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'YEAR_REC_INJURIES',
        {
          text: async () =>
            builder.setKeyStore(
              'YEAR_REC_INJURIES',
              await builder
                .createUTRTableBuilder('gri/2020/403-9/a', 29)
                .sum(['injuries_company_employees_recordable_injuries_number'])
            ) ?? '',
        },
      ],
      [
        'CHANGE_REC_INJURIES',
        {
          text: async () =>
            builder.getChangeText(
              Number(builder.getKeyStore('PREV_YEAR_REC_INJURIES')),
              Number(builder.getKeyStore('YEAR_REC_INJURIES'))
            ),
        },
      ],
    ],
  },
];
