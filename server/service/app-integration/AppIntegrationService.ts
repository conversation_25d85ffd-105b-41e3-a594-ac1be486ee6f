import { User<PERSON>lain } from '../../models/user';
import UserError from '../../error/UserError';
import { InitiativeModel } from '../../models/initiative';
import { CustomerManager, getCustomerManager } from '../payment/CustomerManager';
import { AppConfig } from '../app/AppConfig';
import { defaultMT } from '../app/materiality-tracker';
import { defaultCTPro } from '../app/company-tracker/defaultCTPro';
import { getUnixSeconds, oneDayInSeconds } from '../../util/date';

export class AppIntegrationService {
  constructor(private customerManager: CustomerManager) {}

  async addCompanyTracker({
    initiative,
    user,
    appConfig = defaultCTPro,
  }: {
    initiative: InitiativeModel;
    user: UserPlain;
    appConfig?: AppConfig;
  }) {
    const initiativeId = initiative._id;
    if (initiative.appConfigCode !== defaultMT.code || initiative.permissionGroup !== defaultMT.permissionGroup) {
      throw new UserError('Your organization is not valid Materiality Tracker', {
        initiativeId,
        appConfigCode: initiative.appConfigCode,
        permissionGroup: initiative.permissionGroup,
      });
    }

    const subscriptions = await this.customerManager.getSubscriptions(initiativeId);
    const integratedSubscriptions = subscriptions.filter((sub) =>
      sub.items.some((item) => item.productCode === appConfig.productCode)
    );
    if (integratedSubscriptions.length) {
      throw new UserError('Your organization has already connected Company Tracker', {
        initiativeId,
        subscriptions: integratedSubscriptions.map((sub) => sub.id),
      });
    }

    const result = await this.customerManager.createProductSubscription({
      user,
      initiative,
      productCode: appConfig.productCode,
      trialEnd: getUnixSeconds(oneDayInSeconds * 14 + 3600),
    });

    initiative.permissionGroup = appConfig.permissionGroup;
    initiative.appConfigCode = appConfig.code;
    await initiative.save();

    return result;
  }

  async addMaterialityTracker({ initiative, user }: { initiative: InitiativeModel; user: UserPlain }) {
    const initiativeId = initiative._id;
    const subscriptions = await this.customerManager.getSubscriptions(initiativeId);
    const integratedSubscription = this.customerManager.getValidSubscriptionByProduct(
      defaultMT.productCode,
      subscriptions
    );
    if (integratedSubscription) {
      throw new UserError('Your organization has already connected Materiality Tracker', {
        initiativeId,
        subscriptions: integratedSubscription.id,
      });
    }

    const result = await this.customerManager.createProductSubscription({
      user,
      initiative,
      productCode: defaultMT.productCode,
    });

    return result;
  }
}

let instance: AppIntegrationService;
export function getAppIntegrationService() {
  if (!instance) {
    instance = new AppIntegrationService(getCustomerManager());
  }
  return instance;
}
