/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../http/AuthRouter';
import { getMaterialityAssessmentManager } from '../service/materiality-assessment/MaterialityAssessmentManager';
import { populateInitiative } from '../middleware/commonMiddlewares';
import { createSurveyConfigSchema, createSurveySchema } from './validation-schemas/materiality-assessment';
import { mustValidate } from '../util/validation';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { ObjectId } from 'bson';
import { getBackgroundJobService } from '../service/background-process/BackgroundJobService';
import { MaterialityAssessmentBackgroundJobService } from '../service/materiality-assessment/MaterialityAssessmentBackgroundJobService';
import { MaterialityAssessmentService } from '../service/materiality-assessment/MaterialityAssessmentService';
import { AIModelType } from '../service/ai/AIModelFactory';
import { getPPTXReportingService } from '../service/pptx-report/PPTXReportService';
import { MTPPTXTemplateScheme, PPTXTemplateName } from '../service/pptx-report/types';
import { SupportedJobPlain } from '../service/materiality-assessment/background-job/types';
import { TaskType } from '../models/backgroundJob';
import { getMaterialityAssessmentReportService } from '../service/materiality-assessment/MaterialityAssessmentReportService';
import { FileParserType } from '../service/survey/transfer/parserTypes';
import { write } from '@sheet/core';
import { setCsvFileHeaders, setXlsxFileHeaders } from '../http/FileDownload';
import { UrlMapper } from '../service/url/UrlMapper';
import BadRequestError from "../error/BadRequestError";
import { canGenerateScore } from '../middleware/materialityAssessmentMiddlewares';
import { canAccessInsights, isMaterialitySurvey, isScoresCalculated } from '../middleware/surveyMiddlewares';
import { z } from 'zod';
import { canManageInitiative } from '../middleware/initiativeMiddlewares';
import { getMaterialityAssessmentConfigService} from '../service/materiality-assessment/MaterialityAssessmentConfigService';
import { SurveyModelPlain } from '../models/survey';
import { AssessmentType } from '../types/materiality-assessment';
import { AssessmentData, AssessmentResult, AssessmentResultType } from '../service/materiality-assessment/types';

const router = express.Router() as AuthRouter;
const materialityAssessmentManager = getMaterialityAssessmentManager();
const pptxService = getPPTXReportingService();
const materialityAssessmentReportService = getMaterialityAssessmentReportService();
const backgroundJobService = getBackgroundJobService();
const materialityAssessmentConfigService = getMaterialityAssessmentConfigService();

router.route('/initiatives/:initiativeId/surveys').get(async (req, res, next) => {
  try {
    const initiativeId = req.params.initiativeId;
    const surveys = await materialityAssessmentManager.listSurveys(initiativeId);
    res.FromModel(surveys);
  } catch (e) {
    next(e);
  }
});

router
  .route('/initiatives/:initiativeId/context')
  .get(async (req, res, next) => {
    try {
      const utrs = await materialityAssessmentManager.getContextUtrs();

      res.FromModel({
        utrs,
      });
    } catch (e) {
      next(e);
    }
  });

  router
  .route('/initiatives/:initiativeId/create')
  .post(populateInitiative, async (req, res, next) => {
    try {
      if (!req.initiative?._id) {
        return next(new PermissionDeniedError());
      }
      const { effectiveDate, referralCode, returnUrl, assessmentType } = mustValidate(
        req.body,
        createSurveyConfigSchema
      );
      const context = mustValidate(req.body.answers, createSurveySchema);

      const domain = req.header('origin');
      const returnUrlClean = UrlMapper.relativeUrl(returnUrl, domain);

      const setup = await materialityAssessmentManager.create({
        initiativeId: req.initiative._id,
        user: req.user,
        promoCode: referralCode,
        context,
        metadata: {
          initiativeId: req.initiative._id.toHexString(),
          effectiveDate: effectiveDate.toISOString(),
          userId: req.user._id.toHexString(),
          assessmentType,
        },
        returnUrl: returnUrlClean,
      });

      res.FromModel(setup);
    } catch (e) {
      next(e);
    }
  });

  router
  .route('/initiatives/:initiativeId/create/:sessionId')
  .get(populateInitiative, async (req, res, next) => {
    try {
      if (!req.initiative?._id) {
        return next(new PermissionDeniedError());
      }

      const results = await materialityAssessmentManager.getSurveyIdBySessionId({
        initiativeId: req.initiative._id,
        sessionId: req.params.sessionId,
      });

      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/scores/:scoreJobId/questions')
  .get(canAccessInsights, isScoresCalculated, async (req, res, next) => {
    try {
      const { surveyId } = req.params;
      const result = res.locals.scoreJob.tasks[0].data.result as AssessmentResult<AssessmentData>;
      const materialityAssessmentService = new MaterialityAssessmentService(new ObjectId(surveyId));
      const utrs = await materialityAssessmentService.getAssessmentMappedUtrs(result);
      res.FromModel(utrs);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/scores')
  .get(canAccessInsights, canGenerateScore, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;

      const bjService = new MaterialityAssessmentBackgroundJobService(backgroundJobService);
      const job = await bjService.createOrReturnJob({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
        userId: req.user._id,
      });

      let result = job.tasks[0].data.result;
      if (result) {
        const materialityAssessmentService = new MaterialityAssessmentService(new ObjectId(surveyId));
        result = await materialityAssessmentService.hydrateTopics(result);
      }

      res.FromModel({
        jobId: job._id.toString(),
        status: job.status,
        result: result,
        config: job.tasks[0].data.config,
        updatedAt: job.updated,
      });
    } catch (e) {
      next(e);
    }
  })
  .post(canAccessInsights, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const { modelType = AIModelType.Claude } = req.params;

      const bjService = new MaterialityAssessmentBackgroundJobService(backgroundJobService);
      const job = await bjService.createJob({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
        modelType: modelType as AIModelType,
        userId: req.user._id,
      });

      res.FromModel({
        jobId: job._id.toString(),
        status: job.status,
        result: job.tasks[0].data.result,
        updatedAt: job.updated,
      });
    } catch (e) {
      next(e);
    }
  });

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/config')
  .put(canManageInitiative, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const config = mustValidate(
        req.body,
        z.object({
          orderedTopics: z
            .object({
              code: z.string(),
              disabled: z.boolean().optional(),
            })
            .array(),
          explanation: z.string(),
        })
      );

      const result = await materialityAssessmentConfigService.update({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
        config,
      });

      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  })
  .delete(canManageInitiative, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const result = await materialityAssessmentConfigService.update({
        initiativeId: new ObjectId(initiativeId),
        surveyId: new ObjectId(surveyId),
      });
      res.FromModel({ result });
    } catch (e) {
      next(e);
    }
  });

router.route('/initiatives/:initiativeId/surveys/:surveyId/size-scope').get(canAccessInsights, async (req, res, next) => {
  try {
    const { surveyId } = req.params;
    const sizeScope = await materialityAssessmentManager.getSizeScopeByAssessmentId(surveyId);
    res.FromModel({ sizeScope });
  } catch (e) {
    next(e);
  }
});

router
  .route('/initiatives/:initiativeId/surveys/:surveyId/scores/:scoreJobId/report/pptx')
  .get(canAccessInsights, isMaterialitySurvey, isScoresCalculated, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const survey = res.locals.survey as SurveyModelPlain;
      const assessmentType: AssessmentType = survey.assessmentType ?? AssessmentType.FinancialMateriality;
      const existingJob = res.locals.scoreJob as SupportedJobPlain;
      const materialityAssessmentService = new MaterialityAssessmentService(new ObjectId(surveyId));
      const orderedFinancialData =
        materialityAssessmentService.getOrderedFinancialAssessmentData(existingJob?.tasks[0].data) ?? [];

      if (assessmentType === AssessmentType.FinancialMateriality) {
        const job = await pptxService.createOrReturnJob({
          templateName: PPTXTemplateName.MT,
          templateScheme: MTPPTXTemplateScheme.FinancialReport,
          type: TaskType.GenerateMaterialityTrackerFinancialReport,
          userId: req.user._id,
          initiativeId: new ObjectId(initiativeId),
          surveyId: new ObjectId(surveyId),
          financialAssessmentDataMin: orderedFinancialData,
          scoreJobId: existingJob._id,
          metadata: {
            effectiveDate: survey.effectiveDate,
          },
        });
        return res.FromModel({
          jobId: job._id.toString(),
          taskId: job.tasks[0].id,
          status: job.status,
          completedDate: job.completedDate,
        });
      }

      if (assessmentType === AssessmentType.DoubleMateriality) {
        const hasCustomOrder = !!existingJob.tasks[0].data.config?.orderedTopics.length;
        const assessmentDataMin = existingJob.tasks[0].data.result ? {
          [AssessmentResultType.Financial]: orderedFinancialData,
          [AssessmentResultType.Impact]: existingJob.tasks[0].data.result[AssessmentResultType.Impact],
        } : {
          [AssessmentResultType.Financial]: [],
          [AssessmentResultType.Impact]: [],
        };
        const job = await pptxService.createOrReturnJob({
          templateName: PPTXTemplateName.MT,
          templateScheme: MTPPTXTemplateScheme.DoubleMateriality,
          type: TaskType.GenerateMaterialityTrackerDoubleMaterialityReport,
          userId: req.user._id,
          initiativeId: new ObjectId(initiativeId),
          surveyId: new ObjectId(surveyId),
          assessmentDataMin,
          scoreJobId: existingJob._id,
          metadata: {
            effectiveDate: survey.effectiveDate,
            hasCustomOrder,
          },
        });
        return res.FromModel({
          jobId: job._id.toString(),
          taskId: job.tasks[0].id,
          status: job.status,
          completedDate: job.completedDate,
        });
      }

      next(new BadRequestError('Unsupported assessment type', {
        assessmentType,
        surveyId,
      }));
    } catch (error) {
      next(error);
    }
  })
  .post(canAccessInsights, isMaterialitySurvey, isScoresCalculated, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const survey = res.locals.survey as SurveyModelPlain;
      const assessmentType: AssessmentType = survey.assessmentType ?? AssessmentType.FinancialMateriality;
      const existingJob = res.locals.scoreJob as SupportedJobPlain;
      const materialityAssessmentService = new MaterialityAssessmentService(new ObjectId(surveyId));
      const orderedFinancialData =
        materialityAssessmentService.getOrderedFinancialAssessmentData(existingJob.tasks[0].data) ?? [];

      if (assessmentType === AssessmentType.FinancialMateriality) {
        const job = await pptxService.createJob({
          templateName: PPTXTemplateName.MT,
          templateScheme: MTPPTXTemplateScheme.FinancialReport,
          type: TaskType.GenerateMaterialityTrackerFinancialReport,
          userId: req.user._id,
          initiativeId: new ObjectId(initiativeId),
          surveyId: new ObjectId(surveyId),
          financialAssessmentDataMin: orderedFinancialData,
          scoreJobId: existingJob._id,
          metadata: {
            effectiveDate: survey.effectiveDate,
          },
        });
        return res.FromModel({
          jobId: job._id.toString(),
          taskId: job.tasks[0].id,
          status: job.status,
        });
      }

      if (assessmentType === AssessmentType.DoubleMateriality) {
        const hasCustomOrder = !!existingJob.tasks[0].data.config?.orderedTopics.length;
        const assessmentDataMin = existingJob.tasks[0].data.result ? {
          [AssessmentResultType.Financial]: orderedFinancialData,
          [AssessmentResultType.Impact]: existingJob.tasks[0].data.result[AssessmentResultType.Impact],
        } : {
          [AssessmentResultType.Financial]: [],
          [AssessmentResultType.Impact]: [],
        };
        const job = await pptxService.createJob({
          templateName: PPTXTemplateName.MT,
          templateScheme: MTPPTXTemplateScheme.DoubleMateriality,
          type: TaskType.GenerateMaterialityTrackerDoubleMaterialityReport,
          userId: req.user._id,
          initiativeId: new ObjectId(initiativeId),
          surveyId: new ObjectId(surveyId),
          assessmentDataMin,
          scoreJobId: existingJob._id,
          metadata: {
            effectiveDate: survey.effectiveDate,
            hasCustomOrder,
          },
        });
        return res.FromModel({
          jobId: job._id.toString(),
          taskId: job.tasks[0].id,
          status: job.status,
        });
      }

      next(new BadRequestError('Unsupported assessment type', {
        assessmentType,
        surveyId,
      }));
    } catch (error) {
      next(error);
    }
  });

['xlsx', 'csv'].forEach((type) => {
router
  .route(`/initiatives/:initiativeId/surveys/:surveyId/metric-answers-report/${type}`)
  .get(async (req, res, next) => {
    try {
      const { surveyId } = req.params;
      const { workbook, filename } = await materialityAssessmentReportService.downloadAssessmentAnswers({ surveyId });
      if (type === 'csv') {
        setCsvFileHeaders(res, `${filename}.${type}`);
      } else {
        setXlsxFileHeaders(res, `${filename}.${type}`);
      }
      return res.send(write(workbook, { type: 'buffer', bookType: type as FileParserType, cellStyles: true }));
    } catch (error) {
      next(error);
    }
  });
})


module.exports = router;
