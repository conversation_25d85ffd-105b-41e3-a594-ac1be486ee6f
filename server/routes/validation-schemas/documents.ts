import { DocumentSubType } from '../../models/document';
import { z } from 'zod';
import { getObjectIdsSchema } from './common';

export const getDocumentsSchema = z.object({
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  cursor: z.coerce.date().optional(),
  limit: z.coerce.number().min(1).max(100).optional(),
});

export const uploadDocumentSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  ownerSubType: z.nativeEnum(DocumentSubType).optional(),
});

export const bulkUploadDocumentSchema = uploadDocumentSchema.merge(
  z.object({
    documentIds: getObjectIdsSchema({ min: 1, isOptional: false }),
  })
);
