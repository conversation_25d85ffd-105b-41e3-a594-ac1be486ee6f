import {
  ChartSubType,
  InsightDashboardItemType,
  PrivacyFilter,
  SurveyFilter,
  TimeFrameType,
  UtrvFilter,
  TextSubType,
} from '../../models/insightDashboard';
import { z } from 'zod';
import { BASED_SURVEY_TYPES, SurveyType } from '../../models/survey';
import { DataPeriods } from '../../service/utr/constants';
import { objectIdsSchema } from './common';

const commonDashboardFilterObject = {
  survey: z.nativeEnum(SurveyFilter).optional(),
  utrv: z.enum([UtrvFilter.AllAnswered, UtrvFilter.Verified, UtrvFilter.Assured] as const).optional(),
  privacy: z.nativeEnum(PrivacyFilter).optional(),
  timeFrame: z
    .object({
      type: z.nativeEnum(TimeFrameType),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    })
    .optional(),
  surveyType: z.enum(BASED_SURVEY_TYPES).optional(),
  period: z.nativeEnum(DataPeriods).optional(),
  baselinesTargets: z
    .object({
      enabled: z.boolean(),
    })
    .optional(),
  initiativeInfo: z
    .object({
      enabled: z.boolean(),
    })
    .optional(),
  sdgContribution: z
    .object({
      enabled: z.boolean(),
    })
    .optional(),
};

const dashboardFilterObject = z
  .object({
    ...commonDashboardFilterObject,
    shareWithSubsidiaries: z
      .object({
        enabled: z.boolean(),
      })
      .optional(),
    initiativeIds: objectIdsSchema,
  })
  .passthrough();

const itemsSchema = z
  .object({
    gridSize: z.object({
      x: z.number(),
      y: z.number(),
      w: z.number(),
      h: z.number(),
      minW: z.number().optional(),
      maxW: z.number().optional(),
      minH: z.number().optional(),
      maxH: z.number().optional(),
    }),
    _id: z.string(),
    title: z.string().optional(),
    type: z.nativeEnum(InsightDashboardItemType).optional(),
    subType: z.union([z.nativeEnum(ChartSubType), z.nativeEnum(TextSubType)]).optional(),
    unitText: z.string().optional(),
    icon: z.string().optional(),
  })
  .passthrough()
  .array()
  .optional();

export const createInsightDashboardDtoSchema = z.object({
  title: z.string(),
  filters: dashboardFilterObject,
  items: itemsSchema,
});

export const updateInsightDashboardDtoSchema = z
  .object({
    title: z.string(),
    filters: dashboardFilterObject,
    items: itemsSchema,
    deletedDocumentIds: z.string().array().optional(),
  })
  .passthrough();

export const createInsightDashboardItemDtoSchema = z
  .object({
    gridSize: z
      .object({
        x: z.number(),
        y: z.number(),
        w: z.number(),
        h: z.number(),
        minW: z.number(),
        maxW: z.number(),
        minH: z.number(),
        maxH: z.number(),
      })
      .partial(),
    title: z.string().optional(),
    type: z.nativeEnum(InsightDashboardItemType).optional(),
    subType: z.nativeEnum(ChartSubType).optional(),
    unitText: z.string().optional(),
    icon: z.string().optional(),
  })
  .passthrough();

export const getAvailablePeriodsParamsSchema = z.object({
  initiativeId: z.string(),
  surveyType: z.nativeEnum(SurveyType).default(SurveyType.Default),
});

const portfolioDashboardFilterObject = z
  .object({
    ...commonDashboardFilterObject,
    displayAsDefault: z
      .object({
        enabled: z.boolean(),
      })
      .optional(),
  })
  .passthrough();

export const createPortfolioInsightDashboardDtoSchema = z.object({
  title: z.string(),
  filters: portfolioDashboardFilterObject,
  items: itemsSchema,
});

export const updatePortfolioInsightDashboardDtoSchema = z
  .object({
    title: z.string(),
    filters: portfolioDashboardFilterObject,
    items: itemsSchema,
    deletedDocumentIds: z.string().array().optional(),
  })
  .passthrough();
