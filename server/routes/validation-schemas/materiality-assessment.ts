import { z } from 'zod';
import { AssessmentType } from '../../types/materiality-assessment';

export enum MaterialityAssessmentUtrCodes {
  OperationTime = 'materiality-2024/operation-time',
  NumStaff = 'materiality-2024/num-staff',
  AnnualSales = 'materiality-2024/annual-sales',
  CapitalEmployed = 'materiality-2024/capital-employed',
  Sector = 'materiality-2024/sector',
}

// This also determines the order of the utrs displayed in assessment modal
export const materialityContextUtrCodes = [
  MaterialityAssessmentUtrCodes.OperationTime,
  MaterialityAssessmentUtrCodes.Sector,
  MaterialityAssessmentUtrCodes.NumStaff,
  MaterialityAssessmentUtrCodes.AnnualSales,
  MaterialityAssessmentUtrCodes.CapitalEmployed,
];

export const createSurveySchema = z.object({
  [MaterialityAssessmentUtrCodes.OperationTime]: z.string(),
  [MaterialityAssessmentUtrCodes.NumStaff]: z.string(),
  [MaterialityAssessmentUtrCodes.AnnualSales]: z.string(),
  [MaterialityAssessmentUtrCodes.CapitalEmployed]: z.string(),
  [MaterialityAssessmentUtrCodes.Sector]: z.string(),
});

export const createSurveyConfigSchema = z.object({
  effectiveDate: z.coerce.date(),
  referralCode: z.string().optional(),
  returnUrl: z.string(),
  assessmentType: z.nativeEnum(AssessmentType),
});

export const matOnboardingSchema = z.object({
  firstName: z.string(),
  surname: z.string(),
  email: z.string(),
  telephoneNumber: z.string().optional(),
  password: z.string(),
  rPassword: z.string(),
  referenceCompany: z.string().optional(),
  referralCode: z.string().optional(),
});

export type CreateSurveyDto = z.infer<typeof createSurveySchema>;
