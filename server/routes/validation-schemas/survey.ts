import { z } from 'zod';
import { getObjectIdsSchema, refineIdSchema } from './common';
import { VisibilityStatus } from '../../service/survey/scope/visibilityStatus';
import { UtrvAssuranceStatus } from '../../models/universalTrackerValue';
import { ActionList } from '../../service/utr/constants';
import { FileParserType } from '../../service/survey/transfer/parserTypes';

export const surveyScopeSchema = z.object({
  sdg: z.string().array(),
  frameworks: z.string().array(),
  materiality: z.string().array(),
  standards: z.string().array(),
  custom: getObjectIdsSchema()
});

export const downloadDelegatedSurveysSchema = z.object({
  initiativeId: refineIdSchema('initiativeId'),
  type: z.enum([FileParserType.Csv, FileParserType.Xlsx]),
  userIds: getObjectIdsSchema({ min: 1, isOptional: false }),
  surveyIds: getObjectIdsSchema({ min: 1, isOptional: false }),
  downloadScope: z.object({
    statuses: z.nativeEnum(ActionList).array(),
    assuranceStatus: z.nativeEnum(UtrvAssuranceStatus).array().optional(),
    visibility: z.nativeEnum(VisibilityStatus),
    displayMetricOverrides: z.coerce.boolean(),
    displayUserInput: z.coerce.boolean(),
    displayTag: z.coerce.boolean(),
  })
});
