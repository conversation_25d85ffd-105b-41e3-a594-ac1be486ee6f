/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { wwgLogger } from '../service/wwgLogger';
import { saveProfile } from '../service/file/profile';
import FileUpload from '../http/FileUpload';
import { createUserManager } from '../service/user/UserManager';
import { AuthRouter } from '../http/AuthRouter';
import { getMagicBell } from '../service/notification/delivery/MagicBell';
import { getNotificationService } from '../service/notification/NotificationService';
import { NotificationPreferencesUpdateRequest } from '../service/notification/NotificationModels';
import { OrganizationRepository } from "../repository/OrganizationRepository";
import { CompleteUser } from '../models/user';
import { InitiativePlain } from '../models/initiative';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import UserError from '../error/UserError';
import { getEmailService } from "../service/email/EmailService";
import config from "../config";
import { RedirectModalConfig, TriggeredUserModals } from '../service/user/TriggeredUserModals';
import { getNotificationPreferencesService } from '../service/notification/NotificationPreferencesService';
import { mustValidate } from '../util/validation';
import { z } from 'zod';
import { TimePeriod } from '../util/date';
import { OrganizationPartnerTypes } from '../models/organization';
import UserPreferences from "../models/userPreferences";
import { getUserAppPermissionService } from '../service/user/UserAppPermissionService';

const router = express.Router() as AuthRouter;
const userManager = createUserManager();
const magicBell = getMagicBell();
const notificationService = getNotificationService();
const notificationPreferencesService = getNotificationPreferencesService();
const emailService = getEmailService()

interface CurrentUser extends CompleteUser {
  initiativeTree: Pick<InitiativePlain, '_id' | 'parentId'>[];
  redirectConfig?: RedirectModalConfig;
}

router
  .route('/')
  .get(async (req, res) => {
    try {
      const initiativeTree: Pick<InitiativePlain, '_id' | 'parentId' | 'permissionGroup'>[] = await InitiativeRepository.getInitiativeTree(req.user, { _id: 1, parentId: 1, permissionGroup: 1 });
      const currentUser: CurrentUser = {
        ...req.user.getComplete(),
        initiativeTree,
      };

      const frontendModal = await TriggeredUserModals.GetRedirectRegionModalConfig(
        req.ip,
        currentUser,
        req.header('origin')
      );

      if (frontendModal) {
        currentUser.redirectConfig = frontendModal;
      }

      return res.FromModel(currentUser);
    } catch (e) {
      return res.Exception(new UserError('Could not get current user details.', { cause: e }));
    }
  })
  .patch(FileUpload.single('profile'), async (req, res, next) => {
    const user = req.user;
    await userManager.updateProfile(user, req.body);
    if (req.file) {
      await saveProfile(user._id, 'user', [req.file]).catch(wwgLogger.error);
    }
    res.FromModel(user);
  });

router.route('/interest').post((req, res, next) => {
  const { title = '', message, company = '' } = req.body;

  const user = req.user;
  const html = `
     <div>
  <p>New user message</p>
  <table class="container600 table-striped" border="0" width="540" cellpadding="0" cellspacing="0">
    <tbody>
        <tr>
      <td>id</td>
      <td>${user._id}</td>
    </tr>
    <tr>
      <td>First name</td>
      <td>${user.firstName}</td>
    </tr>
    <tr>
      <td>Last name</td>
      <td>${user.surname}</td>
    </tr>
    <tr>
      <td>Email</td>
      <td>${user.email}</td>
    </tr>
      <tr>
      <td>Company</td>
      <td>${company}</td>
    </tr>
     <tr>
      <td>Subject</td>
      <td>${title}</td>
    </tr>
     <tr>
      <td>Message</td>
      <td>${message}</td>
    </tr>
    </tbody>
    </table>
  </div>
  `;
  if (!message) {
    return next(new UserError(`Please provide message`, { userId: user._id }));
  }

  const msg = emailService.getNewMessageInstance();
  msg.addTo(config.email.supportEmail, 'Support').setSubject(title.slice(0, 100)).setHtml(html);

  emailService
    .send(msg)
    .then(() => res.Success('Success'))
    .catch((e) => next(e));
});

router.route('/magic-bell/auth-token').get((req, res) => {
  res.FromModel(magicBell.getAuthToken(req.user._id));
});

router.route('/preferences').get(async (req, res, next) => {
  const preferences = await UserPreferences.findOne({ userId: req.user._id }).lean().exec();
  res.FromModel(preferences ?? { userId: req.user._id });
});

router
  .route('/notifications/preferences')
  .get(async (req, res) => {
    const userOrgId = req.user.organizationId;
    const organization = userOrgId
      ? await OrganizationRepository.findOne({ _id: userOrgId })
      : await OrganizationRepository.findOne({
          partnerTypes: OrganizationPartnerTypes.Assurer,
          'permissions.userId': req.user._id,
        });
    res.FromModel(await notificationService.getPreferences(req.user, organization));
  })
  .put(async (req, res) => {
    await notificationService.setPreferences(req.user._id, req.body as NotificationPreferencesUpdateRequest);
    res.Success();
  });

router.route('/notifications/preferences/summaries').put(async (req, res) => {
  const data = mustValidate(
    req.body,
    z.object({
      period: z.nativeEnum(TimePeriod),
      isSummary: z.boolean(),
    })
  );

  await notificationPreferencesService.setSummaries(req.user._id, data);
  res.Success();
});

router.route('/apps/available').get(async (req, res) => {
  const result = await getUserAppPermissionService().getAvailableApps(req.user);
  res.FromModel(result);
});

module.exports = router;
