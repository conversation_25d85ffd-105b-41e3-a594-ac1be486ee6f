/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerValue from '../../models/universalTrackerValue';
import { ActionList } from '../../service/utr/constants';
import UniversalTrackerActionManager from "../../service/utr/UniversalTrackerActionManager"
import { AuthRouter } from '../../http/AuthRouter';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';

const router = express.Router() as AuthRouter;

router.route('/')
  .get(function (req, res) {
    UniversalTrackerValue.find()
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:id')
  .put((req, res) => {
    if (!req.body._id || req.params.id !== req.body._id) {
      return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
    }
    UniversalTrackerValueRepository.mustFindById(req.params.id)
      .then((obj) => {
        obj.set(req.body);
        return obj.save();
      })
      .then(() => res.Success('Successfully updated UniversalTrackerValue with _id=' + req.params.id))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/initiativeId/:initiativeId')
  .get(function (req, res) {
    UniversalTrackerValue.find({ initiativeId: req.params.initiativeId }, '-history -disaggregations')
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/universalTrackerId/:universalTrackerId')
  .get(function (req, res) {
    UniversalTrackerValue.find({ universalTrackerId: req.params.universalTrackerId }, '-history -disaggregations')
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/universalTrackerId/:universalTrackerId/initiativeId/:initiativeId')
  .get(function (req, res) {
    UniversalTrackerValue.find({ universalTrackerId: req.params.universalTrackerId, initiativeId: req.params.initiativeId },
      '-history -disaggregations')
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/importAndReplace')
  .post(ContextMiddleware, function (req, res) {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on importAndReplace');
    }

    UniversalTrackerValue.find(
      {
        universalTrackerId: req.body.universalTrackerId, initiativeId: req.body.initiativeId,
        type: req.body.type, status: { $in: [ActionList.Created, ActionList.Updated, ActionList.Verified] }, effectiveDate: req.body.effectiveDate
      },
      '-history -disaggregations')
      .sort({ created: 'asc' })
      .exec()
      .then(async (models) => {

        const requestValue = isNaN(req.body.value) || req.body.value==='' ? undefined : Number(req.body.value);
        req.body.value = undefined;

        delete req.body.status; // Not allowed, and will fall back to model default
        const user = req.user;
        if (models.length === 0) {
          const model = new UniversalTrackerValue(req.body);
          return model.save()
            .then(async (obj) => {
              try {
                await UniversalTrackerActionManager.setValueById(obj._id, user, requestValue);
                if (obj.verificationRequired) {
                  await UniversalTrackerActionManager.verifyId(obj._id, user); // Auto verify only with importer
                }
                return res.FromModel('Created');
              } catch (e) {
                console.log(e);
                return res.Invalid('Created document successfully, but could not update value. Please do it manually');
              }
            })
            .catch((err: Error) => {
              return res.Exception(err);
            });
        }

        let updated = false;
        const latestModel = models.pop();
        if (latestModel && requestValue !== latestModel.value) {
          updated = true;
          try {
            await UniversalTrackerActionManager.setValueById(latestModel._id, user, requestValue);
            if (latestModel.verificationRequired) {
              await UniversalTrackerActionManager.verifyId(latestModel._id, user);
            }
          } catch (e) {
            console.log(e);
            return res.Invalid('Could not update value');
          }
        } else if (latestModel && latestModel.status !== ActionList.Verified) {
          updated = true;
          try {
            await UniversalTrackerActionManager.verifyId(latestModel._id, user);
          } catch (e) {
            console.log(e);
            return res.Invalid('Could not update value');
          }
        }

        for (const model of models) {
          try {
            await UniversalTrackerActionManager.rejectId(model._id, user);
          } catch (e) {
            console.log(e);
            return res.Invalid('Could not update value');
          }
        }
        return res.FromModel(updated ? 'Updated' : 'Skipped');
      })
      .catch((e: Error) => res.Exception(e));
  });

router.route('/source/:sourceType/:surveyCode')
  .get(function (req, res) {
    UniversalTrackerValue.find({ sourceType: req.params.sourceType, sourceCode: req.params.surveyCode },
      { created: 1, status: 1, type: 1, value: 1, initiativeId: 1, universalTrackerId: 1, disaggregationSummary: 1 })
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' })
      .lean()
      .exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/rejectAll/universalTrackerId/:universalTrackerId/sourceCode/:sourceCode')
  .patch(async function (req, res) {
    const utrValues = await UniversalTrackerValue.find({
        universalTrackerId: req.params.universalTrackerId,
        sourceCode: req.params.sourceCode,
        status: { $ne: ActionList.Rejected },
      }).lean().exec();

    const promises: Promise<any>[] = [];
    const user = req.user;
    utrValues.forEach((utrValue) => promises.push(
      UniversalTrackerActionManager.rejectId(utrValue._id, user).catch(console.log)
    )
    );

    const result = await Promise.all(promises);
    return res.Success('Rejected ' + result.length + ' values');
  });

router.route('/verifyAll/universalTrackerId/:universalTrackerId/sourceCode/:sourceCode')
  .patch(async function (req, res) {
    const utrValues = await UniversalTrackerValue.find(
      { universalTrackerId: req.params.universalTrackerId, sourceCode: req.params.sourceCode, status: ActionList.Updated })
      .lean().exec();

    const promises: Promise<any>[] = [];
    const user = req.user;
    utrValues.forEach((utrValue) => promises.push(
      UniversalTrackerActionManager.verifyId(utrValue._id, user).catch(console.log)
    ));

    const result = await Promise.all(promises);
    return res.Success('Verified ' + result.length + ' values');
  });

module.exports = router;
