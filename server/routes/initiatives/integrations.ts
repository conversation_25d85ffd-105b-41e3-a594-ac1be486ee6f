/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import express from "express";
import { ObjectId } from "bson";
import { AuthRouter } from "../../http/AuthRouter";
import { getIntegrationManager } from "../../service/integration/IntegrationManager";
import { canManageInitiative } from "../../middleware/initiativeMiddlewares";
import Initiative from "../../models/initiative";
import { toArray } from "../../http/query";
import { Answers } from "../../service/integration/IntegrationProvider";
import UserError from "../../error/UserError";

const router = express.Router({ mergeParams: true }) as AuthRouter;
const integrationManager = getIntegrationManager();

router.use(canManageInitiative)

router.route('/').get((req, res, next) => {
  Promise.all([
    integrationManager.allProviders(),
    integrationManager.getUsedProviders(new ObjectId(req.params.initiativeId)),
  ])
    .then(([allProviders, usedProviders]) => {
      res.FromModel({ allProviders, usedProviders });
    })
    .catch(next);
});

router.route('/:code')
  .get((req, res, next) => {
    integrationManager.getIntegration({
      initiativeId: new ObjectId(req.params.initiativeId),
      integrationCode: req.params.code
    }).then((provider) => res.FromModel(provider))
      .catch(next)
  })
  .post(async (req, res, next) => {
    try {
      const initiative = await Initiative.findById(req.params.initiativeId).orFail().exec();

      const currentSetup = await integrationManager.getIntegration({
        initiativeId: new ObjectId(req.params.initiativeId),
        integrationCode: req.params.code
      });

      if (currentSetup.status !== 'setup_required') {
        next(new UserError(`Integration already created`, {
          integrationStatus: currentSetup.status,
          initiativeId: new ObjectId(req.params.initiativeId),
          integrationCode: req.params.code,
          status: 400,
        }));
        return;
      }

      const result = await integrationManager.createIntegration({
        rootInitiative: initiative,
        providerCode: req.params.code,
        user: req.user,
        generatedAnswers: toArray<Answers>(req.body.generatedAnswers),
        address: req.body.address,
      });

      res.FromModel(result);
    } catch (error) {
      next(error);
    }
  });

router.route('/:code/used').get((req, res, next) => {
  integrationManager
    .getUsedIntegration({
      initiativeId: new ObjectId(req.params.initiativeId),
      integrationCode: req.params.code,
    })
    .then((provider) => res.FromModel(provider))
    .catch(next);
});

module.exports = router;
