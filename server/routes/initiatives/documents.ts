/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { DocumentOwnerType } from '../../models/document';
import { getDocumentService } from '../../service/file/DocumentService';
import FileUpload from '../../http/FileUpload';
import UserError from '../../error/UserError';
import { AuthRouter } from '../../http/AuthRouter';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { mustValidate } from '../../util/validation';
import { uploadDocumentSchema, bulkUploadDocumentSchema, getDocumentsSchema } from '../validation-schemas/documents';
import { canAccessInitiative, canManageInitiative, isInitiativeExists } from '../../middleware/initiativeMiddlewares';
import { getResultsFromPromiseAllSettled } from '../../util/promise';
import { wwgLogger } from '../../service/wwgLogger';

const router = express.Router({ mergeParams: true }) as AuthRouter;
router.use(ContextMiddleware);

const documentService = getDocumentService();

router
  .route('/')
  .get(canAccessInitiative, async (req, res) => {
    try {
      const initiativeId = req.params.initiativeId;
      const { startDate, endDate, cursor, limit } = mustValidate(req.query, getDocumentsSchema);
      const documents = await documentService.handleGetDocumentsWithUrl(initiativeId, {
        startDate,
        endDate,
        cursor,
        limit,
      });
      return res.FromModel(documents);
    } catch (e) {
      return res.Exception(e);
    }
  })
  .post(isInitiativeExists, canManageInitiative, FileUpload.any(), async (req, res) => {
    try {
      const data = mustValidate(req.body, uploadDocumentSchema);
      const initiative = res.locals.initiative;
      const files: any = req.files;
      if (!Array.isArray(files) || files.length === 0) {
        throw new UserError('There are no file to be uploaded');
      }
      const result = await Promise.allSettled(
        files.map((file) =>
          documentService.handleDocumentUpload(
            { ...data, ownerId: initiative._id, ownerType: DocumentOwnerType.Initiative },
            file,
            req.user
          )
        )
      );

      const { fulfilled, rejected } = getResultsFromPromiseAllSettled(result);
      if (rejected.length > 0) {
        wwgLogger.error(`Cannot upload documents for initiative ${initiative._id}`, rejected);
      }

      return res.FromModel({ fulfilled, rejected });
    } catch (e) {
      return res.Exception(e);
    }
  })
  .patch(isInitiativeExists, canManageInitiative, async (req, res) => {
    try {
      const data = mustValidate(req.body, bulkUploadDocumentSchema);
      const documentIds = data.documentIds;
      const result = await documentService.handleBulkDocumentEdit(documentIds, {
        ...data,
        ownerType: DocumentOwnerType.Initiative,
      });

      return res.FromModel(result);
    } catch (e) {
      return res.Exception(e);
    }
  });

router
  .route('/:documentId')
  .patch(isInitiativeExists, canManageInitiative, async (req, res) => {
    try {
      const data = mustValidate(req.body, uploadDocumentSchema);
      const documentId = req.params.documentId;
      const result = await documentService.handleDocumentEdit(documentId, {
        ...data,
        ownerType: DocumentOwnerType.Initiative,
      });

      return res.FromModel(result);
    } catch (e) {
      return res.Exception(e);
    }
  })
  .delete(isInitiativeExists, canManageInitiative, async (req, res) => {
    try {
      const documentId = req.params.documentId;
      const initiative = res.locals.initiative; // Parsed by middleware
      const result = await documentService.handleDeleteDocument({ documentId, initiative });

      return res.FromModel(result);
    } catch (e) {
      return res.Exception(e);
    }
  });

module.exports = router;
