/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import Initiative, { InitiativeWithCustomer } from '../../models/initiative';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import UserError from '../../error/UserError';
import deleteConfirm from '../../middleware/deleteConfirm';
import MetricGroup from '../../models/metricGroup';
import FileUpload from '../../http/FileUpload';
import { saveProfile } from '../../service/file/profile';
import { InitiativeManager } from '../../service/initiative/InitiativeManager';
import UniversalTracker, { UniversalTrackerPlain, UtrType } from '../../models/universalTracker';
import { wwgLogger } from '../../service/wwgLogger';
import { clearCache } from '../../service/cache';
import { getOnboardingManager } from '../../service/onboarding/OnboardingManager';
import { ObjectId } from 'bson';
import { AuthenticatedRequest, AuthRouter } from '../../http/AuthRouter';
import { UserInitiativeRepository } from '../../repository/UserInitiativeRepository';
import CalculationUniversalTrackerService from '../../service/utr/CalculationUniversalTrackerService';
import { getAuditLogger } from '../../service/audit/AuditLogger';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import asyncHandler from 'express-async-handler';
import { InitiativeAudit } from '../../service/audit/events/Initiative';
import { addToLinkedUtr } from "../../service/initiative/linkedUniversalTrackers";
import { getLinkedUtrManager } from "../../service/initiative/LinkedUtrManager";
import { isValidSubscriptionCode } from '../../service/payment/subscriptionCodes';
import { checkIsStaff } from '../../middleware/userMiddlewares';
import ContextError from "../../error/ContextError";
import { SurveyTemplateRepository } from '../../repository/SurveyTemplateRepository';
import { setXlsxFileHeaders } from '../../http/FileDownload';
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import * as XLSX from '@sheet/core';
import { SurveyConfigService } from '../../service/initiative/SurveyConfigService';
import { LEVEL } from '../../service/event/Events';
import { getCustomerManager } from "../../service/payment/CustomerManager";
import { CustomMetricUpdate, getCustomMetricManager } from '../../service/custom-metrics/CustomMetricManager';
import { CustomMetricRepository } from '../../repository/CustomMetricRepository';
import { getSponsorshipService } from "../../service/referral/SponsorshipService";
import UniversalTrackerValue from '../../models/universalTrackerValue';
import { getAgreementService } from "../../service/organization/AgreementService";
import BadRequestError from "../../error/BadRequestError";
import { mustValidate } from '../../util/validation';
import { customMetricUpdateDtoSchema } from '../validation-schemas/custom-metrics';
import { getInitiativeOnboardingRepository } from '../../repository/InitiativeOnboardingRepository';

const router = express.Router() as AuthRouter;

const onboardingManager = getOnboardingManager();
const auditLogger = getAuditLogger();
const customMetricManager = getCustomMetricManager();

router.use(ContextMiddleware);

router.route('/subsidiary').post(function (req, res, next) {
  InitiativeManager.createChildInitiativeWithUsage({
    name: req.body.name,
    parentId: res.locals.initiativeId,
    userId: req.user._id,
  })
    .then((created) => res.FromModel(created))
    .catch(next);
});

router.route('/universal-trackers/copy/')
  .post(checkIsStaff, async (req, res, next) => {
    Initiative.findById(res.locals.initiativeId)
      .orFail(new UserError('There was an error adding this Universal Tracker. Please refresh the page and try again.'))
      .exec()
      .then(async initiative => {
        const lutrManager = getLinkedUtrManager();
        const result = await lutrManager.copyCalculatedLinkedUtrs(initiative, {
          options: req.body.options,
          usage: req.body.usage,
          targets: req.body.targets,
          user: req.user,
        })
        clearCache();
        res.FromModel(result);
      }).catch(e => next(e))
  })

router.route('/universal-trackers/usage/')
  .delete(deleteConfirm, async (req, res) => {
    try {
      const data = req.body;
      const usageId = data.usage;
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return res.Exception(new UserError('There was an error adding this Universal Tracker. Please refresh the page and try again.'));
      }
      if (!initiative.linkedUniversalTrackers || initiative.linkedUniversalTrackers.length === 0) {
        // This must be inheriting UTRs, so need to query recursively and populate here
        const existingInitiative = await UserInitiativeRepository.getUserInitiative(req.user, initiativeId);
        if (existingInitiative) {
          initiative.linkedUniversalTrackers = existingInitiative.linkedUniversalTrackers;
        }
      }

      for (const utrId of data.utrIds) {
        const indexToRemove = initiative.linkedUniversalTrackers.findIndex((el) => el.universalTrackerId.toString() === utrId);
        if (indexToRemove < 0) {
          return res.Exception(new UserError('The selected Universal Tracker could not be removed because it was not assigned.'));
        }
        const utr = initiative.linkedUniversalTrackers[indexToRemove];
        const usageToRemove = utr.usage.indexOf(usageId);
        if (usageToRemove < 0) {
          return res.Exception(new UserError('The selected Universal Tracker could not be removed because it was not assigned.'));
        }
        utr.usage.splice(usageToRemove, 1);
        utr.usage = [...utr.usage];
        if (utr.usage.length === 0) {
          initiative.linkedUniversalTrackers.splice(indexToRemove, 1);
          await CalculationUniversalTrackerService.deprecateUtrById(utr.universalTrackerId, initiative._id);
        }
        initiative.linkedUniversalTrackers = [...initiative.linkedUniversalTrackers];
      }

      await initiative.save();
      clearCache();
      return res.Success('Universal Tracker removed successfully.');
    } catch (e) {
      return res.Exception(e);
    }
  })
  .patch(async (req, res) => {
    try {
      const data = req.body;
      const usageId = data.usage;
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId)
        .orFail(new UserError('There was an error adding this Universal Tracker. Please refresh the page and try again.')).exec();

      if (!initiative.linkedUniversalTrackers || initiative.linkedUniversalTrackers.length === 0) {
        // This must be inheriting UTRs, so need to query recursively and populate here
        const existingInitiative = await UserInitiativeRepository.getUserInitiative(req.user, initiativeId);
        if (existingInitiative) {
          initiative.linkedUniversalTrackers = existingInitiative.linkedUniversalTrackers;
        }
      }

      initiative.linkedUniversalTrackers = addToLinkedUtr({
        linkedUtrs: initiative.linkedUniversalTrackers,
        add: data.utrIds.map((id: string) => ({
          universalTrackerId: new ObjectId(id),
          usage: [usageId]
        }))
      })

      await initiative.save();
      clearCache();
      return res.Success('Universal Tracker added successfully.');
    } catch (e) {
      return res.Exception(e);
    }
  });

router.route('/onboarding')
  .post(async (req, res, next) => {
    try {
      const { onboardingId, action, roles } = req.body;
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const repo = getInitiativeOnboardingRepository();

      const onboarding = await repo.mustFindOne({
        _id: new ObjectId(onboardingId),
        initiativeId: new ObjectId(initiativeId)
      });

      switch (action) {
        case 'accept': {
          if (Array.isArray(roles)) {
            await onboardingManager.updateUserPermissions(req.user, onboarding, { permissions: roles })
          }
          await onboardingManager.startOnboarding(onboarding)
          return res.FromModel({ status: 'started' });
        }
        case 'reject': {
          await onboardingManager.remove(onboarding, req.user)
          return res.FromModel({ status: onboarding.status });
        }
        default:
          return next(new ContextError(`Not supported action "${action}", must be "accept" or "reject"`))
      }
    } catch (e) {
      return res.Exception(e);
    }
  });

router.route('/materiality')
  .patch(async (req, res) => {
    try {
      const materialityMap = req.body;
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return res.Exception(new UserError('There was an error updating this Initiative. Please refresh the page and try again.'));
      }
      initiative.materialityMap = materialityMap;

      await initiative.save();
      clearCache();
      return res.Success(`Initiative materiality updated.`);
    } catch (e) {
      return res.Exception(e);
    }
  });

router.route('/metric')
  .get((req, res, next) => {
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    InitiativeRepository.getInitiativeKpis(initiativeId)
      .then((metrics) => res.FromModel(metrics)).catch(next);
  })
  .post(async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(res.locals.initiativeId);
      const [rootInitiative] = await InitiativeRepository.getRootInitiativesForIds([initiativeId]);
      if (!rootInitiative) {
        return next(new ContextError('Failed to find root initiative', { initiativeId }));
      }

      const utr = await customMetricManager.create({
        rootInitiative,
        data: req.body,
        ownerId: initiativeId
      });
      res.FromModel(utr);
    } catch (e) {
      next(e);
    }
  });


router.route('/metric/:universalTrackerId')
  .get(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const utr = await UniversalTracker.findOne({
        _id: req.params.universalTrackerId,
        type: UtrType.CustomKpi,
        ownerId: initiativeId
      });
      return res.FromModel(utr);
    } catch (e) { next(e); }
  })
  .patch(async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(res.locals.initiativeId); // Parsed by middleware

      const { originalUtr, utr } = await customMetricManager.update({
        utrId: new ObjectId(req.params.universalTrackerId),
        initiativeId,
        updates: mustValidate(req.body, customMetricUpdateDtoSchema) as CustomMetricUpdate,
      });

      auditLogger.fromRequest(req, {
        initiativeId: initiativeId,
        auditEvent: InitiativeAudit.custom_metric_updated,
        targets: [
          auditLogger.utrTarget(utr),
        ],
        message: auditLogger.buildMessage(InitiativeAudit.custom_metric_updated.description, utr.name),
        debugData: {
          utr: auditLogger.debugUtrChange({ before: originalUtr as UniversalTrackerPlain, after: utr })
        },
      }).catch(wwgLogger.error);

      return res.FromModel(utr);
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const utrId = req.params.universalTrackerId;

      if (await CustomMetricRepository.checkIsAnswered(new ObjectId(utrId))) {
        return next(new UserError('Custom metrics that have been added to surveys and answered cannot be deleted'));
      }

      const utr = await UniversalTracker.findOneAndDelete({
        _id: utrId,
        type: UtrType.CustomKpi,
        ownerId: initiativeId,
      }, {});

      if (!utr) {
        return res.Success('Custom metric has already been deleted');
      }

      const { deletedCount: deletedUtrvs } = await UniversalTrackerValue.deleteMany({
        universalTrackerId: new ObjectId(utrId),
      }).exec();

      const userActor = auditLogger.userTarget(req.user);

      const metricGroups = await MetricGroup.find({ universalTrackers: utr._id }).exec();
      metricGroups.forEach(metricGroup => {
        const position = metricGroup.universalTrackers.findIndex(u => String(u) === utrId);
        if (position < 0) {
          return;
        }
        metricGroup.universalTrackers.splice(position, 1);
        metricGroup.updated = new Date();
        metricGroup.save();

        // For same initiative it's user, for others it's the other initiative or PT that triggered it
        const actor = metricGroup.initiativeId.toString() === initiativeId ?
          userActor :
          auditLogger.initiativeTarget({ _id: metricGroup.initiativeId });

        auditLogger.createSystem({
          req: req,
          initiativeId: metricGroup.initiativeId,
          actor,
          auditEvent: InitiativeAudit.metricGroupQuestionRemoved,
          severity: LEVEL.WARNING,
          targets: [
            auditLogger.metricGroupTarget(metricGroup),
            auditLogger.utrTarget(utr),
          ],
          message: auditLogger.buildMessage(InitiativeAudit.metricGroupQuestionRemoved.description, metricGroup.groupName)
        }).catch(wwgLogger.error);
      });

      auditLogger.fromRequest(req, {
        initiativeId: initiativeId,
        auditEvent: InitiativeAudit.custom_metric_deleted,
        severity: LEVEL.WARNING,
        targets: [
          auditLogger.initiativeTarget({ _id: initiativeId }),
          auditLogger.utrTarget(utr),
        ],
        message: auditLogger.buildMessage(InitiativeAudit.custom_metric_deleted.description, utr.name),
        debugData: { utr: auditLogger.utrCompareProps(utr), deletedUtrvs },
      }).catch(wwgLogger.error);

      res.Success();
    } catch (e) {
      next(e);
    }
  });

router.route('/metric/:universalTrackerId/check-is-answered')
  .get(async (req, res, next) => {
    try {
      const isAnswered = await CustomMetricRepository.checkIsAnswered(new ObjectId(req.params.universalTrackerId));
      return res.FromModel({isAnswered});
    } catch (e) {
      next(e);
    }
  })

router.route('/calculated-metric')
  .post(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return res.Exception(new UserError('There was an error creating the metric. Please refresh the page and try again.'));
      }

      if (!['total', 'percentage'].includes(req.body.showAs)) {
        return res.Exception(new UserError(`There was an error creating the metric. Invalid calculation type (showAs: ${req.body.showAs}).`));
      }

      const utrs = await CalculationUniversalTrackerService.createCombinedUtrs(req.body, initiativeId);
      /// Only first UTR is supposed to be displayed, as the other ones are just used for intermediary calculations
      const utr = utrs[0];
      initiative.linkedUniversalTrackers.push({
        universalTrackerId: utr._id,
        usage: ['4']
      });
      await initiative.save();
      clearCache();

      return res.FromModel(utr);
    } catch (e) { next(e); }
  });

router.route('/onboarding/:onboardingId')
  .delete(async (req, res, next) => {
    const { onboardingId } = req.params;
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    onboardingManager.removeOnboardInitiative(onboardingId, initiativeId, req.user)
      .then((resp) => res.Success(resp))
      .catch((e: Error) => next(e));
  });

router.route('/onboarding/:onboardingId/remove-permission').delete(async (req, res, next) => {
  const { onboardingId } = req.params;
  const initiativeId = res.locals.initiativeId; // Parsed by middleware
  onboardingManager
    .removePermissionFromOnboarding(onboardingId, initiativeId, req.user)
    .then((resp) => res.Success(resp))
    .catch((e: Error) => next(e));
});

router.route('/display')
  .patch((req, res) => {
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    return InitiativeManager.updateSettings(initiativeId, req.body)
      .then((initiative) => {
        res.FromModel(initiative.displaySettings)
        clearCache();
      })
      .catch((e: Error) => res.Exception(e));
  })

router.route('/system-log')
  .post(asyncHandler(async (req, res, next) => {
    const defaultStartDate = new Date();
    defaultStartDate.setMonth(defaultStartDate.getMonth() - 3);
    defaultStartDate.setHours(0, 0, 0, 0);
    const { fromDate = defaultStartDate, toDate = new Date(), limit = 100 } = req.body;

    const logs = await auditLogger.findForInitiative(res.locals.initiativeId, {
      fromDate: new Date(fromDate),
      toDate: new Date(toDate),
      limit,
    })
    res.FromModel(logs);
  }));

router.route('/referrals/:productCode')
  .get(async (req, res, next) => {
    Initiative.findById(res.locals.initiativeId, { tags: 1, referrals: 1 })
      .orFail().exec()
      .then(initiative => {
        return res.FromModel(initiative.referrals ?? []);
      })
      .catch((e) => next(e))
  })
  .post(ContextMiddleware, async (req, res, next) => {

    const { productCode } = req.params;
    const { code, referrer } = req.body;

    try {
      if (!isValidSubscriptionCode(productCode)) {
        return next(new UserError(`Product code is not valid`))
      }

      // Must be valid Stripe promo code for any of this to work.
      const customerManager = getCustomerManager();
      const [promotion] = await customerManager.getActivePromotions(code);
      if (!promotion) {
        return next(new UserError(`Referral code "${code}" is not valid`))
      }

      const initiative = await Initiative.findById(res.locals.initiativeId).orFail().exec();
      if (!initiative.customer) {
        throw new ContextError('Initiative does not have a customer', { code, referrer, productCode, initiativeId: res.locals.initiativeId });
      }

      const update = await getSponsorshipService().applyPromoReferral(
        initiative as InitiativeWithCustomer,
        productCode,
        { code, referrer },
        req.user,
      )
      res.FromModel(update.referrals);
    } catch (e) {
      next(e);
    }
  });


router.route('')
  .patch(FileUpload.single('profile'), asyncHandler(async (req, res) => {
    const initiativeId = res.locals.initiativeId; // Parsed by middleware
    const originalInitiative = await Initiative.findById(initiativeId).orFail().exec();

    const obj = await InitiativeManager.update(initiativeId, req.body);

    if (obj.parentId && (originalInitiative.parentId ? obj.parentId.equals(originalInitiative.parentId) : true)) {
      await auditLogger
        .fromRequest(req as AuthenticatedRequest, {
          initiativeId: obj._id,
          auditEvent: InitiativeAudit.parentIdUpdate,
          user: req.user,
          targets: [auditLogger.initiativeTarget(obj)],
          debugData: {
            initiative: {
              before: { parentId: originalInitiative.parentId },
              after: { parentId: obj.parentId },
            },
          },
        })
        .catch(wwgLogger.error);
    } else {
      await auditLogger
        .fromRequest(req as AuthenticatedRequest, {
          initiativeId: obj._id,
          auditEvent: InitiativeAudit.profileUpdate,
          targets: [auditLogger.initiativeTarget(obj)],
        })
        .catch(wwgLogger.error);
    }

    if (req.file) {
      await saveProfile(obj._id, 'initiative', [req.file]).catch(wwgLogger.error);
    }
    res.Success('Successfully updated document with _id=' + initiativeId)
    clearCache(); // If industry changes, it affects materiality, and in turn the scorecard which is cached
  }));

router.route('/survey-templates')
  .get(async (req, res) => {
    try {
      const initiativeId = res.locals.initiativeId;
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return res.Exception('Invalid reporting level');
      }
      // current level and above
      const currentLevelAndAbove = await InitiativeRepository.getCurrentAndParentInitiativeIds(initiative._id);
      const templates = await SurveyTemplateRepository.getTemplatesByCurrentLevelAndAbove(currentLevelAndAbove);
      return res.FromModel(templates);
    } catch (e) {
      res.Exception(e);
    }
  });
router.route('/download').get(async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId;
    const initiative = await Initiative.findById(initiativeId).orFail().exec();

    const initiativeTree = await InitiativeRepository.getMainTreeChildren(initiative._id);
    const { fileName, workBook } = await InitiativeManager.downloadInitiativeSubsidiaries(initiative, initiativeTree);
    setXlsxFileHeaders(res, fileName);

    res.send(XLSX.write(workBook, { type: 'buffer', bookType: FileParserType.Xlsx }));
  } catch (e) {
    next(e);
  }
});

router
  .route('/default-survey-config')
  .get(async (req, res, next) => {
    const initiativeId: string = res.locals.initiativeId;

    try {
      const config = await SurveyConfigService.findByInitiative(initiativeId);
      return res.FromModel(config);
    } catch (e) {
      next(e);
    }
  })
  .post(async (req, res, next) => {
    const initiativeId = res.locals.initiativeId;

    try {
      const result = await SurveyConfigService.update(initiativeId, req.body);
      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

['sponsorship', 'company'].forEach(type => {

router.route(`/agreements/${type}/:agreementCode/accept`)
  .post(async (req, res, next) => {
    try {
      const initiative = await Initiative.findById(res.locals.initiativeId).orFail().exec();
      const agreement = await getAgreementService().acceptAgreement(initiative, {
        userId: req.user._id,
        code: req.params.agreementCode,
        type: type as 'sponsorship' | 'company', // Enforced by rules
      });

      res.FromModel(agreement);
    } catch (err) {
      next(err)
    }
  });
});

/**
 * @TODO: Remove on release/3.32+
 * @deprecated replaced by /agreements/:type(sponsorship|company)/:agreementCode/accept
 */
router.route('/agreements/:agreementCode/accept')
  .post(async (req, res, next) => {
    try {
      const { agreementCode } = req.params;
      if (!getAgreementService().isCompanyAgreementCode(agreementCode)) {
        return next(new BadRequestError('The agreement is not available for organization', {
          agreementCode,
        }));
      }

      const initiative = await Initiative.findById(res.locals.initiativeId).orFail().exec();
      const agreement = await getAgreementService().acceptCompanyAgreement(initiative, {
        userId: req.user._id,
        code: agreementCode,
        type: 'company'
      });

      res.FromModel(agreement);
    } catch (err) {
      next(err)
    }
  });

module.exports = router;
