/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { canManageInitiative } from '../middleware/initiativeMiddlewares';
import { AuthRouter } from '../http/AuthRouter';
import Initiative from '../models/initiative';
import { toCodeFormat } from '../util/string';
import { generateRandomToken } from '../service/crypto/token';
import BadRequestError from '../error/BadRequestError';
import { BankingService, BankingSetting, getBankingService } from '../service/banking/BankingService';
import { BankType } from '../service/banking/constants';
import { ObjectId } from 'bson';

const router = express.Router() as AuthRouter;

router.route('/list').get((req, res) => {
  return res.FromModel(BankingService.getListOfBanks());
});

router.route('/countries').get((req, res) => {
  const countryCodes = req.query.countryCodes;
  return res.FromModel(BankingService.getCountries(countryCodes as string[]));
});

router
  .route('/:initiativeId')
  .post(canManageInitiative, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId;
      const bankName = req.body.bankName;
      const i = await Initiative.findById(initiativeId).orFail().exec();

      const bankingSettings = i.bankingSettings ?? [];
      const isDuplicated =
        BankingService.getListOfBanks().some((item) => item.name === bankName) ||
        bankingSettings.some((item) => item.name === bankName);

      if (isDuplicated) {
        return res.Exception(new BadRequestError('Duplicate the bank name'));
      }

      // Add a manual bank with a unique token of 6 characters
      i.bankingSettings = [
        ...bankingSettings,
        { name: bankName, code: `${toCodeFormat(bankName)}-${generateRandomToken(6)}`, type: BankType.ManualBank },
      ];

      await i.save();
      res.FromModel(i.bankingSettings);
    } catch (e) {
      next(e);
    }
  })
  .patch(canManageInitiative, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId;
      const bankingSettings = req.body.bankingSettings as BankingSetting[];
      const i = await Initiative.findById(initiativeId).orFail().exec();

      if (!bankingSettings) {
        return res.Exception(new BadRequestError('Banking settings is required'));
      }

      i.bankingSettings = bankingSettings;
      await i.save();
      res.FromModel(i.bankingSettings);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/:initiativeId/workflow/:bankCode')
  .post(canManageInitiative, async (req, res, next) => {
    try {
      const bankingService = getBankingService();
      const response = await bankingService.addWorkflow(req.params.initiativeId, req.params.bankCode);
      res.FromModel(response);
    } catch (e) {
      next(e);
    }
  })
  .delete(canManageInitiative, async (req, res, next) => {
    try {
      const bankingService = getBankingService();
      const portfolio = await bankingService.removeWorkflow(req.params.initiativeId, req.params.bankCode);
      if (portfolio) {
        res.FromModel(portfolio);
      } else {
        res.Success();
      }
    } catch (e) {
      next(e);
    }
  });

router.route('/:initiativeId/workflow/:bankCode/add-metrics').post(canManageInitiative, async (req, res, next) => {
  try {
    const bankingService = getBankingService();
    const domain = req.header('origin');
    const dataShare = await bankingService.addAssignedMetrics({
      user: req.user,
      initiativeId: new ObjectId(req.params.initiativeId),
      domain,
      bankCode: req.params.bankCode
    });
    res.FromModel(dataShare);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
