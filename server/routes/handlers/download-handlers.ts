import { Response } from "express";
import { write } from "@sheet/core";
import { SurveyModelPlainWithInitiative } from "../../models/survey";
import { DownloadMultiScope } from "../../service/survey/scope/downloadScope";
import { getSimpleReportGenerator } from '../../service/custom-report/SimpleReportGenerator';
import { FileParserType } from "../../service/survey/transfer/parserTypes";
import { setCsvFileHeaders, setXlsxFileHeaders } from "../../http/FileDownload";
import { getSurveyExcel } from "../../service/survey/transfer/SurveyExcel";
import { stringifyArrayCsvFile } from '../../service/file/writer/CsvFileWriter';
import { DateFormat, customDateFormat } from "../../util/date";
import { DataPeriods } from "../../service/utr/constants";
import { DATA_PERIODS_UI_MAP } from "../../service/custom-report/constants";
import { getExcel } from '../../service/file/Excel';

interface SimpleDownloadHandlerParams {
  survey: SurveyModelPlainWithInitiative;
  downloadScope: DownloadMultiScope;
  res: Response;
  type: string;
}

const getFileName = (survey: SurveyModelPlainWithInitiative) => {
  return `${survey.initiative.name} ${DATA_PERIODS_UI_MAP[survey.period ?? DataPeriods.Yearly]} ${customDateFormat(
    survey.effectiveDate,
    DateFormat.MonthYear
  )} Data Report`;
};

export const simpleDownloadHandler = async ({ survey, downloadScope, res, type }: SimpleDownloadHandlerParams) => {
  const { headers, records } = await getSimpleReportGenerator().getDownloadData({
    surveys: [survey],
    downloadScope,
    initiativeId: survey.initiativeId,
  });

  const excel = getExcel();
  const SurveyExcel = getSurveyExcel();
  const exportType = type === FileParserType.Xlsx ? FileParserType.Xlsx : FileParserType.Csv;
  const filename = getFileName(survey);
  const fullFilename = `${filename}.${exportType}`;

  if (exportType === FileParserType.Xlsx) {
    setXlsxFileHeaders(res, fullFilename);
    const result = await SurveyExcel.createSimpleReportSheet({
      headers,
      sheets: [{ name: 'Data Report', data: [headers ?? [], ...records] }],
      isBoldHeader: true,
    });
    return res.send(write(result, { type: 'buffer', bookType: exportType, cellStyles: true }));
  }

  setCsvFileHeaders(res, fullFilename);
  return res.send(stringifyArrayCsvFile({ records: excel.getPlainSheetData(records), header: headers }));
};
