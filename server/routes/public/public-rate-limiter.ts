/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import rateLimit from "express-rate-limit";
import { UserType } from "../../models/user";
import { AuthType } from "./AuthTypes";
import { Request, Response } from "express";
import { RedisStore } from 'rate-limit-redis'
import { redisCache } from "../../service/cache";
import { wwgLogger } from "../../service/wwgLogger";


const seconds = 60;
const windowMs = seconds * 1000;
const authReqLimit = 60;

export const publicApiRateLimiter = rateLimit({
  windowMs: windowMs,
  legacyHeaders: false,
  standardHeaders: 'draft-6',
  passOnStoreError: true,
  skipFailedRequests: true,
  limit: async (req, res) => {
    if (req.auth) {
      return authReqLimit;
    }
    return 30
  },
  message: (req: Request, res: Response) => {
    if (req.auth) {
      return { message: `You can only make ${authReqLimit} requests every ${seconds} seconds, please try again later.`, success: false }
    }
    return { message: 'Too many requests, please try again later.', success: false }
  },
  keyGenerator: (req, res) => {
    if (req.auth) {
      if (req.auth.type === AuthType.ApiKey) {
        return req.auth.connection.shortToken;
      }

      if (req.auth.type === UserType.ServiceAccount) {
        return req.auth.connection.addonId?.toString();
      }
    }

    return req.ip ?? 'unknown';
  },
  store: new RedisStore({
    prefix: 'api-rate-limit:',
    sendCommand: async (...args: string[]) => {
      if (!redisCache.isReady) {
        wwgLogger.warn('Redis cache is not ready, skipping rate limit check');
        return ''
      }
      return redisCache.sendCommand(args);
    },
  }),
})
