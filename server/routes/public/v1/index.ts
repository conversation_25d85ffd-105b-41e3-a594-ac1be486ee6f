import express, { NextFunction } from "express";
import { publicAuth } from "../publicAuth";
import auth from "./auth";
import oauth from "./oauth";
import surveys from "./surveys";
import valueList from "./value-list";
import initiatives from "./initiatives";
import universalTrackerValues from "./universal-tracker-values";
import { AuthPublicRouter, PreAuthRequest } from "../PublicAuthRouter";
import { UserErrorMessages } from "../../../error/ErrorMessages";
import { Response } from "express-serve-static-core";
import { publicApiRateLimiter } from "../public-rate-limiter";

const router = express.Router() as AuthPublicRouter;
export const UserActive = (req: PreAuthRequest, res: Response, next: NextFunction) => {
  if (!req.auth?.user.active) {
    return res.status(403).json({
      success: false,
      message: UserErrorMessages.NotActive,
    });
  }
  return next();
};

const Auth = [publicAuth, UserActive, publicApiRateLimiter];

router.route('/').get(publicAuth, UserActive, (req, res) => {
  res.FromModel(req.auth)
});

router.use('/auth', publicApiRateLimiter, auth);
router.use('/oauth', publicApiRateLimiter, oauth);
router.use('/surveys', Auth, surveys);
router.use('/initiatives', Auth, initiatives);
router.use('/value-list', Auth, valueList);
router.use('/universal-tracker-values', Auth, universalTrackerValues);


module.exports = router;
