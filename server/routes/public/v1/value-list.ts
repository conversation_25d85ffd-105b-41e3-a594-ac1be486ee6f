import express from "express";
import ValueList from "../../../models/valueList";
import { ScopePermissions } from "../../../public-api/scopePermission";
import PermissionDeniedError from "../../../error/PermissionDeniedError";
import { AuthPublicRouter } from "../PublicAuthRouter";
import { ScopePermission } from "../../../public-api/scopePermissionModels";

const router = express.Router() as AuthPublicRouter;

router.route('/:id')
  .get((req, res, next) => {
    if (!ScopePermissions.hasScope(req.auth, ScopePermission.ValueListRead)) {
      return next(new PermissionDeniedError())
    }

    ValueList.findById(req.params.id, { options: 1 }).lean().orFail().exec()
      .then((vl) => res.FromModel(vl))
      .catch(e => next(e))
  });

export default router;
