/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import ValueList from '../models/valueList';
import { getDashboardSharingService } from '../service/insight-dashboard/DashboardSharingService';

const router = express.Router();
const dashboardSharingService = getDashboardSharingService();

router.route('/:id')
  .get((req, res) => {
    ValueList.findById(req.params.id).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  })

router.route('/:id/insight-dashboards/:dashboardId/token/:token').get(async (req, res, next) => {
  try {
    await dashboardSharingService.findSharedDashboard({ dashboardId: req.params.dashboardId, token: req.params.token });
    const valueList = await ValueList.findById(req.params.id).lean().exec();
    res.FromModel(valueList);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
