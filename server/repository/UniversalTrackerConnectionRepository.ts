import { ObjectId } from 'bson';
import { CalculationGroup, CalculationGroupPlain } from '../models/calculationGroup';
import UniversalTracker from '../models/universalTracker';
import { UniversalTrackerConnectionPlain, UniversalTrackerConnection } from '../models/universalTrackerConnection';

export interface ExpandedConnection extends Omit<UniversalTrackerConnectionPlain, 'calculations'> {
  calculationGroups: CalculationGroupPlain[];
}

export class UniversalTrackerConnectionRepository {
  constructor(
    private model: typeof UniversalTrackerConnection,
    private utrModel: typeof UniversalTracker,
    private calculationGroupModel: typeof CalculationGroup
  ) {}

  async getConnections(utrId: ObjectId): Promise<ExpandedConnection[]> {
    const { code: utrCode } = await this.utrModel.findById(utrId, { code: 1 }).orFail().lean().exec();

    const utrConnections = await this.model.find({ utrCode }).lean<UniversalTrackerConnectionPlain[]>().exec();
    if (!utrConnections.length) {
      return [];
    }

    const calculationGroupIdSet = new Set<string>();
    utrConnections.forEach((connection) =>
      connection.calculations.forEach(({ calculationGroupId }) => {
        calculationGroupIdSet.add(calculationGroupId.toString());
      })
    );

    const calculationGroupIds = Array.from(calculationGroupIdSet).map((id) => new ObjectId(id));
    const calculationGroups = await this.calculationGroupModel
      .find({ _id: { $in: calculationGroupIds } })
      .lean()
      .exec();

    return utrConnections.map((connection) => {
      const { calculations, ...rest } = connection;
      return {
        ...rest,
        calculationGroups: calculationGroups.filter((group) =>
          calculations.some(({ calculationGroupId }) => calculationGroupId.equals(group._id))
        ),
      };
    });
  }
}

let instance: UniversalTrackerConnectionRepository;

export const getUniversalTrackerConnectionRepository = () => {
  if (!instance) {
    instance = new UniversalTrackerConnectionRepository(UniversalTrackerConnection, UniversalTracker, CalculationGroup);
  }
  return instance;
};
