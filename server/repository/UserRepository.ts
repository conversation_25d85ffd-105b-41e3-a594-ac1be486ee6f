/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import User, {
  completeUserFields,
  ExtendedCompleteUser,
  SafeUser,
  safeUserFields,
  StatsUser,
  statsUserFields,
  UserMin,
  userMinFields,
  UserModel,
  userPlain,
  UserPlain,
} from '../models/user';
import { InitiativeRepository } from './InitiativeRepository';
import { UserPermissions, UserRoles } from '../service/user/userPermissions';
import { AssurancePortfolioStatus } from '../service/assurance/model/AssurancePortfolio';
import AssurancePortfolio from '../models/assurancePortfolio';
import { PipelineStage, ProjectionType } from 'mongoose';
import { KeysEnum } from '../models/commonProperties';

const emailRules: ConvertEmailRules = {
  numOfFirstLetters: 4,
  numOfAtLetters: 5,
}

export interface ConvertEmailRules {
  numOfFirstLetters?: number;
  numOfAtLetters?: number;
}

interface UsersByInitiativeAndParentsParams {
  initiativeId: ObjectId;
  roles: UserRoles[];
  projection?: ProjectionType<UserPlain> | null;
}

interface UsersByInitiativeIds {
  initiativeIds: ObjectId[];
  roles: UserRoles[];
  projection?: ProjectionType<UserPlain> | null;
}

export interface UsersSearchResult
  extends Pick<UserPlain, '_id' | 'email' | 'firstName' | 'surname' | 'profile' | 'permissions'> {
  jobTitle?: string;
}

export class UserRepository {

  static searchFields = { firstName: 1, surname: 1, jobTitle: 1, email: 1, profile: 1 };
  static searchFieldsWithPermissions: KeysEnum<UsersSearchResult> = {
    ...UserRepository.searchFields,
    _id: 1,
    permissions: 1,
  };

  public static async findByIds(ids: ObjectId[], lean = true): Promise<UserPlain[]> {
    return User.find({ _id: { $in: ids } }).lean(lean).exec();
  }

  public static async findById(id: ObjectId | string, lean = true): Promise<UserPlain | null> {
    return User.findById(id).lean(lean).exec();
  }

  public static async findByIdSafeUser(id: ObjectId | string, lean = true): Promise<SafeUser | null> {
    return User.findById(id, safeUserFields).lean(lean).exec();
  }

  public static async findByIdStatsUser(id: ObjectId | string, lean = true): Promise<StatsUser | null> {
    return User.findById(id, statsUserFields).lean(lean).exec();
  }

  public static async findByIdOrEmail({ userId, email }: { userId?: ObjectId | string; email?: string }, lean?: false): Promise<UserModel | null>
  public static async findByIdOrEmail({ userId, email }: { userId?: ObjectId | string; email?: string }, lean?: true): Promise<UserPlain | null>
  public static async findByIdOrEmail({ userId, email }: { userId?: ObjectId | string; email?: string }, lean = true): Promise<UserModel | UserPlain | null> {
    if (userId) {
      return User.findById(userId).lean(lean).orFail().exec();
    }
    return User.findOne({ email }).lean(lean).orFail().exec();
  }

  public static async findManyByEmail(emails: string[], lean?: false): Promise<UserModel[]>
  public static async findManyByEmail(emails: string[], lean?: true): Promise<UserPlain[]>
  public static async findManyByEmail(emails: string[], lean = true): Promise<UserModel[] | UserPlain[]> {
    return User.find({ email: { $in: emails } }).lean(lean).exec();
  }

  public static async getUsersByInitiativeTree(initiativeId: string, projection: {} = {}): Promise<SafeUser[]> {
    if (!ObjectId.isValid(initiativeId)) {
      return [];
    }

    const ids = await InitiativeRepository.getFullTree(initiativeId)
      .then(d => d.map((i) => i._id));

    return User
      .find(
        { 'permissions.initiativeId': { $in: ids } },
        projection
      )
      .sort({ surname: 1, firstName: 1 })
      .lean()
      .exec();
  }

  public static async getUsersByInitiativeAndDescendants(
    initiativeId: ObjectId,
    projection: ProjectionType<UserPlain> | null  = { _id: 1, surname: 1, firstName: 1 }
  ): Promise<UserPlain[]> {
    const children = await InitiativeRepository.getAllChildrenById(initiativeId);
    return User.find({ 'permissions.initiativeId': { $in: [initiativeId, ...children.map(child => child._id)] } }, projection)
      .sort({ surname: 1, firstName: 1 })
      .lean()
      .exec();
  }

  public static async getUsersByInitiativeAndParents<T extends UserMin = UserMin>({
    initiativeId,
    roles,
    projection = { _id: 1, surname: 1, firstName: 1 },
  }: UsersByInitiativeAndParentsParams): Promise<T[]> {
    // current level and above
    const currentLevelAndAbove = await InitiativeRepository.getCurrentAndParentInitiativeIds(initiativeId);

    return User.find({
      permissions: {
        $elemMatch: { initiativeId: { $in: currentLevelAndAbove }, permissions: { $in: roles } },
      },
    }, projection)
      .lean<T[]>()
      .exec();
  }

  public static async getUsersInitiativeIds<T extends UserMin = UserMin>(lookupData: UsersByInitiativeIds) {
    const {
      initiativeIds,
      roles,
      projection = { _id: 1, surname: 1, firstName: 1 },
    } = lookupData;

    return User.find({
      permissions: {
        $elemMatch: { initiativeId: { $in: initiativeIds }, permissions: { $in: roles } },
      },
    }, projection)
      .lean<T[]>()
      .exec();
  }

  public static async getUsersWithOrganization(userIds?: any[], projection: {} = {}) {

    const userMatch: { [K in keyof SafeUser]?: any } = {
      active: true,
    };

    if (Array.isArray(userIds) && userIds.length > 0) {
      userMatch._id = { $in: userIds.map(i => new ObjectId(i)) };
    }

    const aggregations: any[] = [
      {
        $match: userMatch
      },
      {
        $lookup: {
          from: 'organizations',
          localField: 'organizationId',
          foreignField: '_id',
          as: 'organization'
        }
      },
      {
        $project: {
          ...safeUserFields,
          name: { $concat: ['$firstName', ' ', '$surname'] },
          ...projection,
          organization: { $arrayElemAt: ['$organization', 0] }
        }
      },
      {
        $project: {
          ...safeUserFields,
          name: 1,
          organization: {
            _id: 1,
            name: 1,
            profile: 1,
            created: 1,
            organizationType: 1,
            entityLevel: 1,
            address: 1,
          },
          ...projection
        }
      }
    ];

    return User.aggregate(aggregations);
  }

  public static async getUsersWithExtendedPermissions(match: PipelineStage.Match): Promise<ExtendedCompleteUser[]> {

    const aggregations: PipelineStage[] = [
      match,
      {
        $unwind: {
          path: '$permissions',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'permissions.initiativeId',
          foreignField: '_id',
          as: 'initiative',
        },
      },
      // append initiative name and code to permission
      {
        $project: {
          ...completeUserFields,
          name: { $concat: ['$firstName', ' ', '$surname'] },
          permissions: {
            permissions: '$permissions.permissions',
            initiativeId: '$permissions.initiativeId',
            initiativeName: { $arrayElemAt: ['$initiative.name', 0] },
            initiativeCode: { $arrayElemAt: ['$initiative.code', 0] },
            permissionGroup: { $arrayElemAt: ['$initiative.permissionGroup', 0] },
            appConfigCode: { $arrayElemAt: ['$initiative.appConfigCode', 0] },
          },
        },
      },
      // Remove fields that is empty or null using filter
      // $$this.v represents the value of the current field being processed
      {
        $replaceWith: {
          $arrayToObject: {
            $filter: {
              input: { $objectToArray: '$$ROOT' },
              cond: { $not: { $in: ['$$this.v', [null, '', {}]] } },
            },
          },
        },
      },
      // Group by userId and permissions, keep all other fields
      {
        $group: {
          _id: '$_id',
          permissions: { $push: '$permissions' },
          oldRoot: { $last: '$$ROOT' },
        },
      },
      {
        $replaceRoot: {
          newRoot: { $mergeObjects: ['$oldRoot', '$$ROOT'] },
        },
      },
      {
        $project: {
          ...completeUserFields,
        },
      },
      {
        $sort: {
          name: -1,
          created: -1,
        }
      }
    ];
    return User.aggregate(aggregations);
  }

  public static async getUsersMin(userIds: any[]): Promise<UserMin[]> {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return [];
    }

    return User.aggregate([
      {
        $match: {
          _id: { $in: userIds.map(i => new ObjectId(i)) }
        }
      },
      {
        $project: {
          ...userMinFields,
          name: { $concat: ['$firstName', ' ', '$surname'] },
        }
      }
    ]);
  }

  public static convertEmail = (email: string, rules?: ConvertEmailRules) => {
    const numOfFirstLetters = rules?.numOfFirstLetters ?? 1;
    const numOfAtLetters = rules?.numOfAtLetters ?? 1; // including @ letter
    const atPos = email.lastIndexOf('\@');
    const dotPos = email.lastIndexOf('\.');
    const tld = email.substring(dotPos);

    const firstLetters = email.substring(0, numOfFirstLetters < atPos ? numOfFirstLetters : 1);
    const atLetters = email.substring(atPos, numOfAtLetters < dotPos - atPos ? atPos + numOfAtLetters : atPos + 1);

    return `${firstLetters}***${atLetters}***${tld}`;
  }

  public static anonymize = (data: Pick<UserPlain, 'email'>[], rules?: ConvertEmailRules) => {
    return data.map(d => {
      d.email = UserRepository.convertEmail(d.email, rules);
      return d;
    });
  }

  public static async searchByInitiativeId(searchStr: string, initiativeId: string) {

    if (!searchStr || !ObjectId.isValid(initiativeId)) {
      return [];
    }

    const search = decodeURIComponent(searchStr).replace(/ /g, '|');
    const searchRegex = new RegExp('.*(' + search + ').*', 'i');

    const ids = await InitiativeRepository.getFullTree(initiativeId)
      .then(d => d.map((i: any) => i._id));

    const whereQuery = {
      $and: [
        { 'permissions.initiativeId': { $in: ids } },
        {
          $or: [
            { email: { $regex: searchRegex } },
            { firstName: { $regex: searchRegex } },
            { surname: { $regex: searchRegex } }
          ]
        },
      ]
    };

    return User.find(whereQuery, UserRepository.searchFields)
      .sort({ surname: 1, firstName: 1 })
      .limit(20)
      .lean()
      .exec();
  }

  public static async searchUsersForDelegation({
    initiativeIds,
    searchRegex,
    visibleStakeholders,
  }: {
    initiativeIds: ObjectId[];
    searchRegex: RegExp;
    visibleStakeholders: ObjectId[];
  }): Promise<UsersSearchResult[]> {
    const whereQuery = {
      $and: [
        {
          $or: [{ 'permissions.initiativeId': { $in: initiativeIds } }, { _id: { $in: visibleStakeholders } }],
        },
        {
          $or: [
            { email: { $regex: searchRegex } },
            { firstName: { $regex: searchRegex } },
            { surname: { $regex: searchRegex } },
          ],
        },
      ],
    };

    return User.find(whereQuery, UserRepository.searchFieldsWithPermissions)
      .sort({ surname: 1, firstName: 1 })
      .limit(20)
      .lean()
      .exec();
  }

  public static async searchByOrganizationId(searchStr: string, organizationId: string) {

    if (!searchStr || !ObjectId.isValid(organizationId)) {
      return [];
    }

    const search = decodeURIComponent(searchStr).replace(/ /g, '|');
    const searchRegex = new RegExp('.*(' + search + ').*', 'i');

    const whereQuery = {
      $and: [
        { organizationId: new ObjectId(organizationId) },
        {
          $or: [
            { email: { $regex: searchRegex } },
            { firstName: { $regex: searchRegex } },
            { surname: { $regex: searchRegex } },
          ]
        },
      ]
    };

    return User.find(whereQuery, UserRepository.searchFields)
      .sort({ surname: 1, firstName: 1 })
      .limit(20)
      .lean()
      .exec();
  }

  public static async findInitiativeUsers(
    initiativeId: ObjectId | string,
    role?: UserRoles | UserRoles[],
    projection: {} = {}
  ) {
    const whereQuery = !role?.length
      ? { 'permissions.initiativeId': initiativeId }
      : {
          permissions: {
            $elemMatch: { initiativeId, permissions: { $in: role } },
          },
        };

    return User.find(whereQuery, { ...UserRepository.searchFields, ...projection })
      .lean()
      .exec();
  }

  public static async findInitiativesUsers(initiativeIds: ObjectId | ObjectId[], includeStaff: boolean): Promise<StatsUser[]> {
    const ids = Array.isArray(initiativeIds) ? initiativeIds : [initiativeIds];

    const filter: Record<string, unknown> = {
      'permissions.initiativeId': { $in: ids },
    };

    if (!includeStaff) {
      // Cover the case where isStaff is not even set
      filter.isStaff = { $ne: true };
    }

    return User.find(filter, statsUserFields).lean().exec();
  }

  public static async findInitiativeOwners(initiativeId: ObjectId) {
    const includeStaff = true;
    const users = await UserRepository.findInitiativesUsers(initiativeId, includeStaff);
    const owners = users.filter((u) =>
      u.permissions.some((p) => p.initiativeId.equals(initiativeId) && p.permissions.includes(UserRoles.Owner))
    );
    return UserRepository.anonymize(owners, emailRules);
  }

  public static async searchByAssurancePortfolioId(searchStr: string, assurancePortfolioId: string) {

    if (!searchStr || !ObjectId.isValid(assurancePortfolioId)) {
      return [];
    }

    const search = decodeURIComponent(searchStr).replace(/ /g, '|');
    const searchRegex = new RegExp('.*(' + search + ').*', 'i');

    const aggregations: PipelineStage[] = [
      {
        $match: {
          _id: new ObjectId(assurancePortfolioId),
          status: { $ne: AssurancePortfolioStatus.Deleted },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'permissions.userId',
          foreignField: '_id',
          as: 'assurers',
        },
      },
      {
        $unwind: {
          path: '$assurers',
        },
      },
      {
        $replaceRoot: {
          newRoot: '$assurers',
        },
      },
      {
        $match: {
          $or: [
            { email: { $regex: searchRegex } },
            { firstName: { $regex: searchRegex } },
            { surname: { $regex: searchRegex } },
          ],
        },
      },
      {
        $project: {
          ...userMinFields,
          email: 1,
        },
      },
      { $sort: { surname: 1, firstName: 1 } },
      { $limit: 20 },
    ];

    return AssurancePortfolio.aggregate(aggregations);
  }

  public static async findByCreatedBetween(dateFrom: Date, dateTo: Date, includeStaff: boolean = false): Promise<UserPlain[]> {
    const filter: Record<string, unknown> = {
      created: { $gte: dateFrom, $lte: dateTo },
    };

    if (!includeStaff) {
      // Cover the case where isStaff is not even set
      filter.isStaff = { $ne: true };
    }

    return User.find(filter, { ...userPlain, created: 1 }).lean().exec();
  }

  public static async getUsersByEmailDomain(emailDomain: string) {
    return User.find({
      email: { $regex: `.*@${emailDomain}$` }
    }, { permissions: 1 }).lean().exec();
  }

  public static addUserPermission(userIds: ObjectId[], permissions: UserPermissions) {
    return User.updateMany(
      { _id: { $in: userIds } },
      { $push: { permissions } }
    ).exec();
  }
}
