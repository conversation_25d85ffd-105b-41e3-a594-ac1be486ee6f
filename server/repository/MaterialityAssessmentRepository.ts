import { ObjectId } from 'bson';
import { ValueData } from '../models/public/universalTrackerValueType';
import { Option } from '../models/public/valueList';
import Survey, { SurveyModelPlain } from '../models/survey';
import { KeysEnum } from '../models/public/projectionUtils';
import { excludeSoftDeleted } from './aggregations';
import { materialityContextUtrCodes } from '../routes/validation-schemas/materiality-assessment';
import ContextError from '../error/ContextError';
import { ScopeCalculatorContext } from '../service/materiality-assessment/MaterialityAssessmentSizeCalculator';

export interface AssessmentMinData extends Pick<SurveyModelPlain, '_id' | 'name' | 'effectiveDate'> {
  visibleUtrvs: {
    _id: ObjectId;
    status: string;
    valueData: ValueData<string | string[]>;
    universalTracker: {
      _id: ObjectId;
      name: string;
      code: string;
    };
    valueList: Option[];
  }[];
}

export const projectionFields: KeysEnum<AssessmentMinData> = {
  _id: 1,
  name: 1,
  effectiveDate: 1,
  visibleUtrvs: {
    _id: 1,
    status: 1,
    valueData: 1,
    universalTracker: {
      _id: 1,
      name: 1,
      code: 1,
    },
    valueList: 1,
  },
};

export class MaterialityAssessmentRepository {
  constructor(private surveyModel: typeof Survey) {}
  public async getAssessmentData(surveyId: ObjectId | string): Promise<AssessmentMinData[]> {
    const aggregate = [
      {
        $match: {
          _id: new ObjectId(surveyId),
          ...excludeSoftDeleted(),
        },
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleUtrvs',
        },
      },
      {
        $unwind: '$visibleUtrvs',
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'visibleUtrvs.universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker',
        },
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'valueList',
        },
      },
      {
        $group: {
          _id: '$_id',
          name: { $first: '$name' },
          effectiveDate: { $first: '$effectiveDate' },
          visibleUtrvs: {
            $push: {
              _id: '$visibleUtrvs._id',
              status: '$visibleUtrvs.status',
              valueData: '$visibleUtrvs.valueData',
              universalTracker: {
                $arrayElemAt: ['$universalTracker', 0],
              },
              valueList: { $arrayElemAt: ['$valueList.options', 0] },
            },
          },
        },
      },
      {
        $project: projectionFields,
      },
    ];
    return this.surveyModel.aggregate(aggregate).exec();
  }

  public async getAssessmentSetupAnswers(
    assessmentId: string | ObjectId
  ): Promise<ScopeCalculatorContext> {
    const [result] = await this.surveyModel
      .aggregate<ScopeCalculatorContext>([
        {
          $match: {
            _id: new ObjectId(assessmentId),
            ...excludeSoftDeleted(),
          },
        },
        {
          $lookup: {
            from: 'universal-tracker-values',
            localField: 'visibleUtrvs',
            foreignField: '_id',
            as: 'utrvs',
          },
        },
        { $unwind: '$utrvs' },
        {
          $lookup: {
            from: 'universal-trackers',
            localField: 'utrvs.universalTrackerId',
            foreignField: '_id',
            as: 'utr',
          },
        },
        { $unwind: '$utr' },
        {
          $match: {
            'utr.code': { $in: materialityContextUtrCodes },
          },
        },
        {
          $group: {
            _id: null,
            data: {
              $push: {
                k: '$utr.code',
                v: { $ifNull: ['$utrvs.valueData.data', ''] },
              },
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: { $arrayToObject: '$data' },
          },
        },
      ])
      .exec();

    if (!result) {
      throw new ContextError(`Assessment not found for id ${assessmentId}`, { assessmentId });
    }

    return result;
  }
}

let instance: MaterialityAssessmentRepository;
export const getMaterialityAssessmentRepository = () => {
  if (!instance) {
    instance = new MaterialityAssessmentRepository(Survey);
  }

  return instance;
};
