/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { DefaultBlueprintCode } from '../survey/blueprints';
import ReportingFramework, { CreateReportingFrameworkData } from '../models/reportingFramework';
import sdgMap from '../static/un-sdg';
import { Tags } from '../models/universalTracker';
import { frameworks, standards, findChildByCode } from '@g17eco/core';

const bc = getBluePrintContribution();

interface SunburstDataItem {
  name: string;
  type: string; // User for url slug
  code: string;
  value?: number;
  children?: SunburstDataItem[];
}

interface SDGToFrameworkItems {
  code: string;
  label: string;
  items: Set<{ code: string, name: string }>;
}

export class ReportingFrameworkRepository {

  public static async getSDGToFrameworkMap() {
    const reportingFrameworks = await ReportingFramework.find().sort({ name: 1 }).exec();
    return ReportingFrameworkRepository.getMap(reportingFrameworks);
  }

  private static async getMap(reportingFrameworks: CreateReportingFrameworkData[]) {

    const sdgToFrameworks = new Map<string, Map<string, SDGToFrameworkItems>>();
    reportingFrameworks.forEach((framework) => {
      const frameworkCode = framework.code;

      framework.items.forEach((item) => {
        item.sdgCodes.forEach((sdgCode) => {
          const sdgComponents = sdgCode.split('.');
          let sdgCodeCombined = '';
          sdgComponents.forEach((sdgComponent) => {
            sdgCodeCombined += sdgComponent;
            let frameworkMap = sdgToFrameworks.get(sdgCodeCombined);
            if (!frameworkMap) {
              frameworkMap = new Map();
            }
            let frameworkData = frameworkMap.get(frameworkCode);
            if (!frameworkData) {
              frameworkData = {
                code: framework.code,
                label: framework.shortName,
                items: new Set()
              };
            }

            frameworkData.items.add({
              code: item.itemCode,
              name: item.itemName
            });
            frameworkMap.set(frameworkCode, frameworkData);
            sdgToFrameworks.set(sdgCodeCombined, frameworkMap);
            sdgCodeCombined += '.';
          });
        });
      });
    });

    const map: SunburstDataItem = {
      name: 'SDG',
      type: 'sdg',
      code: '',
      children: []
    };
    sdgMap.forEach((goal) => {
      const goalSlice = 100 / 17;
      const goalObj: SunburstDataItem = {
        name: 'SDG ' + goal.code,
        type: 'sdg',
        code: goal.code,
        children: [],
      };

      const targetSlice = goalSlice / goal.targets.length;
      goal.targets.forEach((target) => {
        const targetObj: SunburstDataItem = {
          name: 'SDG ' + target.code,
          type: 'sdg',
          code: target.code,
          children: [],
        };
        const frameworkMap = sdgToFrameworks.get(target.code);
        if (!frameworkMap) {
          targetObj.value = Math.round(targetSlice * 10) / 10;
          goalObj.children?.push(targetObj);
          return;
        }

        const frameworkKeys = frameworkMap.keys();
        const frameworkSlice = targetSlice / frameworkMap.size;
        for (const key of frameworkKeys) {
          const frameworkData = frameworkMap.get(key);
          if (!frameworkData) {
            continue;
          }
          targetObj.children?.push({
            name: frameworkData.label,
            code: '',
            type: frameworkData.code,
            value: Math.round(frameworkSlice * 10) / 10
          });
        }
        goalObj.children?.push(targetObj);
      });
      map.children?.push(goalObj);
    });


    return {
      frameworks: reportingFrameworks,
      map
    }
  }

  public static async getSDGToFrameworkMapFromSurvey(blueprintCode = DefaultBlueprintCode) {

    const utrs = await bc.getQuestions(blueprintCode);
    await bc.populateSdgTagsProperty(blueprintCode, utrs);

    const reportingFrameworksMap: Map<string, CreateReportingFrameworkData> = new Map();

    const populateCode = (type: 'standard' | 'framework', code: string) => {
      if (reportingFrameworksMap.has(code)) {
        return;
      }

      const data = type === 'framework' ? frameworks[code] : standards[code];
      return reportingFrameworksMap.set(code, {
        code: data.code,
        name: data.name,
        shortName: data.shortName,
        description: data.description ?? '',
        type: type,
        items: []
      });
    }

    const populate = (obj: CreateReportingFrameworkData, itemCode: string, displayCode: string, name: string, sdgCodes: string[]) => {
      const existingItem = obj.items.find(i => i.itemCode === itemCode);
      if (!existingItem) {
        obj.items.push({
          itemCode: displayCode,
          itemName: name,
          sdgCodes: sdgCodes
        });
      } else {
        existingItem.sdgCodes = [...existingItem.sdgCodes, ...sdgCodes].filter((v, i, a) => a.indexOf(v) == i); // uniquify
      }
    }

    const populateSdgCodes = (type: 'standard' | 'framework', code: string, tagItems: string[], sdgCodes: string[]) => {
      const obj = reportingFrameworksMap.get(code);
      if (!obj) {
        return;
      }

      const data = type === 'framework' ? frameworks[code] : standards[code];

      if (tagItems.length === 0) {
        populate(obj, code, data.shortName, data.name, sdgCodes);
        return;
      }
      if (!data.subgroups) {
        tagItems.forEach(tagItem => {
          populate(obj, tagItem ?? code, tagItem ?? data.shortName, tagItem ?? data.name, sdgCodes);
        });
        return;
      }

      tagItems.forEach(tagItem => {
        if (!tagItem) {
          return;
        }

        const subGroup = findChildByCode(data, tagItem);
        const itemCode = subGroup?.officialCode ?? subGroup?.code ?? tagItem;

        populate(obj, itemCode, itemCode, subGroup?.name ?? tagItem, sdgCodes);
      });
    }

    utrs.forEach(utr => {
      if (!utr.tags?.sdg) {
        return;
      }
      const tags: Tags = utr.tags;
      const sdgCodes = tags.sdg;
      const frameworkCodes = Object.keys(utr.tags);
      frameworkCodes
        .filter(code => frameworks[code] && tags[code].length > 0)
        .forEach(code => {
          populateCode('framework', code);
          const tagItems = tags[code];
          populateSdgCodes('framework', code, tagItems, sdgCodes);
        });

      if (standards[utr.type]) {
        const code = utr.type;
        populateCode('standard', code);
        let tagItems = utr.typeTags ?? [];
        if (tagItems.length === 0 && utr.typeCode !== undefined) {
          tagItems = [utr.typeCode ?? ''];
        }
        populateSdgCodes('standard', code, tagItems, sdgCodes);
      }

      if (!utr.alternatives) {
        return;
      }
      const standardCodes = Object.keys(utr.alternatives);
      standardCodes
        .filter(code => standards[code])
        .forEach(code => {
          populateCode('standard', code);
          let tagItems = utr.alternatives?.[code].typeTags ?? [];
          if (tagItems.length === 0 && utr.alternatives?.[code].typeCode !== undefined) {
            tagItems = [utr.alternatives[code].typeCode ?? ''];
          }
          populateSdgCodes('standard', code, tagItems, sdgCodes);
        });

    });

    const reportingFrameworks = Array.from(reportingFrameworksMap.values()).filter(m => m.items.length > 0);
    return ReportingFrameworkRepository.getMap(reportingFrameworks);
  }
}
