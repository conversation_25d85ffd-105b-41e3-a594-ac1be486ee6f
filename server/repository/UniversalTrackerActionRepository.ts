/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UniversalTrackerValue from '../models/universalTrackerValue';
import { ObjectId } from 'bson';
import {
  userPendingActionMatch
} from './aggregations';
import {
  actionProjection,
  initiativeMinFields,
  valueValidationProjection
} from './projections';
import {
  documentsLookup,
  initiativeLookup,
  universalTrackerLookup,
  userStakeholderAndHistoryLookup,
  userStakeholderLookup
} from './utrvAggregations';
import { addDocumentUrl } from '../service/storage/fileStorage';
import { getLatestStakeholdersHistory } from '../service/utr/utrvHistory';
import { UtrvPermissions } from '../service/utr/UtrvPermissions';
import { UserModel } from '../models/user';
import PermissionDeniedError from '../error/PermissionDeniedError';

export default class UniversalTrackerActionRepository {

  public static async getUserPendingActions(userId: ObjectId) {

    const baseProjection = {
      ...actionProjection,
      initiatives: initiativeMinFields
    };

    const utrvAggregation = [
      { $match: userPendingActionMatch(userId) },
      universalTrackerLookup,
      ...userStakeholderLookup,
      initiativeLookup,
      {
        $project: {
          ...baseProjection,
          universalTracker: { $arrayElemAt: ['$universalTracker', 0] },
        }
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'list'
        }
      },
      {
        $project: {
          ...baseProjection,
          universalTracker: {
            name: 1,
            type: 1,
            ...valueValidationProjection,
          },
        }
      },
    ];

    return UniversalTrackerValue.aggregate(utrvAggregation);
  }

  public static async getUtrvByIdExtended(utrvId: string) {

    const utrvIdObject = new ObjectId(utrvId);

    const match = {
      $and: [
        { _id: utrvIdObject }
      ]
    };

    const baseProjection = {
      ...actionProjection,
      initiatives: 1,
      documents: 1,
      evidenceRequired: 1,
      history: 1,
      type: 1,
    };

    return UniversalTrackerValue.aggregate([
      {
        $match: match
      },
      universalTrackerLookup,
      ...userStakeholderAndHistoryLookup,
      initiativeLookup,
      documentsLookup,
      {
        $project: {
          ...baseProjection,
          universalTracker: { $arrayElemAt: ['$universalTracker', 0] },
        }
      },
    ]).then(async ([utrv]) => {
      if (!utrv || !utrv.universalTracker) {
        return;
      }

      const { documents, users, initiatives, universalTracker, ...rest } = utrv;

      // Converting to something similar to framework utr
      const converted = {
        ...universalTracker,
        documents,
        users,
        initiatives,
        universalTrackerValues: [rest]
      };

      await addDocumentUrl(converted);
      return converted;
    });
  }

  public static async getUtrvStakeholdersHistory(utrvId: string, user: UserModel) {
    const utrv = await UniversalTrackerValue.findById(utrvId).orFail().lean().exec();
    if (!await UtrvPermissions.canAccess(utrv, user)) {
      throw new PermissionDeniedError();
    }
    const history = getLatestStakeholdersHistory(utrv);
    return { latestHistory: history };
  }

  public static async getUtrvStakeholdersHistoryAssurer(utrvId: string, user: UserModel) {
    const utrv = await UniversalTrackerValue.findById(utrvId).orFail().lean().exec();
    if (!await UtrvPermissions.canAssure(utrv, user)) {
      throw new PermissionDeniedError();
    }
    const history = getLatestStakeholdersHistory(utrv);
    return { latestHistory: history };
  }

}
