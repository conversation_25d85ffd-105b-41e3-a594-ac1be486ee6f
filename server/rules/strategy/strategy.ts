/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CalculationRule, CalculationStrategy } from '../rule';
import { ResolvedImportConfiguration, ValidationRules } from '../calculation';
import { ConditionsProcessingType } from '../condition';
import { wwgLogger } from '../../service/wwgLogger';
import ValueValidationError from '../../error/ValueValidationError';
import { NotApplicableTypes } from '../../models/universalTrackerValue';
import { isNAError, isNRError } from '../calculation/formula';

type CalculationProcessing = (definition: CalculationRule) => number | NotApplicableTypes | undefined;

interface ProcessingBase {
  calculationProcess: CalculationProcessing;
  conditionProcess: ConditionsProcessingType;
}

export interface StrategyProcess extends ProcessingBase {
  rules: ResolvedImportConfiguration;
}

interface ValidationProcessing extends ProcessingBase {
  validation: ValidationRules;
}

export const preValidate = async (strategyProcess: ValidationProcessing) => {

  const { validation, variables } = strategyProcess.validation;
  if (!validation) {
    return true; // Nothing to validate
  }

  let preValidation;
  try {
    preValidation = strategyProcess.conditionProcess(validation.conditions, variables);
  } catch (e) {
    if (isNAError(e) || isNRError(e)) {
      wwgLogger.info('Expression error %j', e);
      preValidation = true;
    }
  }

  if (!preValidation) {
    wwgLogger.error('Failed to process pre validation rule %j', strategyProcess.validation);
    throw new ValueValidationError('Failed to process pre validation rule');
  }

  return true;
};

const processFirstMatch = async ({ rules, calculationProcess, conditionProcess }: StrategyProcess) => {

  const { variables, calculation, validation } = rules;

  if (validation && validation.pre) {
    await preValidate({
      validation: { validation: validation.pre, variables },
      calculationProcess,
      conditionProcess
    }
    );
  }

  for (const value of calculation.values) {
    // If all condition match
    if (conditionProcess(value.conditions, variables)) {

      // Calculate value
      return calculationProcess({ variables, value });
    }
  }

  return;
};

export function getStrategy(type: string) {
  switch (type) {
    case CalculationStrategy.FirstMatch:
      return processFirstMatch;
    default:
      throw new Error(
        `calculation strategy type "${type}" is not supported`
      );
  }
}

