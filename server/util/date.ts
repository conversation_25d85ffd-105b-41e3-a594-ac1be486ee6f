/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import moment, { DurationInputArg1, DurationInputArg2, unitOfTime } from 'moment';
import { TimeFrameType } from '../models/insightDashboard';

export enum DateFormat {
  DefaultDashes = 'YYYY-MM-DD',
  Sortable = 'YYYY-MM-DD HH:mm:ss',
  SortableSlash = 'DD/MM/YYYY HH:mm:ss',
  Slash = 'DD/MM/YYYY',
  Humanize = 'humanize',
  YearMonth = 'YYYY MMMM',
  YearMonthShort = 'YYYY MMM',
  FileName = 'YYYY-MM-DD_HH-mm-ss',
  DayMonthYear = 'DD MMMM YYYY',
  MonthDayYear = 'MMM DD, YYYY',
  FullMonthName = 'MMMM',
  Year = 'YYYY',
  MonthYear = 'MMMM YYYY',
  DayMonth = 'DD MMM'
}

type DateString = string | Date;
export interface DateRange {
  startDate?: string;
  endDate?: string;
}

export const customDateFormat = (date: Date | string | undefined, format = DateFormat.DefaultDashes, utc = true) => {
  if (!date) {
    return '';
  }
  const day = utc ? moment.utc(date) : moment(date);
  switch (format) {
    case DateFormat.Humanize:
      return day.fromNow();
    default:
      return day.format(format);
  }
};


export const getBackwardWeekDates = (endDate: Date, startDate: Date) => {

  // end Date must be later
  if (endDate <= startDate) {
    return [];
  }

  const arr = [];
  const date = new Date(endDate);
  for (; date >= startDate; date.setDate(date.getDate() - 7)) {
    arr.push(new Date(date));
  }
  return arr;
};

export const isBefore = (value: Date | string, date?: Date | string) => {
  return moment(date).isBefore(value);
};

export const isAfter = (value: Date | string, date?: Date | string) => {
  return moment(date).isAfter(value);
};

export const getDiffAgo = (date: moment.MomentInput) => {
  const diffMonths = moment().diff(date, "months")
  return diffMonths ? `${diffMonths}mo ago` : `${moment().diff(date, "days")}d ago`
}

export const hasChange = (date: string | Date | undefined, comparedDate: string | Date | undefined) => {
  if (typeof date === 'object' && typeof comparedDate === 'object') {
    return date.toString() !== comparedDate.toString();
  }
  return date !== comparedDate;
};

const MONTHS = {
  Jan: 'January',
  Feb: 'February',
  Mar: 'March',
  Apr: 'April',
  May: 'May',
  Jun: 'June',
  Jul: 'July',
  Aug: 'August',
  Sep: 'September',
  Oct: 'October',
  Nov: 'November',
  Dec: 'December',
};

export const getMonthFromAbbreviation = (abbreviation: string): string | undefined =>
  MONTHS[abbreviation as keyof typeof MONTHS];

export const getCurrentDateStr = (format = DateFormat.DefaultDashes) => moment().format(format);

export const getCurrentDate = () => {
  return new Date();
}

export const getFinancialMonths = () => {
  const months = Object.values(MONTHS).map((month) => month.toLowerCase());
  months.unshift('');
  return months;
};

// If now Feb, return [january, december, november, october]
export const getLastNumberOfMonths = (numberOfMonths = 4) => {
  const months = [];
  for (let number = 1; number <= numberOfMonths; number++) {
    months.push(moment().utc().subtract(number, 'months').format(DateFormat.FullMonthName).toLowerCase());
  }
  return months;
};

export const subtractDate = (date: string | Date, value: number, unit: DurationInputArg2) => {
  return moment.utc(date).subtract(value, unit).toDate();
}

export const addDate = (date: string | Date | number, value: number, unit: DurationInputArg2) => {
  return moment.utc(date).add(value, unit).toDate();
}

export const getUTCEndOf = (unit: unitOfTime.StartOf, date?: DateString) => {
  return moment.utc(date).endOf(unit).toDate();
}

export const setUTCEndOf = ({
  year,
  month,
  day = 1,
  unit = 'month',
}: {
  year: number;
  month: number;
  day?: number;
  unit?: unitOfTime.StartOf;
}) => {
  return moment.utc([year, month, day]).endOf(unit).toDate();
};

export const getYear = (date?: DateString) => {
  return moment.utc(date).year();
};

export const toTimestamp = (date: Date | string) => moment.utc(date, false).unix();
export const timestampToISOString = (date: number) => moment.unix(date).toISOString();

/**
 * Notification sent on: (*Deadline is org financial end date plus 4 months)
 *
 * Deadline minus 3 months
 * Deadline minus 1 month
 * Deadline minus 7 days
 */
export const getFinancialEndDateDeadline = (deadline: Date) => {

  const today = moment().utc();
  if (moment(deadline).utc().subtract(7, 'days').isSame(today, 'day')) {
    return 'seven days';
  }
  if (moment(deadline).utc().subtract(1, 'months').isSame(today, 'day')) {
    return 'one month';
  }
  if (moment(deadline).utc().subtract(3, 'months').isSame(today, 'day')) {
    return 'three months';
  }
};

type LikeDate = string | Date;
export const isSame = (firstDate: LikeDate, secondDate: LikeDate, unit: DurationInputArg2) => {
  return moment(firstDate).isSame(secondDate, unit)
}

export const getDuration = (diff: number, unit?: DurationInputArg2) => {
  return moment.duration(diff, (unit ?? 'milliseconds'))
}

export const isBeforeNow = (value: moment.MomentInput) => moment(value).isBefore();

export const getDiff = (date1: number, date2: moment.MomentInput) => {
  return moment.unix(date1).diff(date2, 'days')
};

export const getDiffInUnit = (date1: moment.MomentInput, date2: moment.MomentInput, unit: DurationInputArg2) => {
  return moment(date1).diff(date2, unit)
}

export const getFutureDate = (duration: DurationInputArg1, unit: DurationInputArg2 = 'day') => moment().add(duration, unit).toDate();

export const getPastDate = (duration: DurationInputArg1, unit: DurationInputArg2 = 'day') => moment().subtract(duration, unit).toDate();

export const projectDate = ({
  field,
  startDate,
  endDate,
}: {
  field: 'effectiveDate' | 'created' | 'completedDate';
  startDate: string | Date | undefined;
  endDate: string | Date | undefined;
}) => {
  if (!startDate && !endDate) {
    return undefined;
  }
  return {
    [field]: {
      ...(startDate && { $gte: new Date(startDate) }),
      ...(endDate && { $lte: new Date(endDate) }),
    },
  };
};

export const getStartOfMonth = (date: Date | string) => {
  return moment(date).startOf('month').startOf('day').toDate();
}

export enum TimePeriod {
  Hourly = 'hourly',
  Daily = 'daily',
  Weekly = 'weekly',
  Monthly = 'monthly',
  Yearly = 'yearly'
}

export const TimePeriodUnitMapping = {
  [TimePeriod.Hourly]: 'hour',
  [TimePeriod.Daily]: 'day',
  [TimePeriod.Weekly]: 'week',
  [TimePeriod.Monthly]: 'month',
  [TimePeriod.Yearly]: 'year'
} satisfies Record<TimePeriod, unitOfTime.StartOf>;

export const getTimeRangeByPeriod = (period: TimePeriod) => {
  return {
    startTime: moment().utc().startOf(TimePeriodUnitMapping[period]).toDate(),
    endTime: moment().utc().endOf(TimePeriodUnitMapping[period]).toDate()
  }
}

export const revertFormattedToDate = (date: string, format: DateFormat | string): Date => {
  return moment(date, format).toDate();
};

export const generateDateMatch = (date: string | Date) => {
  return {
    $gte: moment(date).startOf('month').toDate(),
    $lte: moment(date).endOf('month').toDate(),
  };
}

export const toDate = (date: string | Date) => {
  return moment(date).toDate();
}

export const sortDateFnAsc = (dateA: LikeDate, dateB: LikeDate) => (dateA < dateB ? 1 : dateA > dateB ? -1 : 0);

export const oneDayInSeconds = 3600 * 24;

export const getUnixSeconds = (timeInSeconds: number) => moment().unix() + timeInSeconds;

export const getDateRangeFromTimeFrame = (timeFrameType: TimeFrameType) => {
  switch (timeFrameType) {
    case TimeFrameType.TwelveMonths:
      return {
        startDate: subtractDate(new Date(), 12, 'months'),
        endDate: moment.utc().toDate(),
      };
    case TimeFrameType.SixMonths:
      return {
        startDate: subtractDate(new Date(), 6, 'months'),
        endDate: moment().utc().toDate(),
      };
    case TimeFrameType.ThreeMonths:
      return {
        startDate: subtractDate(new Date(), 3, 'months'),
        endDate: moment().utc().toDate(),
      };
    case TimeFrameType.OneMonth:
      return {
        startDate: subtractDate(new Date(), 1, 'months'),
        endDate: moment().utc().toDate(),
      };
    case TimeFrameType.AllTime:
    case TimeFrameType.Custom:
      return undefined;
  }
}