/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { model, Model, Schema, Types } from 'mongoose';
import { UnitConfig, UnitConfigSchema } from '../service/units/unitTypes';
import { FeatureDetails } from '@g17eco/core';
import { FeatureDetailSchema } from './featureDetailSchema';

export interface SurveyConfig {
  subsidiariesEnforced: boolean;
  evidenceRequired: boolean;
  noteRequired?: boolean;
  verificationRequired: boolean;
  isPrivate: boolean;
  unitConfig: UnitConfig;
}

const SurveyConfigSchema = new Schema<SurveyConfig>(
  {
    subsidiariesEnforced: { type: Boolean, default: false, required: true },
    evidenceRequired: { type: Boolean, default: true, required: true },
    noteRequired: { type: Boolean, default: false },
    verificationRequired: { type: Boolean, default: true, required: true },
    isPrivate: { type: Boolean, default: false, required: true },
    unitConfig: UnitConfigSchema,
  },
  { _id: false }
);

export enum FeatureSettingType {
  Default = 'default',
  InitiativeSetting = 'initiative_setting',
}

export interface FeatureSetting extends FeatureDetails {
  type: FeatureSettingType.InitiativeSetting
}

const InitiativeSettingsSchema = new Schema<InitiativeSettingsModel>(
  {
    initiativeId: {
      type: Schema.Types.ObjectId,
      required: true,
      unique: true,
    },
    surveyConfig: { type: SurveyConfigSchema, require: false },
    features: {
      type: [FeatureDetailSchema],
      default: undefined,
    },
  },
  { collection: 'initiative-settings' }
);

InitiativeSettingsSchema.index({ initiativeId: 1 });

export interface InitiativeSettingsModel {
  _id?: Types.ObjectId;
  initiativeId: Types.ObjectId;
  surveyConfig?: SurveyConfig;
  features?: FeatureDetails[];
}

const InitiativeSettings: Model<InitiativeSettingsModel> = model('InitiativeSettings', InitiativeSettingsSchema);
export default InitiativeSettings;
