/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { Model, model, Schema } from 'mongoose';
import {
  getDefaultMagicBell,
  NotificationPreferencesModel,
} from '../service/notification/NotificationModels';
import { TimePeriod } from '../util/date';

const NotificationScheduleSchema = new Schema({
  isSummary: {
    type: Schema.Types.Boolean,
    default: false,
  },
  period: {
    type: Schema.Types.String,
    enum: Object.values(TimePeriod),
    default: TimePeriod.Weekly
  }
}, { _id: false });

const MagicBellPreferencesSchema = new Schema({
  notification_preferences: {
    categories: {
      type: Schema.Types.Mixed
    }
  },
}, { _id: false });

const NotificationPreferencesSchema = new Schema<NotificationPreferencesModel>({
  // Ownership
  userId: { type: Schema.Types.ObjectId, unique: true, required: true },
  magicBell: {
    type: MagicBellPreferencesSchema,
    required: true,
    default: getDefaultMagicBell()
  },
  notificationSchedule: {
    type: NotificationScheduleSchema,
    require: false
  },
  // General info
  created: { type: Date, default: Date.now },
}, { collection: 'notification-preferences' });

NotificationPreferencesSchema.index({ userId: 1 });

NotificationPreferencesSchema.virtual('user', {
  ref: 'User', // The model to use
  localField: 'userId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

const NotificationPreferences: Model<NotificationPreferencesModel> = model<NotificationPreferencesModel>('NotificationPreferences', NotificationPreferencesSchema);

export default NotificationPreferences;
