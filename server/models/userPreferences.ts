/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, InferSchemaType, model, Schema } from 'mongoose';
import { ObjectId } from 'bson';


enum UserFeatureCode {
  UserApiKey = 'user_api_key',
}

interface UserFeature {
  code: UserFeatureCode;
  name: string;
  description?: string;
  enabled: boolean;
}

// Features
export const userFeatures = [
  {
    code: UserFeatureCode.UserApiKey,
    name: 'User API Key',
    description: 'User API Key allow to interact and authenticate with public API',
    enabled: true,
  },
] satisfies UserFeature[]


// Feature flags
interface ActiveFeature {
  code: UserFeatureCode;
  created: Date;
}

const featureFlags = new Schema<ActiveFeature>({
  code: { type: Schema.Types.String, required: true, enum: Object.values(UserFeatureCode) },
  created: { type: Schema.Types.Date, default: Date.now },
}, { _id: false })

interface UserPreferencesPlain {
  _id: Object;
  userId: ObjectId;
  created: Date;
  featureFlags: ActiveFeature[];
}

const UserPreferencesSchema = new Schema<UserPreferencesPlain>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      required: true,
      unique: true
    },
    created: { type: Schema.Types.Date, default: Date.now },
    featureFlags: {
      type: [featureFlags],
      required: true,
      default: [],
    },
  },
  { collection: 'user-preferences' }
);

UserPreferencesSchema.index({ userId: 1 });
export type UserPreferencesModel = HydratedDocument<InferSchemaType<typeof UserPreferencesSchema>>;


const UserPreferences = model('UserPreferences', UserPreferencesSchema);

export default UserPreferences;
