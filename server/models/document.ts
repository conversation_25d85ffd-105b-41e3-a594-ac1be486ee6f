/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Types, model, Schema, HydratedDocument } from 'mongoose';


export enum DocumentType {
  Link = 'link',
  File = 'file',
}

export enum DocumentOwnerType {
  Initiative = 'initiative',
  User = 'user',
  Organization = 'organization',
  UniversalTrackerValue = 'universalTrackerValue',
  Survey = 'survey',
  InsightDashboard = 'insightDashboard',
}

export enum DocumentOwnerSubType {
  InsightDashboardItem = 'insightDashboardItem',
}

export enum DocumentSubType {
  Spreadsheet = 'spreadsheet',
  Guidelines = 'guidelines',
  SustainabilityReport = 'sustainability-report',
  AssuranceDocument = 'assurance-document',
  Image = 'image',
  Other = 'other',
}

interface Metadata {
  name: string;
  mimetype: string;
  extension: string;
  exif?: any;
}

export interface DocumentPlain {
  _id: Types.ObjectId;
  path: string;
  userId: Types.ObjectId | string;
  metadata?: Metadata;
  size?: number;
  type: DocumentType;
  public: boolean;
  title?: string;
  description?: string;
  ownerId?: Types.ObjectId;
  ownerType?: DocumentOwnerType;
  ownerSubType?: string | DocumentOwnerSubType | DocumentSubType;
  url?: string;
  checksum?: string;
  created?: Date;
}

export type DocumentModel = HydratedDocument<DocumentPlain>

export interface LinkDocument {
  title?: string;
  description?: string;
  link: string;
  public?: boolean;
}

const DocumentSchema = new Schema<DocumentPlain>({
  title: {
    type: Schema.Types.String,
    default: function (this: DocumentPlain) {
      if (!this.title) {
        return this.metadata?.name;
      }
      return this.title;
    },
  },
  description: String,
  created: {type: Date, default: Date.now},
  metadata: {
    name: {type: String, default: ''},
    mimetype: {type: String, default: ''},
    extension: { type: String, default: '' },
    exif: {
      GPSLatitudeRef: Schema.Types.String,
      GPSLatitude: Schema.Types.Number,
      GPSLongitudeRef: Schema.Types.String,
      GPSLongitude: Schema.Types.Number
    }
  },
  public: { type: Schema.Types.Boolean, default: true },
  type: {
    type: Schema.Types.String,
    enum: [DocumentType.File, DocumentType.Link],
    default: DocumentType.File,
  },
  path: String,
  userId: Schema.Types.ObjectId,
  size: Schema.Types.Number,
  ownerId: Schema.Types.ObjectId,
  checksum: Schema.Types.String,
  ownerType: {
    type: Schema.Types.String,
    enum: Object.values(DocumentOwnerType),
  },
  ownerSubType: Schema.Types.String,
}, {collection: 'document'});

DocumentSchema.index({ ownerId: 1 }, { unique: false });

const documentModel = model('DocumentFile', DocumentSchema);


export default documentModel;
