/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { Model, model, Schema, HydratedDocument } from 'mongoose';
import { ObjectId } from 'bson';
import UniversalTracker from './universalTracker';
import { MaterialityAssessmentType } from './materialityMetric';
import { MaterialityAssessmentScope } from '../service/materiality-assessment/types';

const g17ecoCode = {
  type: Schema.Types.String,
  trim: true,
  lowercase: true,
  required: true,
  validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/)'] as [
    RegExp,
    string
  ],
};

const g17ecoCodeUnique = {
  ...g17ecoCode,
  unique: true,
};

interface MaterialTopicBase {
  name: string;
  code: string;
}

export interface UtrMapping {
  code: string;
  name?: string;
  score: number;
}

export enum ESGCategory {
  Environmental = 'Environmental',
  Social = 'Social',
  Governance = 'Governance',
}

export enum MaterialPillar {
  People = 'people',
  Partnership = 'partnership',
  Planet = 'planet',
  Prosperity = 'prosperity',
  Principle = 'principle',
}

export enum MaterialityBoundary {
  Leadership = 'leadership',
  ResearchAndDevelopment = 'research-and-development',
  SupplyChain = 'supply-chain',
  ProductAndServices = 'product-and-services',
  Distribution = 'distribution',
  Communities = 'communities',
  Experiences = 'experiences',
}

export interface MaterialTopicCategories {
  esg?: ESGCategory[];
  sdg?: string[];
  materialPillar?: MaterialPillar[];
  boundary?: MaterialityBoundary[];
}

export interface MaterialTopicPlain<D = Date> extends MaterialTopicBase {
  created: D;
  description?: string;
  action?: string;
  verifiedDate?: D;
  mappedDate?: D;
  types?: MaterialityAssessmentType[];
  utrMapping?: UtrMapping[];
  categories?: MaterialTopicCategories;
  scopeScores?: {
    scope: string;
    maxScore: number;
    referenceCount: number;
  }[];
}

type MaterialTopicModel<T = ObjectId> = HydratedDocument<MaterialTopicPlain>;

const UtrMappingSchema = new Schema(
  {
    code: g17ecoCode,
    score: {
      type: Schema.Types.Number,
      required: true,
    },
  },
  { _id: false }
);

const CategoriesSchema = new Schema(
  {
    esg: {
      type: [Schema.Types.String],
      enum: ESGCategory,
      required: false,
      default: undefined,
    },
    sdg: {
      type: [Schema.Types.String],
      required: false,
      default: undefined,
    },
    materialPillar: {
      type: [Schema.Types.String],
      enum: MaterialPillar,
      required: false,
      default: undefined,
    },
    boundary: {
      type: [Schema.Types.String],
      enum: MaterialityBoundary,
      required: false,
      default: undefined,
    },
  },
  { _id: false }
);

const ScopeScoresSchema = new Schema(
  {
    scope: {
      type: Schema.Types.String,
      enum: Object.values(MaterialityAssessmentScope),
      required: true,
    },
    maxScore: {
      type: Schema.Types.Number,
      required: true,
    },
    referenceCount: {
      type: Schema.Types.Number,
      required: true,
    },
  },
  { _id: false },
);

export const MaterialTopicSchema = new Schema<MaterialTopicModel>(
  {
    code: g17ecoCodeUnique,
    name: { type: Schema.Types.String, trim: true, required: true },
    description: { type: Schema.Types.String, trim: true, required: false },
    action: { type: Schema.Types.String, trim: true, required: false },
    created: { type: Date, default: Date.now },
    verifiedDate: { type: Date },
    mappedDate: { type: Date },
    scopeScores: {
      type: [ScopeScoresSchema],
      required: false,
    },
    types: {
      type: [Schema.Types.String],
      enum: Object.values(MaterialityAssessmentType),
      trim: true,
      required: false,
      default: undefined,
    },
    utrMapping: { type: [UtrMappingSchema] },
    categories: { type: CategoriesSchema, required: false },
  },
  { collection: 'material-topics' }
);

MaterialTopicSchema.virtual('utr', {
  ref: UniversalTracker.name,
  localField: 'utrMapping.code',
  foreignField: 'code',
  justOne: true,
  as: 'utrMapping.utr',
});

const MaterialTopic: Model<MaterialTopicModel> = model('MaterialTopic', MaterialTopicSchema);
export default MaterialTopic;
