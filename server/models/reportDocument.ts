import { Schema, model, Types, HydratedDocument } from 'mongoose';

export enum ReportDocumentTemplate {
  /** Empty state **/
  Blank = 'blank',
  /** Allow AI to Generate the initial template state **/
  AiGenerated = 'ai_generated',
}

interface ReportDocumentConfig {
  type: ReportDocumentTemplate;
}

const configSchema = new Schema<ReportDocumentConfig>({
  type: {
    type: Schema.Types.String,
    required: false,
    trim: true,
    enum: Object.values(ReportDocumentTemplate),
    default: ReportDocumentTemplate.Blank
  },
}, { _id: false });

export interface ReportDocumentPlain {
  _id: Types.ObjectId;
  title: string;
  description?: string;
  type: string;

  config?: ReportDocumentConfig;

  initiativeId: Types.ObjectId;
  createdBy: Types.ObjectId
  created: Date;
  lastUpdated: Date;
}

export type CreateReportDocument = Omit<ReportDocumentPlain, '_id' | 'created' | 'lastUpdated'>;

export type ReportDocumentModel = HydratedDocument<ReportDocumentPlain>;

export const ReportDocumentTypes = ['csrd'] as const;

const ReportDocumentSchema = new Schema<ReportDocumentPlain>({
  title: { type: Schema.Types.String, required: true },
  description: { type: Schema.Types.String, required: false, trim: true },
  type: { type: Schema.Types.String, required: true, enum: ReportDocumentTypes },
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  config: { type: configSchema },
  createdBy: { type: Schema.Types.ObjectId, required: true },
}, {
  collection: 'report-documents',
  timestamps: {
    createdAt: 'created',
    updatedAt: 'lastUpdated',
  }
});

const ReportDocument = model('ReportDocument', ReportDocumentSchema);

export default ReportDocument;
