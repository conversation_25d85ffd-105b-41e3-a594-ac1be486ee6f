/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, model, Schema, Types } from 'mongoose';
import { ObjectId } from 'bson';
import { valueChainCategories } from '../util/valueChain';
import ICBMaterialityData from './icbMaterialityData';
import { getIndustryText } from '../service/reporting/FrameworkMapping';
import { InitiativeGroupModel } from './initiativeGroup';
import { FeatureTag, RequiredTags } from '@g17eco/core';
import { AddressPlain } from './organization'
import { Materiality } from "./materiality";
import { DataScope, DataScopeSchema } from './dataShare';
import { UserSendNotificationData } from './user';
import { BankingSetting } from '../service/banking/BankingService';
import { Subscription } from './customer';

export const usageOptions = [
  'initiative_structure',
  'menu_dropdowns',
  'menu_summary',
  'menu_impact_performance',
  'menu_universal_trackers_disaggregations',
  'menu_universal_trackers',
  'menu_pitchbook',
  'menu_frameworks',
  'menu_financial_performance',
  'menu_intelligence',
  'menu_documents_and_media',
  'menu_ecosystem_partners',
  'menu_survey_workflow',
  'menu_sdg_contributions',
];

export const DEFAULT_USAGE = ['initiative_structure', 'menu_dropdowns', 'menu_summary', 'menu_impact_performance'];

export const PERMISSION_GROUPS = {
  FREE: 'free',

  COMPANY_TRACKER_STARTER: 'company_tracker_starter',
  COMPANY_TRACKER_LIGHT: 'company_tracker_light',
  COMPANY_TRACKER_PRO: 'company_tracker_pro',
  COMPANY_TRACKER_ENTERPRISE: 'company_tracker_enterprise',
  COMPANY_TRACKER_ENTERPRISE_AND_PORTFOLIO_TRACKER: 'premium_unlimited_portfolio_tracker',

  PORTFOLIO_TRACKER: 'portfolio_tracker',
  PORTFOLIO_TRACKER_EXCHANGE: 'portfolio_tracker_exchange',

  // LEGACY
  COMPANY_TRACKER_ENTERPRISE_OLD1: 'premium_unlimited',
  COMPANY_TRACKER_PRO_OLD1: 'premium_trial_limit1',
  COMPANY_TRACKER_PRO_OLD2: 'premium_limit5',
  COMPANY_TRACKER_PRO_OLD3: 'premium_limit20',
}

export const LegacyPermissionGroups = [
  PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE_OLD1,
  PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD1,
  PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD2,
  PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD3,
];

export const CompanyTrackerProIds = [
  PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD1,
  PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD2,
  PERMISSION_GROUPS.COMPANY_TRACKER_PRO_OLD3,
  PERMISSION_GROUPS.COMPANY_TRACKER_PRO,
] as const;

export const CompanyTrackerEnterpriseIds = [
  PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE,
  PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE_OLD1,
  PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE_AND_PORTFOLIO_TRACKER,
] as const;

export const CompanyTrackerStarterIds = [
  PERMISSION_GROUPS.COMPANY_TRACKER_STARTER,
] as const;

export const PortfolioTrackerIds = [
  PERMISSION_GROUPS.PORTFOLIO_TRACKER,
  PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE,
] as const;

export const minimumFields = {
  _id: 1,
  name: 1,
  code: 1,
  created: 1,
  parentId: 1,
  startDate: 1,
  endDate: 1,
  usage: 1,
  profile: 1,
  tags: 1,
  type: 1,
  industry: 1,
  permissionGroup: 1,
  initiativeGroupId: 1,
};

export enum InitiativeTags {
  VerifiedOrganization = 'verified_organization',
  StaffOrganization = 'staff_organization',
  DemoOrganization = 'demo_organization',
  Organization = 'organization',
  Country = 'country',
}

export const betaFeatureTags = [
  FeatureTag.MetricAssistantAI,
  FeatureTag.DraftFurtherExplanationAI,
  FeatureTag.SDGInsightAI,
  FeatureTag.PPTXReportAI,
] as const;

const requiredTagsStandards = Object.values(RequiredTags);
const featureTags = Object.values(FeatureTag);

export enum InitiativeTypes {
  Group = 'initiativeGroup',
  Initiative = 'initiative',
}

export const AvailableTags = [
  InitiativeTags.StaffOrganization,
  InitiativeTags.VerifiedOrganization,
  InitiativeTags.Organization,
  InitiativeTags.DemoOrganization,
  InitiativeTags.Country,
  ...featureTags,
  ...requiredTagsStandards,
  ...valueChainCategories,
];

export const TagGroups = [
  { name: 'Organization', options: Object.values(InitiativeTags) },
  { name: 'Features', options: [...featureTags] },
  { name: 'Standards Scope', options: [...requiredTagsStandards] },
  { name: 'Value Chain', options: [...valueChainCategories] },
];

export enum RequestReviewMethod {
  Review = 'review',
  Automate = 'automate',
  NoAccess = 'noAccess',
}

const RatingsSchema = new Schema({
  code: { type: Schema.Types.String, required: true },
  rating: { type: Schema.Types.String, required: true },
  link: Schema.Types.String,
  linkText: Schema.Types.String,
  date: Schema.Types.Date,
},
  { _id: true });

const DisplayItemSchema = new Schema<DisplayItem>({
  view: String,
}, { _id: false })

const LiveSharingSchema = new Schema<LiveSharing>({
  liveSharing: Boolean,
  token: String,
}, { _id: false })

const DisplaySettingsSchema = new Schema<DisplaySettings>({
  targetActual: {
    type: DisplayItemSchema,
    required: false
  },
  sdgContributionChart: {
    type: LiveSharingSchema,
    required: false
  },
}, { _id: false });

const ReferralSchema = new Schema({
  code: { type: Schema.Types.String },
  referrer: { type: Schema.Types.String },
  usedDate: { type: Schema.Types.Date },
}, { _id: false });

const levelSchema = new Schema({
  level1: String,
  level2: String,
  level3: String,
  level4: String,
},
  { _id: false }
);

const LinkedUniversalTrackerSchema = new Schema({
  universalTrackerId: {
    type: Schema.Types.ObjectId,
    required: true
  },
  usage: [{ type: String }]
},
  { _id: false });

const MaterialityMapSchema = new Schema({
  high: {
    type: [Schema.Types.String],
  },
  medium: {
    type: [Schema.Types.String],
  },
  low: {
    type: [Schema.Types.String],
  },
  none: {
    type: [Schema.Types.String],
  }
}, { _id: false })

const MonthDaySchema = new Schema(
  {
    month: {
      type: Schema.Types.String,
      required: true,
    },
    day: {
      type: Schema.Types.String,
      required: true,
    },
  },
  { _id: false }
);

export enum CompanyAgreement {
  CompanyTrackerServicesAgreement = 'CompanyTrackerServicesAgreement'
}

const AgreementSchema = new Schema({
  [CompanyAgreement.CompanyTrackerServicesAgreement]: {
    type: new Schema({
      date: {
        type: Date,
        default: Date.now
      },
      userId: Types.ObjectId
    }, { _id: false }),
    required: false
  }
}, { _id: false });

const MetadataSchema = new Schema(
  {
    sgx_issuer_name: {
      type: Schema.Types.String,
      required: false,
      trim: true,
      set: function (value: string) {
        return value === '' ? undefined : value;
      }
    },
    sgx_ric: {
      type: Schema.Types.String,
      required: false,

      trim: true,
      set: function (value: string) {
        return value === '' ? undefined : value;
      }
    },
    sgx_code: {
      type: Schema.Types.String,
      required: false,
      trim: true,
      set: function (value: string) {
        return value === '' ? undefined : value;
      }
    },
    agreements: {
      type: AgreementSchema,
      required: false,
    },
  },
  { _id: false }
);

// Represent stripe customer
export interface InitiativeCustomer {
  id: string;
  currency?: string;
  defaultPaymentMethod?: string;
  subscriptions?: Subscription[];
}

export const initiativeCustomerSchema = new Schema<InitiativeCustomer>({
  id: { type: Schema.Types.String, required: true, trim: true },
  defaultPaymentMethod: { type: Schema.Types.String },
  currency: { type: Schema.Types.String },
}, { _id: false });

const BankingSettingSchema = new Schema({
  name: { type: Schema.Types.String },
  code: { type: Schema.Types.String, require: true },
  type: { type: Schema.Types.String },
}, { _id: false });

export const InitiativeSchema = new Schema<InitiativePlain>({
  managedBy: [Schema.Types.ObjectId],
  code: {
    type: String,
    trim: true,
    lowercase: true,
    required: true,
    unique: true,
    validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) ']
  },
  name: String,
  type: {
    type: String,
    enum: [InitiativeTypes.Initiative, InitiativeTypes.Group],
    default: InitiativeTypes.Initiative
  },
  initiativeGroupId: { type: Schema.Types.ObjectId, required: false },
  oktaGroupIds: [Schema.Types.String],
  description: String,
  missionStatement: String,
  startDate: Date,
  endDate: Date,
  deletedDate: Date,
  geoLocation: String,
  sdgImpact: [{
    type: Number,
    min: 1,
    max: 17
  }], // Deprecated
  materialityMap: MaterialityMapSchema,
  tags: {
    type: [Schema.Types.String],
    enum: AvailableTags,
    required: false,
    validate: function () {
      // Validate value chain uniqueness
      return true;
    },
  },
  visible: { type: Boolean, default: true },
  /* @deprecated */
  isPublic: { type: Boolean, default: false },
  dataShare: {
    dataScope: DataScopeSchema,
    requestReview: {
      type: Schema.Types.String,
      enum: RequestReviewMethod,
      required: false,
      default: RequestReviewMethod.Review
    }
  },
  profile: String,
  ratings: { type: [RatingsSchema], default: [] },
  parentId: {
    type: Schema.Types.ObjectId,
    validate: {
      validator: function (this: InitiativeModel, value?: string | ObjectId) {
        return String(this._id) !== String(value);
      },
      message: (props: { value?: string }) => {
        return `id ${props.value} cannot be the parent of it self!`;
      }
    },
  },
  organizationId: Schema.Types.ObjectId,
  permissionGroup: {
    type: String,
    enum: Object.values(PERMISSION_GROUPS),
    default: 'free'
  },
  usage: {
    type: [Schema.Types.String],
    enum: usageOptions,
  },
  industry: new Schema({
    gics: levelSchema,
    icb: levelSchema,
    icb2019: levelSchema,
  },
    { _id: false }
  ),
  customer: { type: initiativeCustomerSchema, required: false },
  country: Schema.Types.String,
  appConfigCode: Schema.Types.String,
  referrals: { type: [ReferralSchema], default: [] },
  displaySettings: DisplaySettingsSchema,
  linkedUniversalTrackers: [LinkedUniversalTrackerSchema],
  financialEndDate: { type: MonthDaySchema, required: false },
  created: { type: Date, default: Date.now },
  metadata: { type: MetadataSchema, required: false },
  bankingSettings: { type: [BankingSettingSchema] },
}, { collection: 'initiatives', toJSON: { virtuals: true } });

export interface LinkedUniversalTrackerModel<T = Types.ObjectId> {
  universalTrackerId: T;
  usage: string[];
}

interface DisplayItem {
  view: string;
}

interface LiveSharing {
  liveSharing: boolean;
  token?: string;
}

export interface DisplaySettings {
  targetActual?: DisplayItem;
  sdgContributionChart?: LiveSharing
}

export interface IndustryLevels {
  [key: string]: string;
  level1: string;
  level2: string;
  level3: string;
  level4: string;
}

export interface Industry {
  [key: string]: IndustryLevels;
  gics: IndustryLevels;
  icb: IndustryLevels;
  icb2019: IndustryLevels;
}

export interface Referral {
  code?: string;
  referrer?: string;
  usedDate?: string | Date;
}

export interface MonthDay {
  month: string;
  day: string;
}

export interface InitiativeMetadata {
  sgx_issuer_name?: string;
  sgx_ric?: string;
  sgx_code?: string;
  agreements?: {
    [key: string]: {
      date: Date;
      userId: ObjectId;
    }
  }
}

export interface CreateInitiativeData<T = Types.ObjectId> {
  name: string;
  code: string;
  parentId?: T;
  startDate?: string;
  endDate?: string;
  type?: string;
  tags?: string[];
  usage?: string[];
  permissionGroup?: string;
  industry?: Industry;
  country?: string;
  referrals?: Referral[];
  address?: AddressPlain;
  registrationNumber?: string;
  managedBy?: ObjectId[];
  financialEndDate?: MonthDay;
  metadata?: InitiativeMetadata;
  appConfigCode?: string;
}

export interface InitiativeDataShare {
  dataScope?: DataScope,
  requestReview?: RequestReviewMethod
}

interface InitiativeCommon<T = Types.ObjectId> {
  _id: T;
  name: string;
  code: string;
  initiativeGroupId?: T;
  oktaGroupIds?: string[];
  profile?: string;
  industry?: Industry;
  sdgImpact?: number[]; // Deprecated
  materialityMap?: MaterialityMap;
  description?: string;
  missionStatement?: string;
  geoLocation?: string;

  startDate?: Date;
  endDate?: Date;
  files?: any[];
  materiality?: number[];
  sectorText?: string;
  industryText?: string;
  permissionGroup?: string;
  root?: RootInitiative;
  created: Date;
  bankingSettings?: BankingSetting[];

  /** @deprecated **/
  visible?: boolean;
  /** @deprecated **/
  isPublic?: boolean;

  organizationId?: T;

  financialEndDate?: MonthDay;

  parentId?: T;
  type: string;
  tags?: string[];
  /** Code to a single config based on initiative permission group **/
  appConfigCode?: string
  usage: string[];
  linkedUniversalTrackers: LinkedUniversalTrackerModel[];
  displaySettings?: DisplaySettings;
  children?: InitiativePlain[];
  ratings?: InitiativeRating<T>[];
  customer?: InitiativeCustomer;
  referrals?: Referral[];
  country?: string;
  metadata?: InitiativeMetadata;
  dataShare?: InitiativeDataShare;

  managedBy?: T[];
  deletedDate?: Date;
}

type BaseFinancialYearInitiativeProps = '_id' | 'code' | 'name' | 'tags' | 'appConfigCode';
export interface InitiativeWithFinancialEndDate<T = Types.ObjectId> extends Pick<InitiativeCommon<T>, BaseFinancialYearInitiativeProps> {
  notificationStartDate: Date;
  notificationEndDate: Date;
  financialEndDate: MonthDay;
  users: UserSendNotificationData[],
}

export interface InitiativeRating<T = Types.ObjectId> {
  _id: T;
  code: string;
  rating: string;
  link?: string;
  linkText?: string;
  date?: string;
}

export interface MaterialityMap {
  high: string[],
  medium: string[],
  low: string[],
  none: string[],
}

type RootInitiative = Pick<InitiativePlain, '_id' | 'name' | 'code' | 'profile'>;
export interface InitiativePlainWithRoot<T = Types.ObjectId> extends InitiativePlain<T> {
  root?: RootInitiative;
}

export interface InitiativePlainWithParents<T = Types.ObjectId> extends InitiativePlain<T> {
  parents: InitiativePlain[];
}

export interface InitiativePlain<T = Types.ObjectId> extends InitiativeCommon<T> {
  parents?: InitiativePlain[];
}

// In mongoose@8 these now align, so as first step, we can keep the references but start aligning
export type InitiativeModel<T = Types.ObjectId> = HydratedDocument<InitiativePlain<T>>;

const Initiative = model('Initiative', InitiativeSchema);

export interface InitiativeModelChildren extends InitiativePlain {
  children: InitiativePlain[];
}

export interface InitiativeWithCustomer extends InitiativeModel {
  customer: InitiativeCustomer;
}

export interface InitiativeModelChildrenGroups extends InitiativePlain {
  initiativeGroup: InitiativeGroupModel;
}

InitiativeSchema.virtual('materiality').get(function (this: InitiativeModel) {
  return getMateriality(this.industry);
});

InitiativeSchema.virtual('surveys', {
  ref: 'Survey', // The model to use
  localField: '_id', // Find people where `localField`
  foreignField: 'initiativeId', // is equal to `foreignField`
  justOne: false,
});


type MaterialityTypeValue = Record<string, number>;
const isMaterialityMap = (materialityMap: MaterialityMap | undefined | null): materialityMap is MaterialityMap => {
  return Boolean(materialityMap && Object.values(materialityMap).flat().length > 0);
};

export const hasMaterialityMap = ({ materialityMap }: { materialityMap?: MaterialityMap }): boolean => {
  return isMaterialityMap(materialityMap);
};

const getSdgUtrMateriality = function (materialityData: Record<string, number>, sdgCode: number) {
  if (materialityData?.[sdgCode] > 66) {
    return Materiality.High;
  } else if (materialityData?.[sdgCode] > 33) {
    return Materiality.Medium
  } else if (materialityData?.[sdgCode] > 0) {
    return Materiality.Low
  }
  return Materiality.None;
};

export const getMaterialityMap = (industry?: Industry) => {
  const map: MaterialityMap = {
    [Materiality.Low]: [],
    [Materiality.Medium]: [],
    [Materiality.High]: [],
    [Materiality.None]: [],
  }

  if (!industry) {
    return map;
  }
  const materialityData = getMateriality(industry)

  if (!materialityData) {
    return map;
  }
  [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17].forEach((sdgCode) => {
    if (sdgCode !== null) {
      const key = getSdgUtrMateriality(materialityData, sdgCode);
      map[key].push(`${sdgCode}`);
    }
  })

  return map
}

export const createEmptyMateriality = (): MaterialityTypeValue => {
  return Object.fromEntries(new Array(17)
    .fill(0)
    .map((v, i) => [String(i + 1), v]));
};

export function getMateriality(industry?: Industry, materialityMap?: MaterialityMap) {

  if (isMaterialityMap(materialityMap)) {
    const materiality = createEmptyMateriality();

    for (const key of materialityMap.high) {
      materiality[key] = 100
    }
    for (const key of materialityMap.medium) {
      materiality[key] = 66
    }
    for (const key of materialityMap.low) {
      materiality[key] = 33
    }
    for (const key of materialityMap.none) {
      materiality[key] = 0
    }
    return materiality
  }


  if (!industry || !industry.icb2019) {
    return undefined;
  }
  const icbCode = industry.icb2019.level3;
  return ICBMaterialityData.getByIdAsDictionaryValue(icbCode);
}

export const isOrganization = (initiative?: Pick<InitiativePlain, 'tags'>): boolean => {
  if (!initiative || !initiative.tags) {
    return false;
  }
  return initiative.tags.includes(InitiativeTags.Organization);
}

export const isStaffOrganization = (initiative?: { tags?: string[] }): boolean => {
  if (!initiative || !initiative.tags) {
    return false;
  }
  return initiative.tags.includes(InitiativeTags.StaffOrganization);
}

InitiativeSchema.virtual('industryText').get(function (this: InitiativeModel) {
  if (!this || !this.industry) {
    return '';
  }
  return getIndustryText(this.industry);
});

export const addReferral = (
  referrals: Referral[] | undefined,
  referral: Referral
): Referral[] => {

  if (!referrals) {
    return [referral]
  }

  if (referrals.every(r => r.code !== referral.code)) {
    referrals.push(referral)
  }

  return referrals;
}

InitiativeSchema.index({ parentId: 1 }, { unique: false });
InitiativeSchema.index({ created: 1 }, { unique: false });

export default Initiative;
