import { model, Schema } from 'mongoose';
import { ObjectId } from 'bson';
import { ValueStages } from '../service/aggregate/stages/ValueStage';
import { SupportedMeasureUnits, NumberScale, validUnitTypes } from '../service/units/unitTypes';

export enum CalculationType {
  Direct = 'direct', // 'a'
  Formula = 'formula', // {a}
  Stages = 'stages', // JSONata
}

export enum CalculationGroupValueType {
  Numeric = 'numeric',
  Text = 'text',
}

interface Variable {
  code: string; // align with utr portal
  valueListCode?: string;
  groupCode?: string;
  subGroupCode?: string;
  /**
   * Integration service provider code
   */
  integrationCode?: string;
}

interface CalculationBase {
  // Common properties for all types
  _id: ObjectId;
  name: string;
  description?: string;
  variables: Record<string, Variable>;

  type: CalculationType;
}

interface DirectCalculation extends CalculationBase {
  type: CalculationType.Direct;
  direct: string;
}

interface FormulaCalculation extends CalculationBase {
  type: CalculationType.Formula;
  formula: string;
}

interface StagesCalculation extends CalculationBase {
  type: CalculationType.Stages;
  stages: ValueStages;
}

export type Calculation = DirectCalculation | FormulaCalculation | StagesCalculation;

const CalculationSchema = new Schema<Calculation>(
  {
    name: { type: Schema.Types.String, required: true },
    description: { type: Schema.Types.String },
    variables: { type: Schema.Types.Mixed, required: true },
    type: { type: Schema.Types.String, enum: Object.values(CalculationType), required: true },
    direct: {
      type: Schema.Types.String,
      required: function (this: Calculation) {
        return this.type === CalculationType.Direct;
      },
    },
    formula: {
      type: Schema.Types.String,
      required: function (this: Calculation) {
        return this.type === CalculationType.Formula;
      },
    },
    stages: {
      type: Schema.Types.Mixed,
      required: function (this: Calculation) {
        return this.type === CalculationType.Stages;
      },
    },
  },
  { minimize: false }
);

export interface CalculationGroupPlain<Id = ObjectId, Created = Date> {
  _id: Id;
  /** Unique code for mapping and sync in different env */
  code: string;
  /** internal name like "Number of female employees" */
  name: string;
  /** Explain where it can be used */
  description?: string;
  valueType: CalculationGroupValueType;
  unitType?: SupportedMeasureUnits;
  numberScale?: NumberScale;
  calculations: Calculation[];
  created: Created;
  oktaId: string;
}

const CalculationGroupSchema = new Schema<CalculationGroupPlain>(
  {
    code: {
      type: Schema.Types.String,
      required: true,
      trim: true,
      lowercase: true,
      unique: true,
      validate: [/^[a-z0-9/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) '],
    },
    name: { type: Schema.Types.String, required: true },
    description: { type: Schema.Types.String },
    valueType: { type: Schema.Types.String, enum: Object.values(CalculationGroupValueType), required: true },
    unitType: {
      type: Schema.Types.String,
      enum: validUnitTypes,
      required: false,
      trim: true,
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    numberScale: {
      type: Schema.Types.String,
      required: false,
      enum: Object.values(NumberScale),
      trim: true,
      set: function (value: any) {
        return value === '' ? undefined : value;
      },
    },
    calculations: { type: [CalculationSchema], required: true },
    created: { type: Date, default: Date.now },
    oktaId: { type: Schema.Types.String, required: true },
  },
  { collection: 'calculation-groups' }
);

CalculationGroupSchema.index({ code: 1 });

export const CalculationGroup = model('CalculationGroup', CalculationGroupSchema);
