/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { <PERSON><PERSON>, ScopeSchema } from './common/scope';
import { HydratedDocument, model, Model, Schema, Types } from 'mongoose';
import { InitiativeMin } from './public/initiativeType';
import { RequesterDetails } from '../service/share/RequesterService';


export enum RestrictionType {
  Subscription = 'subscription',
  Sponsorship = 'sponsorship',
  Staff = 'staff',
  /**
   * We have restriction, but it's not enforced. Same as other types have expired.
   * endDate does do anything in this case. It direct opposite of Sponsorship type
   */
  NotRestricted = 'not_restricted',
}

interface RestrictionSubscriptionData {
  initiativeId: Types.ObjectId;
  subscriptionId: string;
}

interface RestrictionSponsorshipData {
  /** Reference to sponsorship object that cause this restriction */
  sponsorshipId: Types.ObjectId;
}

/** Details when staff applied enforced restriction */
interface RestrictionStaff {
  userId: Types.ObjectId;
}


/**
 * Reason why this share request is restricted.
 *
 * @example Organization is sponsored through subscription, hence some data access
 * is mandatory and cannot be revoked or managed in any way until the restriction
 * is lifted
 */
interface BaseRestriction<T extends RestrictionType, D = unknown> {
  type: T;

  /**
   * Allow to set end date of the restriction
   */
  endDate?: Date;

  /** Additional message for reason/context of this constrain */
  message?: string;

  /** Generic data based on the type value that allow to manage restriction */
  data: D;
}

// Union of all possible restriction types, should be added here as option
export type Restriction =
  | BaseRestriction<RestrictionType.Subscription, RestrictionSubscriptionData>
  | BaseRestriction<RestrictionType.Staff, RestrictionStaff>
  | BaseRestriction<RestrictionType.Sponsorship, RestrictionSponsorshipData>
  | BaseRestriction<RestrictionType.NotRestricted, RestrictionSponsorshipData>

/**
 * Different types of targets, like Portfolio (PT), Financial Organization,
 * Public share, in this case target should some kind of share token
 */
export enum RequesterType {
  Portfolio = 'portfolio',
}

export enum DataShareScopeView {
  Insights = 'insights',
  Downloads = 'downloads',
  Survey = 'survey',
  Messaging = 'messaging'
}

export const getScopeViews = () => Object.values(DataShareScopeView);

export enum DataScopeAccess {
  Full = 'full',
  Partial = 'partial',
  None = 'none'
}

export interface DataScopeSurvey<T = Types.ObjectId> {
  /** Start date applied to survey effectiveDate **/
  startDate?: Date;
  /** End date of scope, that should be applied to survey effectiveDate **/
  endDate?: Date;
  /** Scope restriction **/
  scope?: Partial<Scope<T>>;

  /**
   * These really represents views or features,
   * rather than any kind of restriction
   **/
  views?: DataShareScopeView[];

  /**
   * Access to ensure only use correctly processed
   * dataScope that have explicit access type defined
  **/
  access?: DataScopeAccess;
}

/**
 * Wrapper around default combined scope to ensure only use correctly processed
 * dataScope that have explicit access type defined.
 *
 * Eventually should be moved to dataShare model through migration
 **/
export interface CombinedDataScopeAccess<T = Types.ObjectId> extends DataScopeSurvey<T> {
  access: DataScopeAccess;
  views: DataShareScopeView[];
}


/**
 * Represent the data share scope.
 *
 * Union of filters that applied when data lookups are done
 *
 * @Example
 * For initiativeId and children initiativeIds
 * Find all surveys that are within startDate and endDate
 * and have utrs within these scopes
 *
 * At some point it could also support sharing just specific UTRs directly
 *
 */
export interface DataScope<T = Types.ObjectId> {
  survey?: DataScopeSurvey<T>;
}

export interface DataShareCreate<T = Types.ObjectId> {

  /**
   * Custom title of request message to target organization
   * Should be limited to as short title, limited to 255 characters
   */
  title: string,

  /** Custom content that provided additional context */
  content?: string,

  /** Represent the source data initiative, aka Organization, RootInitiative */
  initiativeId: T;

  /** Represents requester entity that can now use shared data */
  requesterId: T;

  /**
   * What requesterId is referencing:
   * - Portfolio (PT)
   * - Company, Financial Organization (CTL or CT) etc.
   * - Share Token etc.
   */
  requesterType: RequesterType;

  /**
   * Represent restriction on the share request where initiative cannot modify
   * it directly due to applied restriction.
   *
   * Example is sponsorship of the subscription, that allow the Sponsor to access
   * the data
   */
  restriction?: Restriction;

  /**
   * Constrains of the data that is shared through this request
   *
   * If dataScope is not set, it implies there are no constrain
   * and all **published** data are available
   */
  dataScope?: DataScope;


  /**
   * When share request was accepted
   */
  acceptedDate?: Date;

  /**
   * Represent the expiry date of the share request
   * After this period, share request should no longer be valid
   *
   * If end date is not available, then share request never expire and will be
   * valid until manually revoked.
   */
  expiredDate?: Date;

  /**
   * Revoked represents when this share request was revoked
   */
  revokedDated?: Date;

  /**
   * DeletedDate represents when this share request was deleted
   */
  deletedDate?: Date;
}

export type DataShareUpdate = Pick<DataShareCreate, 'dataScope'>;

export enum DataShareStatus {
  Pending = 'pending',
  Accepted = 'accepted',
  Expired = 'expired',
  Revoked = 'revoked',
  Deleted = 'deleted',
}

export interface DataSharePlain<T = Types.ObjectId> extends DataShareCreate<T> {
  _id: Types.ObjectId;
  created: Date;
  status: DataShareStatus
}

export interface DataShareWithRequester extends DataSharePlain {
  requester: RequesterDetails
}

/**
 * Generate status of the Share Request based on available properties.
 *
 * As it relies on multiple Date fields, it would require a lot of coordination
 * to keep the status saved in database in sync. For now we generate when needed
 *
 * Priority of checks are important here as multiple status could be active
 * at the same time.
 */
export const getShareStatus = (dataShare: DataSharePlain): DataShareStatus => {

  if (dataShare.deletedDate) {
    return DataShareStatus.Deleted;
  }

  if (dataShare.revokedDated) {
    return DataShareStatus.Revoked;
  }

  if (dataShare.expiredDate && dataShare.expiredDate <= new Date()) {
    return DataShareStatus.Expired;
  }

  if (dataShare.acceptedDate) {
    return DataShareStatus.Accepted;
  }

  return DataShareStatus.Pending
}

interface DataSharePlainWithInitiative extends DataSharePlain {
  initiative?: InitiativeMin;
}

export type DataShareModel = HydratedDocument<DataSharePlainWithInitiative>;

/**
 * Definitions of the Mongoose models
 */
const RestrictionSchema = new Schema<Restriction, Model<Restriction>, Restriction>({
  type: {
    type: Schema.Types.String,
    enum: Object.values(RestrictionType),
    required: true,
    trim: true
  },
  endDate: Schema.Types.Date,
  message: { type: Schema.Types.String, required: false, trim: true },
  data: Schema.Types.Mixed,
}, { _id: false });

const DataScopeSurveySchema = new Schema( {
  startDate: Schema.Types.Date,
  endDate: Schema.Types.Date,
  scope: { type: ScopeSchema, required: false },
  views: {
    type: [Schema.Types.String],
    enum: getScopeViews(),
  },
  access: {
    type: Schema.Types.String,
    enum: DataScopeAccess,
  }
},{ _id: false });

export const DataScopeSchema = new Schema<DataScope, Model<DataScope>, DataScope>({
  survey: {
    type: DataScopeSurveySchema,
    required: false
  }
}, { _id: false });

const DataShareSchema = new Schema<DataShareModel, Model<DataShareModel>, DataShareModel>({

  title: { type: Schema.Types.String, required: true, trim: true, max: 160 },
  content: { type: Schema.Types.String, required: false, trim: true, max: 255 },

  /** Represents entity that want to use shared data */
  requesterId: { type: Schema.Types.ObjectId, required: true },
  requesterType: {
    type: Schema.Types.String,
    enum: Object.values(RequesterType),
    required: true,
  },

  restriction: {
    type: RestrictionSchema,
    required: false,
  },
  dataScope: {
    type: DataScopeSchema,
    required: false
  },
  // Owner of the share
  initiativeId: {
    type: Schema.Types.ObjectId,
    required: true,
  },
  // Dates
  acceptedDate: Schema.Types.Date,
  expiredDate: Schema.Types.Date,
  revokedDated: Schema.Types.Date,
  deletedDate: Schema.Types.Date,
  created: { type: Schema.Types.Date, default: () => new Date() },
}, { collection: 'data-share' });

DataShareSchema.virtual('initiative', {
  ref: 'Initiative', // The model to use
  localField: 'initiativeId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

DataShareSchema.index({ initiativeId: 1 });
DataShareSchema.index({ requesterId: 1, requesterType: 1 });

const DataShare = model<DataShareModel>('DataShare', DataShareSchema);

export default DataShare;
