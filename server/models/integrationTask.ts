/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { model, Model, Schema, Types } from "mongoose";

export interface IntegrationTaskCreate<D, T = Types.ObjectId> {
  name: string;
  type: string;
  subType: string;
  connectionId: T;
  initiativeId: T;
  data : D,
}

export interface IntegrationTaskPlain<D, T = Types.ObjectId> extends IntegrationTaskCreate<D, T> {
  _id: T;
  created?: string;
}

const IntegrationTaskSchema = new Schema<IntegrationTaskModel>({
  name: Schema.Types.String,
  type: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    enum: ['workiva']
  },
  subType: { type: Schema.Types.String, trim: true, lowercase: true },
  connectionId: { type: Schema.Types.ObjectId, required: true },
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  data: Schema.Types.Mixed,
  created: { type: Date, default: Date.now },
}, { collection: 'integration-tasks' });

export interface IntegrationTaskModel<D = any> extends IntegrationTaskPlain<D> {
  _id: Types.ObjectId
}

const IntegrationTask: Model<IntegrationTaskModel> = model('IntegrationTask', IntegrationTaskSchema);

export default IntegrationTask;
