import { Document, model, Model, Schema } from 'mongoose';
import { ObjectId } from 'bson';
import { AggregationUniversalTrackerPlain } from '../service/utr/aggregation/aggregatorDataService';
import { UniversalTrackerPlain } from './universalTracker';
import { ValueStages } from '../service/aggregate/stages/ValueStage';
import { SurveyType, BASED_SURVEY_TYPES } from './survey';
import { DataPeriods } from '../service/utr/constants';
import type { Scorecard } from '../service/scorecard/ScorecardFactory';
import type { InitiativePlain } from './initiative';
import type { IntegrationData } from '../service/integration/IntegrationProvider';
import { EditorState } from '../types/editorState';
import { UniversalTrackerValuePlain } from './universalTrackerValue';
import { AggregatorValueFields } from '../service/utr/aggregation/utrTypeAggregator';
import { LedgerUniversalTrackerValuePlain } from './ledgerUniversalTrackerValue';

export enum UtrvFilter {
  /** @deprecated **/
  All = 'all',
  Verified = 'verified',
  Assured = 'assured',
  AllAnswered = 'allAnswered',
}

export enum SurveyFilter {
  All = 'all',
  Completed = 'completed',
}

export enum PrivacyFilter {
  All = 'all',
  Public = 'public',
}

export interface ToggleFilter {
  enabled: boolean;
}

export enum TimeFrameType {
  TwelveMonths = '12-months',
  SixMonths = '6-months',
  ThreeMonths = '3-months',
  OneMonth = '1-month',
  AllTime = 'all-time',
  Custom = 'custom',
}

export interface TimeFrameFilter {
  type: TimeFrameType;
  startDate?: Date;
  endDate?: Date;
}

export type DashboardSurveyType = SurveyType.Default | SurveyType.Aggregation;

interface BaseFilters {
  utrv: UtrvFilter;
  survey: SurveyFilter;
  privacy: PrivacyFilter;
  timeFrame?: TimeFrameFilter;
  surveyType?: DashboardSurveyType;
  period?: DataPeriods;
  // Toggle filters are optional as they were added later without migration
  initiativeInfo?: ToggleFilter;
  sdgContribution?: ToggleFilter;
}

interface CTFilters extends BaseFilters {
  initiativeIds?: ObjectId[];
  baselinesTargets?: ToggleFilter;
  shareWithSubsidiaries?: ToggleFilter;
}

interface PTFilters extends BaseFilters {
  displayAsDefault?: ToggleFilter;
}

export type Filters = CTFilters & PTFilters;

interface UtrVariable {
  code: string;
  valueListCode?: string;
  groupCode?: string;
  subGroupCode?: string;
  /**
   * Integration service provider code
   */
  integrationCode?: string;
}

export interface UtrVariables {
  [key: string]: UtrVariable;
}

export interface GridSize {
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number | undefined;
  maxW?: number | undefined;
  minH?: number | undefined;
  maxH?: number | undefined;
}

export enum InsightDashboardItemType {
  Chart = 'chart',
  /**@deprecated Free Text supports Rich Text already. */
  Headline = 'headline',
  Text = 'text',
  SDGTracker = 'sdg-tracker',
  Table = 'table',
  Media = 'media',
  SDGContributionChart = 'sdg-contribution-chart',
  Integration = 'integration',
  Space = 'space',
}

export enum ChartSubType {
  Line = 'line',
  SingleValue = 'singleValue',
  SparkLine = 'sparkLine',
  Pie = 'pie', // donut
  FullPie = 'fullPie',
  PieWithLegend = 'pieWithLegend',
  Column = 'column',
  Bar = 'bar',
  Table = 'table',
  Stacked = 'stacked',
}

export enum TextSubType {
  Free = 'free',
  Metric = 'metric',
}

// extendable for future types
export type SubType = ChartSubType | TextSubType;

export enum CalculationType {
  Sum = 'sum',
  Formula = 'formula',
  Stages = 'stages',
  Percentage = 'percentage',
}

export enum ValueRole {
  Annotation = 'annotation',
  Style = 'style',
}

type Tooltip = { formula: string };

export interface Value {
  name: string;
  formula?: string;
  /**
   * Stages are not supported on single value or table widgets
   **/
  stages?: ValueStages;
  role?: ValueRole;
  decimalPlaces?: number;
  options?: { style: string; tooltip: Tooltip };
}

interface Calculation {
  type: CalculationType;
  values: Value[];
  headers?: ({ name: string } | { role: ValueRole })[];
}

interface Note {
  prefix?: string;
  value?: string;
  valueUnit?: string;
  postfix?: string;
}

interface DashboardItemConfig {
  hiddenColumns?: string[];
  hideQuestionReference?: boolean;
  deleteDisabled?: boolean;
  editDisabled?: boolean;
  hideBorder?: boolean;
  duplicateDisabled?: boolean;
}

export interface MediaFile {
  documentId: ObjectId;
  ratio?: number;
}

export interface PresentingMediaFile extends MediaFile {
  url: string;
  type: string;
  name: string;
}

export interface InsightDashboardItem<T = ObjectId, F = MediaFile> {
  _id: T;
  title?: string;
  variables?: UtrVariables;
  gridSize: GridSize;
  type?: InsightDashboardItemType;
  text?: string;
  editorState?: EditorState;
  subType?: SubType;
  unitText?: string;
  calculation?: Calculation;
  note?: Note;
  icon?: string;
  /** Only used by static dashboard to indicate that this is default and cannot be removed **/
  isDefault?: boolean;
  config?: DashboardItemConfig;
  files?: F[];
}

export interface InsightDashboardPlain<T = ObjectId> {
  _id: T;
  creatorId: T;
  initiativeId: T;
  title: string;
  filters: Filters;
  items: InsightDashboardItem<T>[];
  share?: SharingItem[];
  type: InsightDashboardType;
}

export type StaticDashboard = Omit<InsightDashboardPlain, '_id' | 'creatorId' | 'initiativeId'>;

export enum InsightDashboardType {
  Custom = 'custom',
  Overview = 'overview',
  Environmental = 'environmental',
  Social = 'social',
  Governance = 'governance',
  SgxEnvironmental = 'sgx_environmental',
  SgxSocial = 'sgx_social',
  SgxGovernance = 'sgx_governance',
  WFNTemplate = 'wfn_template',
}

export const INSIGHT_TEMPLATE_DASHBOARD_TYPES = [InsightDashboardType.WFNTemplate];

export type InsightDashboardModel = Document<ObjectId> & InsightDashboardPlain;

export interface SharingItem {
  enabled: boolean;
  token: string;
}

// Combination type of aggregated and non-aggregated utrv
export type HistoricalUtrv = Pick<
  UniversalTrackerValuePlain,
  | 'universalTrackerId'
  | 'type'
  | 'effectiveDate'
  | 'value'
  | 'valueData'
  | 'assuranceStatus'
  | 'compositeData'
  | 'valueType'
  | 'stakeholders'
  | 'period'
> &
  Partial<Pick<AggregatorValueFields, 'aggregationCount' | 'disaggregation'>> & {
    _id?: ObjectId | string;
    initiativeId?: ObjectId;
    status?: string;
    lastUpdated?: Date;
    surveyType?: SurveyType;
    initiative?: {
      _id: string | ObjectId;
      name: string;
    };
    ledgerUniversalTrackerValue?: LedgerUniversalTrackerValuePlain;
  };

export interface HistoricalUtrs {
  utr: AggregationUniversalTrackerPlain | UniversalTrackerPlain;
  utrvs: HistoricalUtrv[];
}

export interface ExtendedDashboard
  extends Pick<
    InsightDashboardModel,
    'creatorId' | 'initiativeId' | 'title' | 'filters' | 'items' | '_id' | 'share' | 'type'
  > {
  utrsData: HistoricalUtrs[];
  scorecard?: {
    scorecard: Scorecard;
    initiative: InitiativePlain;
  };
  integrationsData?: IntegrationData;
}

export type InsightDashboardItemCreate = Pick<
  InsightDashboardItem,
  'title' | 'type' | 'text' | 'subType' | 'icon' | 'calculation' | 'variables' | 'config'
> & {
  gridSize: Partial<GridSize>;
  utrVariables?: UtrVariable[];
};

const ToggleFilterSchema = new Schema<ToggleFilter>(
  {
    enabled: { type: Boolean, default: true },
  },
  { _id: false }
);

const TimeFrameFilterSchema = new Schema<TimeFrameFilter>(
  {
    type: { type: Schema.Types.String, default: TimeFrameType.TwelveMonths, enum: Object.values(TimeFrameType) },
    startDate: { type: Schema.Types.Date },
    endDate: { type: Schema.Types.Date },
  },
  { _id: false }
);

export const getDefaultFilters = (): Filters => {
  return {
    utrv: UtrvFilter.Verified,
    survey: SurveyFilter.All,
    privacy: PrivacyFilter.All,
    timeFrame: {
      type: TimeFrameType.TwelveMonths,
    },
    surveyType: SurveyType.Default,
    period: DataPeriods.Yearly,
    initiativeInfo: {
      enabled: false,
    },
    sdgContribution: {
      enabled: false,
    },
    baselinesTargets: {
      enabled: false,
    },
    shareWithSubsidiaries: {
      enabled: false,
    },
  };
};

const FilterSchema = new Schema<Filters>(
  {
    utrv: {
      type: Schema.Types.String,
      default: UtrvFilter.Verified,
      enum: Object.values(UtrvFilter),
    },
    survey: {
      type: Schema.Types.String,
      default: SurveyFilter.All,
      enum: Object.values(SurveyFilter),
    },
    privacy: {
      type: Schema.Types.String,
      default: PrivacyFilter.All,
      enum: Object.values(PrivacyFilter),
    },
    timeFrame: {
      type: TimeFrameFilterSchema,
      default: {
        type: TimeFrameType.TwelveMonths,
      },
    },
    surveyType: {
      type: Schema.Types.String,
      default: SurveyType.Default,
      enum: BASED_SURVEY_TYPES,
    },
    period: {
      type: Schema.Types.String,
      default: DataPeriods.Yearly,
      enum: Object.values(DataPeriods),
    },
    initiativeInfo: {
      type: ToggleFilterSchema,
      default: { enabled: true },
    },
    sdgContribution: {
      type: ToggleFilterSchema,
      default: { enabled: false },
    },
    baselinesTargets: {
      type: ToggleFilterSchema,
      default: { enabled: true },
    },
    shareWithSubsidiaries: {
      type: ToggleFilterSchema,
      default: { enabled: false },
    },
    displayAsDefault: {
      type: ToggleFilterSchema,
    },
    initiativeIds: { type: [Schema.Types.ObjectId], default: undefined },
  },
  { _id: false }
);

const GridSizeSchema = new Schema(
  {
    x: { type: Schema.Types.Number, required: true },
    y: { type: Schema.Types.Number, required: true },
    w: { type: Schema.Types.Number, required: true },
    h: { type: Schema.Types.Number, required: true },
    minW: { type: Schema.Types.Number },
    maxW: { type: Schema.Types.Number },
    minH: { type: Schema.Types.Number },
    maxH: { type: Schema.Types.Number },
  },
  { _id: false }
);

const ValueSchema = new Schema<Value>(
  {
    name: { type: Schema.Types.String, required: true },
    formula: { type: Schema.Types.String },
    role: {
      type: Schema.Types.String,
      enum: Object.values(ValueRole),
    },
    decimalPlaces: { type: Schema.Types.Number },
    options: { style: { type: Schema.Types.String }, tooltip: { formula: Schema.Types.String } },
  },
  { _id: false }
);

const HeaderSchema = new Schema<Calculation['headers']>(
  {
    name: Schema.Types.String,
    role: {
      type: Schema.Types.String,
      enum: Object.values(ValueRole),
    },
  },
  { _id: false }
);

const CalculationSchema = new Schema<Calculation>(
  {
    type: {
      type: Schema.Types.String,
      required: true,
      enum: Object.values(CalculationType),
    },
    values: {
      type: [ValueSchema],
    },
    headers: {
      type: [HeaderSchema],
      default: undefined,
    },
  },
  { _id: false }
);

const NoteSchema = new Schema<Note>(
  {
    prefix: {
      type: Schema.Types.String,
    },
    value: {
      type: Schema.Types.String,
    },
    valueUnit: {
      type: Schema.Types.String,
    },
    postfix: {
      type: Schema.Types.String,
    },
  },
  { _id: false }
);

const ConfigSchema = new Schema<DashboardItemConfig>(
  {
    hiddenColumns: {
      type: [Schema.Types.String],
      required: false,
    },
    hideQuestionReference: {
      type: Schema.Types.Boolean,
    },
    editDisabled: {
      type: Schema.Types.Boolean,
    },
    deleteDisabled: {
      type: Schema.Types.Boolean,
    },
    hideBorder: {
      type: Schema.Types.Boolean,
    },
    duplicateDisabled: {
      type: Schema.Types.Boolean,
    }
  },
  { _id: false }
);

const MediaFileSchema = new Schema<MediaFile>(
  {
    documentId: { type: Schema.Types.ObjectId, required: true },
    ratio: { type: Schema.Types.Number }, // TODO: require for some types.
  },
  { _id: false }
);

const InsightDashboardItemSchema = new Schema<InsightDashboardItem>({
  title: { type: Schema.Types.String, trim: true },
  variables: {
    type: Schema.Types.Mixed,
    of: {
      type: {
        code: {
          type: Schema.Types.String,
          required: true,
        },
        valueListCode: {
          type: Schema.Types.String,
          required: false,
        },
        groupCode: {
          type: Schema.Types.String,
          required: false,
        },
        subGroupCode: {
          type: Schema.Types.String,
          required: false,
        },
        integrationCode: {
          type: Schema.Types.String,
          required: false,
        },
      },
    },
    required: function (this: InsightDashboardItem) {
      return (
        !this.type ||
        this.type === InsightDashboardItemType.Chart ||
        this.type === InsightDashboardItemType.SDGTracker ||
        this.subType === TextSubType.Metric
      );
    },
  },
  gridSize: GridSizeSchema,
  type: {
    type: Schema.Types.String,
    default: InsightDashboardItemType.Chart,
    enum: Object.values(InsightDashboardItemType),
  },
  text: {
    type: Schema.Types.String,
    trim: true,
    required: function (this: InsightDashboardItem) {
      return (
        (this.type === InsightDashboardItemType.Headline || this.type === InsightDashboardItemType.Text) &&
        this.subType !== TextSubType.Metric
      );
    },
  },
  editorState: Schema.Types.Mixed,
  subType: {
    type: Schema.Types.String,
    default: function (this: InsightDashboardItem) {
      if (this.type === InsightDashboardItemType.Chart || this.type === InsightDashboardItemType.SDGTracker) {
        return ChartSubType.Line;
      }
      return undefined;
    },
    enum: [...Object.values(ChartSubType), ...Object.values(TextSubType)],
  },
  calculation: CalculationSchema,
  unitText: {
    type: Schema.Types.String,
  },
  note: NoteSchema,
  icon: {
    type: Schema.Types.String,
    // avoid storing Integration's logo on update, always get it from Integration's provider when populate dashboard
    set: function (this: InsightDashboardItem, value: string) {
      return this.type === InsightDashboardItemType.Integration ? undefined : value;
    },
  },
  config: ConfigSchema,
  files: { type: [MediaFileSchema], default: undefined },
});

export const SHARING_TOKEN_LENGTH = 30;

const SharingItemSchema = new Schema<SharingItem>(
  {
    enabled: { type: Schema.Types.Boolean, required: true },
    token: { type: Schema.Types.String, required: true, trim: true, minlength: SHARING_TOKEN_LENGTH },
  },
  { _id: false }
);

const InsightDashboardSchema = new Schema<InsightDashboardModel>(
  {
    creatorId: Schema.Types.ObjectId,
    initiativeId: { type: Schema.Types.ObjectId, required: true },
    title: { type: Schema.Types.String, required: true, trim: true },
    filters: {
      type: FilterSchema,
      default: { utrv: UtrvFilter.Verified, survey: SurveyFilter.All, privacy: PrivacyFilter.All },
    },
    items: { type: [InsightDashboardItemSchema], default: () => [] },
    share: { type: [SharingItemSchema] },
    type: {
      type: Schema.Types.String,
      default: InsightDashboardType.Custom,
      enum: Object.values(InsightDashboardType),
    },
  },
  { collection: 'insight-dashboards', toJSON: { virtuals: true }, toObject: { virtuals: true } }
);

export const InsightDashboard: Model<InsightDashboardModel> = model('InsightDashboard', InsightDashboardSchema);
