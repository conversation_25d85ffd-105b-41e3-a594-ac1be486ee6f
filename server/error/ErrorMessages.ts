/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

// https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html#Authentication_and_Error_Messages
export enum UserErrorMessages {
  LoginFailedServiceAccount = 'Login failed. Wrong clientId or clientSecret.',
  NotActive = 'User is not activated',
  EmailExists = 'This e-mail is already registered.',
  FailToCreate = 'There was a problem creating this account. Please contact our customer services team.',
  ActivationTokenNotValid = 'Activation token is not valid. Please contact our customer services.',
  ActivationTokenCouldNotSave = 'There was a problem saving your new password. Please try again.',
  PassResetRequestGeneric = 'If that email address is in our database, we will send you an email to reset your password.',
  PassResetMissingToken = 'Please provide valid password reset token',
  PassResetNotValid = 'Please re-request a password change. This link is no longer valid.',
  NotFound = 'User not found',

  RegistrationMustUseSSO = 'Account already exists through Single Sign-on. Please log in.',
  UserExists = 'User account already exists, please login to continue.',
}

export enum InitiativeErrorMessages {
  RootAccess = `You do not have a access to this organisation`,
}

export enum HttpErrorMessages {
  RateLimit = 'Too Many Requests',
  PermissionDenied = 'Not Permitted',
  BadRequest = 'Bad Request',
}


export const GENERIC_ERROR = 'There has been a problem completing your request. Try again later.';


export enum GenericError {
  Basic = 'There was a problem completing your request.',
}

export enum AssuranceErrorMessages {
  SurveyHasAssurance = 'Survey already has an active assurance portfolio.',
  UpdateNotAllowed = 'Assurance portfolio cannot be updated',
}


export enum SurveyErrorMessages {
  DownloadNoQuestions = 'No questions available for download'
}

export enum RatingsErrorMessages {
  NotPermissionsAdmin = 'You do not have permissions to manage ratings',
}

export enum SurveyTemplateErrorMessages {
  InvalidInitiative = 'Invalid initiativeId',
  Unauthorized = 'User does not have access to reporting company',
}


export enum DataShareErrorMessages {
  NoDataShare = 'You do not have active data shares to process this request',
}
